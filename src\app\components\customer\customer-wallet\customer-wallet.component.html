<div class="row">
    <div class="col-md-12 col-sm-12">
        <div class="panel">
            <div class="panel-heading">
                <div class="displayflex">
                    <button (click)="customerDetailOpen()" class="btn btn-secondary backbtn" data-placement="bottom"
                        data-toggle="tooltip" title="Go to Customer Details" type="button">
                        <i class="fa fa-arrow-circle-left" style="color: #f7b206 !important; font-size: 28px"></i>
                    </button>
                    <h3 class="panel-title">
                        {{ customerLedgerDetailData?.title }}
                        {{ customerLedgerDetailData?.firstname }}
                        {{ customerLedgerDetailData?.lastname }}
                        Wallet
                    </h3>
                </div>
                <div class="right">
                    <button class="btn refreshbtn" type="reset" (click)="addWalletIncustomer()">
                        <i class="fa fa-refresh"></i>
                    </button>
                    <button aria-controls="customerWallet" aria-expanded="false" class="btn-toggle-collapse"
                        data-target="#customerWallet" data-toggle="collapse" type="button">
                        <i class="fa fa-minus-circle"></i>
                    </button>
                </div>
            </div>
            <div class="panel-collapse collapse in" id="customerWallet">
                <div class="panel-body table-responsive">
                    <div class="card" style="display: flex; justify-content: center; text-align: center">
                        <p-card [style]="{ width: '360px' }">
                            <ng-template pTemplate="header">
                                <h1>Wallet Balance</h1>
                            </ng-template>
                            <p-badge *ngIf="getWallatData.customerWalletDetails >= 0"
                                [value]="currency + ' ' + (getWallatData.customerWalletDetails | number: '1.2-2')"
                                size="xlarge" severity="success">
                            </p-badge>
                            <p-badge *ngIf="getWallatData.customerWalletDetails < 0"
                                [value]="currency + ' ' + (getWallatData.customerWalletDetails | number: '1.2-2')"
                                size="xlarge" severity="danger">
                            </p-badge>
                            <ng-template pTemplate="footer">
                                <div *ngIf="this.WalletAmount > 0 && withdrawalAmountAccess"
                                    style="margin-bottom: 2rem">
                                    <button (click)="
                      withdrawalAmountModel('withdrawalAmountModal', customerId, this.WalletAmount)
                    " class="btn btn-primary" id="submit" type="submit">
                                        <i class="fa fa-check-circle"></i>
                                        Withdrawal Amount
                                    </button>
                                </div>
                            </ng-template>
                        </p-card>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<app-customer-withdrawalmodal *ngIf="displayDialogWithDraw" (selectedStaffChange)="selectedStaffChange($event)"
    (closeSelectStaff)="closeSelectStaff()" (walletCustomerID)="addWalletIncustomer($event)"
    [wCustID]="wCustID"></app-customer-withdrawalmodal>