<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ customerLedgerDetailData?.title }}
            {{ customerLedgerDetailData?.firstname }}
            {{ customerLedgerDetailData?.lastname }} Payment Details
          </h3>
        </div>
        <div class="right">
          <button
            class="btn refreshbtn"
            type="reset"
            (click)="openCustomersPaymentData(customerId, '')"
          >
            <i class="fa fa-refresh"></i>
          </button>
          <button
            aria-controls="paymentPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#paymentPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
        <button
          *ngIf="recordPaymentAccess"
          [disabled]="customerLedgerDetailData?.status === 'Terminate' || isDisable"
          (click)="getCustomer()"
          class="btn btn-primary statusbtn"
          data-backdrop="static"
          data-keyboard="false"
          data-target="#recordPayment"
          data-title="Record Payment"
          data-toggle="modal"
          style="
            background-color: #f7b206 !important;
            font-size: 16px;
            padding: 3px 12px;
            margin-top: 10px;
          "
          type="submit"
        >
          Record Payment
        </button>
        <button
          *ngIf="recordPaymentAccess"
          [disabled]="customerLedgerDetailData?.status === 'Terminate' || isDisable"
          (click)="getFailedPayments()"
          class="btn btn-primary statusbtn"
          data-backdrop="static"
          data-keyboard="false"
          data-target="#recordPayment"
          data-title="Record Payment"
          data-toggle="modal"
          style="
            background-color: #f7b206 !important;
            font-size: 16px;
            padding: 3px 12px;
            margin-top: 10px;
          "
          type="submit"
        >
          Online Payment Audits
        </button>
      </div>
      <div class="panel-collapse collapse in" id="paymentPreCust">
        <div *ngIf="viewcustomerPaymentData?.length !== 0" class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Payment By</th>
                    <th>Receipt No</th>
                    <!-- <th>Reference No.</th> -->
                    <!-- <th>Document No.</th> -->
                    <th>Invoice Number</th>
                    <th>Paymode</th>
                    <!-- <th>Type</th> -->
                    <th>Pay Amount</th>
                    <th>TDS</th>
                    <th>ABBS</th>
                    <th>Source</th>
                    <th>Unsettled Amount</th>
                    <th>Bank Name</th>
                    <th>Cheque No.</th>
                    <th>Payment Date</th>
                    <th>File</th>
                    <th>Status</th>
                    <th>Remark</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of viewcustomerPaymentData
                        | paginate
                          : {
                              id: 'customerPayListpageData',
                              itemsPerPage: customerPaymentdataitemsPerPage,
                              currentPage: currentPagecustomerPaymentdata,
                              totalItems: customerPaymentdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ data.paymentBy }}</td>
                    <td>{{ data.creditdocumentno }}</td>
                    <td>{{ data.invoiceNumber }}</td>
                    <td>
                      <a
                        *ngIf="data.paymode == 'Cheque'"
                        style="color: #f7b206"
                        class="HoverEffect"
                        title="Payment Details"
                        (click)="openPaymentModal(data.id)"
                      >
                        {{ data.paymode }}
                      </a>
                      <span *ngIf="data.paymode !== 'Cheque'">
                        {{ data.paymode }}
                      </span>
                    </td>
                    <!-- <td>{{ data.type }}</td> -->
                    <td>
                      <span
                        (click)="openPaymentInvoiceModal('PaymentDetailModal', data.id)"
                        class="curson_pointer"
                        style="color: #f7b206"
                      >
                        <!-- {{ currency}} {{ data.amount | number : "1.2-2" }} -->
                        {{ data.amount | currency: currency : "symbol" : "1.2-2" }}
                      </span>
                    </td>
                    <td>{{ data.tdsamount | number: "1.2-2" }}</td>
                    <td>{{ data.abbsAmount | number: "1.2-2" }}</td>
                    <td>{{ data.onlinesource }}</td>
                    <!-- <td>{{ currency}} {{ data.unsettledAmount | number : "1.2-2" }}</td> -->
                    <td>{{ data.unsettledAmount | currency: currency : "symbol" : "1.2-2" }}</td>
                    <td>{{ data.bankName }}</td>
                    <td>{{ data.paydetails2 }}</td>
                    <!-- <td>{{ data.status }}</td> -->
                    <td>
                      <span *ngIf="data.paymentdate">
                        {{ data.paymentdate }}
                      </span>
                      <span *ngIf="!data.paymentdate">-</span>
                    </td>
                    <td>
                      <a
                        (click)="downloadInvoice(data.id, data.custId, data.filename)"
                        *ngIf="data.filename != null"
                        href="javascript:void(0)"
                        style="color: #28a745; font-size: 20px"
                        title="Download Invoice"
                      >
                        <img src="assets/img/pdf.png" style="width: 25px; height: 25px" />
                      </a>
                      <span *ngIf="data.filename == null"> - </span>
                    </td>
                    <td *ngIf="data.status === 'Fully Adjusted'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="data.status === 'advance'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="data.status === 'approved'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="data.status === 'Online Payment Pending'" style="text-align: center">
                      <span class="badge badge-success">
                        {{ "Payment Pending" | titlecase }}
                      </span>
                    </td>
                    <td
                      *ngIf="data.status === 'pending' && data.nextTeamHierarchyMappingId == null"
                    >
                      <span class="badge badge-info">
                        {{ "Collected" | titlecase }}
                      </span>
                    </td>
                    <td
                      *ngIf="data.status === 'pending' && data.nextTeamHierarchyMappingId != null"
                    >
                      <span class="badge badge-info">
                        {{ "Submitted" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="data.status === 'Partialy Adjusted'">
                      <span class="badge badge-success">
                        {{ "verified" | titlecase }}
                      </span>
                    </td>
                    <td *ngIf="data.status === 'rejected'">
                      <span class="badge badge-danger">
                        {{ data.status | titlecase }}
                      </span>
                    </td>
                    <td>{{ data.remarks }}</td>
                  </tr>
                </tbody>
              </table>
              <br />
              <div class="pagination_Dropdown">
                <pagination-controls
                  (pageChange)="pageChangedcustomerPaymentList($event)"
                  directionLinks="true"
                  id="customerPayListpageData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="TotalPaymentItemPerPage($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="viewcustomerPaymentData?.length === 0" class="panel-body table-responsive">
          Details are not available.
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Payment Modal Start -->
<!-- <div class="modal fade" id="paymentModal">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Cheque Details:</h4>
      </div>

      <div class="modal-body">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>amount</th>
              <th>chequedate</th>
              <th>chequeNo</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let cheque of chequeDetail">
              <td>{{ cheque.amount }}</td>
              <td>{{ cheque.chequedate }}</td>
              <td>{{ cheque.chequeNo }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="modal-footer">
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div> -->
<p-dialog
  header="Cheque Details:"
  [(visible)]="showChequeDetails"
  [modal]="true"
  [style]="{ width: '50%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  style="background: #f7b206 !important"
>
  <div class="modal-body">
    <table class="table table-hover">
      <thead>
        <tr>
          <th>Amount</th>
          <!-- <th>branch</th> -->
          <th>Cheque Date</th>
          <th>Cheque No</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let cheque of chequeDetail">
          <td>{{ cheque.amount }}</td>
          <!-- <td>{{ cheque.branch }}</td> -->
          <td>{{ cheque.chequedate }}</td>
          <td>{{ cheque.chequeNo }}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Modal footer -->
  <div class="modal-footer">
    <button class="btn btn-default" (click)="showChequeDetails = false" type="button">Close</button>
  </div>
</p-dialog>

<p-dialog
  header="Record Payment"
  [(visible)]="displayRecordPaymentDialog"
  [modal]="true"
  [style]="{ width: '90%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closePaymentForm()"
  style="background: #f7b206 !important"
>
  <div class="modal-body">
    <form [formGroup]="paymentFormGroup">
      <div class="row" style="margin-bottom: 20px">
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Customer*</label>
          <select class="form-control" disabled name="customerid" style="width: 100%">
            <option value="">
              {{ customerLedgerDetailData?.title }}
              {{ customerLedgerDetailData?.firstname }}
              {{ customerLedgerDetailData?.lastname }}
            </option>
          </select>
        </div>
        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12 mb-15">
          <label>Invoice*</label>
          <br />
          <div style="display: inline-block">
            <p-multiSelect
              placeholder="Select a Invoice"
              id="roles"
              [options]="invoiceList"
              formControlName="invoiceId"
              styleClass="disableDropdown"
              [disabled]="true"
              defaultLabel="Invoice"
              optionLabel="docnumber"
              optionValue="id"
              resetFilterOnHide="true"
              [ngClass]="{
                'is-invalid': submitted && paymentFormGroup.controls.invoiceId.errors
              }"
            ></p-multiSelect>
          </div>
          <button
            (click)="modalOpenInvoice(customerId)"
            class="btn btn-primary"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
          >
            <i class="fa fa-plus-square"></i>
          </button>

          <div
            *ngIf="submitted && paymentFormGroup.controls.invoiceId.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.invoiceId.errors.required"
              class="error text-danger"
            >
              Invoice is required.
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Payment Mode*</label>
          <p-dropdown
            (onChange)="selPayModeRecord($event)"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.paymode.errors
            }"
            [options]="paymentMode"
            [filter]="true"
            filterBy="text"
            formControlName="paymode"
            optionLabel="text"
            optionValue="value"
            placeholder="Select a Payment Mode"
          ></p-dropdown>
          <div
            *ngIf="submitted && paymentFormGroup.controls.paymode.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.paymode.errors.required"
              class="error text-danger"
            >
              Pay Mode is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Source {{ onlineSourceData?.length > 0 ? "*" : "" }}</label>
          <p-dropdown
            [disabled]="!(onlineSourceData?.length > 0)"
            [options]="onlineSourceData"
            [filter]="true"
            filterBy="text"
            optionLabel="text"
            optionValue="value"
            placeholder="Select a Payment Mode"
            formControlName="onlinesource"
            (onChange)="selPaySourceRecord($event)"
          ></p-dropdown>
          <div
            *ngIf="submitted && paymentFormGroup.controls.onlinesource.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.onlinesource.errors.required"
              class="error text-danger"
            >
              Source is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Amount*</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.amount.errors
            }"
            class="form-control"
            formControlName="amount"
            min="1"
            placeholder="Enter Amount"
            step=".01"
            type="number"
            customDecimal
            (keypress)="keypressId($event)"
            [readonly]="isShowInvoiceList"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.amount.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.required"
              class="error text-danger"
            >
              Amount is required.
            </div>
            <div
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.pattern"
              class="error text-danger"
            >
              Only numeric characters allowed.
            </div>
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.min"
            >
              Amount must be greater then 0.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Select File</label>
          <input
            (change)="onFileChange($event)"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.file.errors
            }"
            class="form-control"
            formControlName="file"
            type="file"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.file.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.file.errors.required"
              class="error text-danger"
            >
              file is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Cheque No.*</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.chequeno.errors
            }"
            class="form-control"
            formControlName="chequeno"
            min="1"
            placeholder="Enter Cheque No."
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.chequeno.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.chequeno.errors.required"
              class="error text-danger"
            >
              Cheque No. is required.
            </div>
            <div
              *ngIf="submitted && paymentFormGroup.controls.chequeno.errors.pattern"
              class="error text-danger"
            >
              Only numeric characters allowed.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>{{ chequeDateName }}*</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.chequedate.errors
            }"
            class="form-control"
            formControlName="chequedate"
            [placeholder]="'Enter' + chequeDateName"
            type="date"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.chequedate.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.chequedate.errors.required"
              class="error text-danger"
            >
              Cheque Date is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Source Bank*</label>
          <select
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.bankManagement.errors
            }"
            class="form-control"
            formControlName="bankManagement"
            style="width: 100%"
          >
            <option value="">Select Source Bank</option>
            <option *ngFor="let bank of bankDataList" value="{{ bank.id }}">
              {{ bank.bankname }} - {{ bank.accountnum }}
            </option>
          </select>

          <div
            *ngIf="submitted && paymentFormGroup.controls.bankManagement.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.bankManagement.errors.required"
              class="error text-danger"
            >
              Bank is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15" *ngIf="destinationbank">
          <label>Destination Bank*</label>
          <select
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.destinationBank.errors
            }"
            class="form-control"
            formControlName="destinationBank"
            style="width: 100%"
          >
            <option value="">Select Destination Bank</option>
            <option *ngFor="let bankDest of bankDestination" value="{{ bankDest.id }}">
              {{ bankDest.bankname }} - {{ bankDest.accountnum }}
            </option>
          </select>

          <div
            *ngIf="submitted && paymentFormGroup.controls.destinationBank.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.destinationBank.errors.required"
              class="error text-danger"
            >
              Bank Destination is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Branch</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.branch.errors
            }"
            class="form-control"
            formControlName="branch"
            placeholder="Enter Branch"
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.branch.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.branch.errors.required"
              class="error text-danger"
            >
              Branch is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Reference No.{{ paymentFormGroup.value.paymode !== null ? "*" : "" }}</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.referenceno.errors
            }"
            class="form-control"
            formControlName="referenceno"
            placeholder="Enter Reference No."
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.referenceno.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.referenceno.errors.required"
              class="error text-danger"
            >
              Reference No. is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Receipt No</label>
          <input
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.reciptNo.errors
            }"
            class="form-control"
            formControlName="reciptNo"
            placeholder="Enter Recipt No."
            type="text"
          />
          <div
            *ngIf="submitted && paymentFormGroup.controls.reciptNo.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.reciptNo.errors.required"
              class="error text-danger"
            >
              Recipt No. is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <span>TDS</span>
          <input
            class="form-control"
            formControlName="tdsAmount"
            placeholder="Please enter TDS amount"
            step=".01"
            type="number"
            [readonly]="isShowInvoiceList"
          />
        </div>

        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <span>ABBS</span>
          <input
            class="form-control"
            formControlName="abbsAmount"
            placeholder="Please enter ABBS amount"
            step=".01"
            type="number"
            [readonly]="isShowInvoiceList"
          />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
          <label>Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            placeholder="Enter Remark"
            rows="3"
          ></textarea>
          <div
            *ngIf="submitted && paymentFormGroup.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="submitted && paymentFormGroup.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="row">
    <div class="modal-body">
      <p-table [value]="selectedInvoice" [rows]="10" [paginator]="true" *ngIf="isShowInvoiceList">
        <ng-template pTemplate="header">
          <tr>
            <th>Doc Number</th>
            <th>Created BY</th>
            <th>Tax Amount</th>
            <th>Total Invoice</th>
            <th>Pending Amount</th>
            <th>Refundable Amount</th>
            <th>Amount</th>
            <th *ngIf="isDisplayConvertedAmount">Converted Amount</th>
            <th>TDS</th>
            <th>ABBS</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-data>
          <tr>
            <td>{{ data.docnumber }}</td>
            <td>{{ data.createdByName }}</td>
            <td>{{ data.tax }}</td>
            <td>{{ data.totalamount | currency: currency : "symbol" : "1.2-2" }}</td>
            <td *ngIf="data.adjustedAmount == null; else showPendingAmount">
              {{ data.totalamount | currency: currency : "symbol" : "1.2-2" }}
            </td>
            <ng-template #showPendingAmount>
              <td>
                {{ getPendingAmount(data) | currency: currency : "symbol" : "1.2-2" }}
              </td>
            </ng-template>

            <td>{{ data.refundAbleAmount | currency: currency : "symbol" : "1.2-2" }}</td>
            <td>{{ data.testamount | currency: currency : "symbol" : "1.2-2" }}</td>
            <td *ngIf="isDisplayConvertedAmount">
              {{ data.convertedAmount | currency: currency : "symbol" : "1.2-2" }}
            </td>
            <td>{{ data.tdsCheck }}</td>
            <td>{{ data.abbsCheck }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="submit"
        (click)="addPayment('')"
        [disabled]="!paymentFormGroup.valid"
        label="Add Payment"
        icon="pi pi-check-circle"
        class="btn btn-primary"
      >
        Add Payment
      </button>
      <button
        type="reset"
        (click)="closePaymentForm()"
        label="Close"
        icon="pi pi-times-circle"
        class="btn btn-danger"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Select Invoice"
  [(visible)]="displaySelectInvoiceDialog"
  [style]="{ width: '75%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <p-table #dt [value]="invoiceList" responsiveLayout="scroll" [(selection)]="selectedInvoice">
      <ng-template pTemplate="header">
        <tr>
          <th>
            <input
              (change)="checkUncheckAllInvoice()"
              [(ngModel)]="masterSelected"
              name="master-checkbox"
              type="checkbox"
            />
          </th>
          <th>Doc Number</th>
          <th>Created By</th>
          <th>Tax Amount</th>
          <th>Total Invoice</th>
          <th>Pending Amount</th>
          <th>Refundable Amount</th>
          <th>Amount</th>
          <th *ngIf="isDisplayConvertedAmount">Converted Amount</th>
          <th>TDS</th>
          <th>ABBS</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-invoice let-rowIndex="rowIndex">
        <tr>
          <td>
            <input
              [value]="invoice"
              (change)="isAllSelectedInvoice()"
              [(ngModel)]="invoice.isSelected"
              type="checkbox"
            />
          </td>
          <td>{{ invoice.docnumber }}</td>
          <td>{{ invoice.createdByName }}</td>
          <td>
            {{ invoice.tax }}
          </td>
          <td>{{ invoice.totalamount | currency: currency : "symbol" : "1.2-2" }}</td>
          <td>{{ getPendingAmount(invoice) | currency: currency : "symbol" : "1.2-2" }}</td>
          <td>{{ invoice.refundAbleAmount | currency: currency : "symbol" : "1.2-2" }}</td>
          <td>
            <input
              pInputText
              [(ngModel)]="invoice.testamount"
              type="number"
              class="small-input"
              (ngModelChange)="
                onSelectedInvoice($event, invoice, invoice.includeTds, invoice.includeAbbs)
              "
            />
          </td>
          <td *ngIf="isDisplayConvertedAmount">
            <input
              pInputText
              [(ngModel)]="invoice.convertedAmount"
              type="number"
              class="small-input"
              (ngModelChange)="onConvertedAmountChange($event, invoice)"
            />
          </td>
          <td>
            <div class="p-d-flex p-ai-center">
              <p-checkbox
                (onChange)="onChangeOFTDSTest($event, invoice)"
                [disabled]="!invoice.testamount"
              ></p-checkbox>
              &nbsp;
              <input
                pInputText
                [(ngModel)]="invoice.tdsCheck"
                [readonly]="!invoice.testamount"
                class="small-input"
              />
            </div>
          </td>
          <td>
            <div class="p-d-flex p-ai-center">
              <p-checkbox
                (onChange)="onChangeOFABBSTest($event, invoice)"
                [disabled]="!invoice.testamount"
              ></p-checkbox>
              &nbsp;
              <input
                pInputText
                class="small-input"
                [(ngModel)]="invoice.abbsCheck"
                [readonly]="!invoice.testamount"
              />
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        style="object-fit: cover; padding: 5px 8px"
        class="btn btn-primary"
        (click)="bindInvoice()"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button type="button" class="btn btn-danger" (click)="modalCloseInvoiceList()">Close</button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Online Payment Audits"
  [(visible)]="displayFailedPaymentDialog"
  [style]="{ width: '85%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeFailedPaymentForm()"
>
  <div class="modal-body">
    <p-table #dt [value]="viewcustomerFailedPaymentData" responsiveLayout="scroll">
      <ng-template pTemplate="header">
        <tr>
          <th>Reference No</th>

          <th>Transaction No</th>
          <th>Account Number</th>
          <th>Customer Username</th>
          <th>Payment Amount</th>
          <th>Status</th>
          <th>Gateway Status</th>
          <th>Failure reason</th>
          <th>Payment Date</th>
          <th>Merchant Name</th>
          <th>Transaction Date</th>
          <th>Payer Mobile Number</th>
          <th>Auto Payment Initiator</th>
          <th>Action</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-payment let-rowIndex="rowIndex">
        <tr>
          <td>
            {{ payment.orderId }}
          </td>

          <td>
            {{
              payment?.pgTransactionId && payment.pgTransactionId !== "null"
                ? payment.pgTransactionId
                : "-"
            }}
          </td>
          <td>
            {{
              payment?.accountNumber && payment.accountNumber !== "null"
                ? payment.accountNumber
                : "-"
            }}
          </td>
          <td>{{ payment.customerUsername }}</td>
          <td>{{ payment.payment }}</td>

          <td>
            <span [ngSwitch]="payment.status?.toUpperCase()">
              <span *ngSwitchCase="'PENDING'" class="badge badge-info">{{
                payment.status | titlecase
              }}</span>
              <span *ngSwitchCase="'INITIATE'" class="badge badge-info">{{
                payment.status | titlecase
              }}</span>
              <span *ngSwitchCase="'SUCCESS'" class="badge badge-success">{{
                payment.status | titlecase
              }}</span>
              <span *ngSwitchCase="'SUCCESSFUL'" class="badge badge-success">{{
                payment.status | titlecase
              }}</span>
              <span *ngSwitchCase="'FAILED'" class="badge badge-danger">{{
                payment.status | titlecase
              }}</span>
              <span *ngSwitchDefault class="badge badge-info">
                {{
                  payment.status == null ||
                  payment.status?.toLowerCase()?.trim() === "" ||
                  payment.status?.toLowerCase()?.trim() === "null"
                    ? "-"
                    : payment.status
                }}
              </span>
            </span>
          </td>

          <td>
            <span [ngSwitch]="payment.gatewayStatus?.toLowerCase()">
              <span *ngSwitchCase="'success'" class="badge badge-success">{{
                payment.gatewayStatus | titlecase
              }}</span>
              <span *ngSwitchCase="'successful'" class="badge badge-success">{{
                payment.gatewayStatus | titlecase
              }}</span>
              <span *ngSwitchCase="'initiate'" class="badge badge-info">{{
                payment.gatewayStatus | titlecase
              }}</span>
              <span *ngSwitchCase="'pending'" class="badge badge-info">{{
                payment.gatewayStatus | titlecase
              }}</span>
              <span *ngSwitchCase="'failed'" class="badge badge-danger">{{
                payment.gatewayStatus | titlecase
              }}</span>
              <span *ngSwitchDefault class="badge badge-info">
                {{
                  payment.gatewayStatus == null ||
                  payment.gatewayStatus?.toLowerCase()?.trim() === "" ||
                  payment.gatewayStatus?.toLowerCase()?.trim() === "null"
                    ? "-"
                    : payment.gatewayStatus
                }}
              </span>
            </span>
          </td>

          <td>
            <span *ngIf="payment.failureDescription; else noFailure">
              <span
                class="badge badge-warning"
                style="cursor: pointer"
                (click)="openFailureReason(payment.failureDescription)"
              >
                View
              </span>
            </span>
            <ng-template #noFailure>
              <span style="margin-right: 5px">-</span>
            </ng-template>
          </td>
          <td>{{ payment.paymentDate | date: "dd-MM-YYYY HH:mm:ss" }}</td>
          <td>{{ payment.merchantName }}</td>
          <td>{{ payment.transactionDate | date: "dd-MM-YYYY HH:mm:ss" }}</td>
          <td>
            {{ payment.payerMobileNumber }}
          </td>
          <td>
            {{ payment.autoPaymentInitiator }}
          </td>
          <td>
            <button
              class="approve-btn"
              id="retry"
              *ngIf="retryPaymentAccess"
              style="border: none; background: transparent; padding: 0; margin-right: 5px"
              [disabled]="
                payment.gatewayStatus?.toLowerCase() === 'success' ||
                payment.gatewayStatus?.toLowerCase() === 'successful'
              "
              type="button"
              title="Retry"
              (click)="retryPayment(payment.orderId)"
            >
              <img
                class="icon"
                style="width: 25px; height: 25px; margin-right: 3px"
                src="assets/img/refresh.png"
              />
            </button>
            <button
              class="approve-btn"
              id="retry"
              type="button"
              title="Manually Settlement"
              style="border: none; background: transparent; padding: 0"
              *ngIf="manuallySettlement"
              [disabled]="!(payment?.pgTransactionId == null || payment?.pgTransactionId == 'NA')"
              (click)="addToWallet(payment.orderId)"
            >
              <img
                class="icon"
                style="width: 25px; height: 25px; margin-right: 3px"
                src="assets/img/19_Promise-to-Pay_Y.png"
              />
            </button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</p-dialog>

<p-dialog
  header="Failure Reason"
  [(visible)]="failureReasonDialog"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeFailureReason()"
>
  <div class="modal-body">
    <div *ngIf="failureReason">
      <div class="failure-item">
        <strong>{{ failureReason }}</strong>
      </div>
    </div>
  </div>
</p-dialog>

<app-payment-amount-model
  *ngIf="displayInvoiceDetails"
  [paymentId]="paymentId"
  (closeParentCustt)="displayInvoiceDetails = false"
  dialogId="PaymentDetailModal"
></app-payment-amount-model>

<p-dialog
  header="Add Transaction No"
  [(visible)]="transModal"
  [modal]="true"
  [style]="{ width: '30%' }"
  [draggable]="false"
  (onHide)="transactionModal()"
  [resizable]="false"
>
  <div class="form-group">
    <label style="margin-top: 10px">Transaction No: </label>
    <input
      type="text"
      name="transactionNo"
      id="transactionNo"
      [(ngModel)]="transactionNo"
      placeholder="Enter Transaction No"
      class="form-control"
    />
  </div>
  <ng-template pTemplate="footer">
    <div class="btnGroup text-center">
      <button
        class="btn btn-primary btnStyle"
        (click)="ConfirmonTransactionNumber()"
        style="margin-right: 10px"
        [disabled]="!transactionNo"
      >
        Add To Wallet
      </button>
      <button class="btn btn-danger btnStyle" (click)="transactionModal()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>
