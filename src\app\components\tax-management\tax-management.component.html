<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Tax Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchtax"
            aria-expanded="false"
            aria-controls="searchtax"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchtax" class="panel-collapse collapse in">
        <div class="panel-body" *ngIf="listView">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchTaxName"
                class="form-control"
                placeholder="Enter Tax Name"
                (keydown.enter)="searchTax()"
              />
            </div>
            <div class="col-lg-3 col-md-3 marginTopSearchinput">
              <p-dropdown
                [options]="taxTypeAll"
                optionValue="value"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Tax Type"
                [(ngModel)]="searchTaxType"
                (keydown.enter)="searchTax()"
              ></p-dropdown>
              <!-- <select
              class="form-control"
              name="taxtype"
              [(ngModel)]="searchTaxType"
              id="taxtype"
              style="width: 100%;"
            >
              <option value="">
                Select Tax Type
              </option>
              <option
                value="{{ taxTypeProperty[i] }}"
                *ngFor="let taxtype of taxTypeAll; let i = index"
              >
                {{ taxtype }}
              </option>
            </select> -->
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button type="button" class="btn btn-primary" id="searchbtn" (click)="searchTax()">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchTax()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
        <div class="panel-body no-padding panel-udata">
          <div class="col-md-6 pcol" *ngIf="createAccess">
            <div class="dbox">
              <a (click)="createTax()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create Tax</h5>
              </a>
            </div>
          </div>
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="listTax()" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search Tax</h5>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12" *ngIf="listView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Tax</h3>
        <div class="right">
          <button type="button" (click)="getRefresh()">
            <i class="fa fa-refresh"></i></button
          >&nbsp;
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#listTax"
            aria-expanded="false"
            aria-controls="listTax"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="listTax" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>ISP Name</th>
                    <th *ngIf="deleteAccess || editAccess">Action</th>
                  </tr>
                </thead>
                <tbody *ngIf="taxListData?.taxlist?.length > 0">
                  <tr
                    *ngFor="
                      let tax of taxListData.taxlist
                        | paginate
                          : {
                              id: 'taxListData',
                              itemsPerPage: TaxListdataitemsPerPage,
                              currentPage: currentPageTaxListdata,
                              totalItems: TaxListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td class="curson_pointer" (click)="taxAllDetails(tax)" style="color: #f7b206">
                      {{ tax.name }}
                    </td>
                    <td>{{ tax.taxtype }}</td>
                    <td *ngIf="tax.status == 'Y'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="tax.status == 'N'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td>{{ tax.mvnoName }}</td>
                    <!-- <td class="discInfo" title="{{tax.desc}}">{{ tax.desc }}</td> -->
                    <td class="btnAction" *ngIf="deleteAccess || editAccess">
                      <a
                        id="edit-button"
                        type="button"
                        title="Edit"
                        href="javascript:void(0)"
                        *ngIf="editAccess"
                        (click)="editTax(tax.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        id="delete-button"
                        href="javascript:void(0)"
                        title="Delete"
                        *ngIf="deleteAccess"
                        (click)="deleteConfirmonTax(tax.id)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
                <tr *ngIf="taxListData?.taxlist?.length === 0">
                  <td>No Record Found.</td>
                </tr>
              </table>
              <div class="pagination_Dropdown" *ngIf="taxListData?.taxlist?.length > 0">
                <pagination-controls
                  id="taxListData"
                  maxSize="10"
                  directionLinks="true"
                  previousLabel=""
                  nextLabel=""
                  (pageChange)="pageChangedTaxList((currentPageTaxListdata = $event))"
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                    (onChange)="TotalItemPerPage($event)"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-12" *ngIf="createView">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isTaxEdit ? "Update" : "Create" }} Tax</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createTax"
            aria-expanded="false"
            aria-controls="createTax"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="createTax" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="panel-body">
            <form class="form-auth-small" [formGroup]="taxGroupForm">
              <!--    Tax Information   -->
              <fieldset style="margin-top: 0px">
                <legend>Tax Information</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                      <label>Tax Name*</label>
                      <input
                        id="taxName"
                        type="text"
                        class="form-control"
                        placeholder="Enter Tax Name"
                        formControlName="name"
                        [ngClass]="{
                          'is-invalid': submitted && taxGroupForm.controls.name.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && taxGroupForm.controls.name.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && taxGroupForm.controls.name.errors.required"
                        >
                          Tax name is required.
                        </div>
                        <div
                          class="position"
                          *ngIf="submitted && taxGroupForm.controls.name.errors?.cannotContainSpace"
                        >
                          <p class="error">White space are not allowed.</p>
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                      <label>Tax Type*</label>
                      <p-dropdown
                        [options]="taxTypeAll"
                        optionValue="value"
                        optionLabel="label"
                        filter="true"
                        filterBy="label"
                        placeholder="Select a Tax Type"
                        formControlName="taxtype"
                        (onChange)="getSelTaxType($event.value)"
                        [ngClass]="{
                          'is-invalid': submitted && taxGroupForm.controls.taxtype.errors
                        }"
                        [disabled]="isTaxEdit"
                      ></p-dropdown>

                      <!-- <select
                    *ngIf="!isTaxEdit"
                    class="form-control"
                    name="taxtype"
                    id="taxtype"
                    formControlName="taxtype"
                    style="width: 100%;"
                    (change)="getSelTaxType($event)"
                    [ngClass]="{
                      'is-invalid':
                        submitted && taxGroupForm.controls.taxtype.errors
                    }"
                  >
                    <option value="">
                      Select Tax Type
                    </option>
                    <option
                      value="{{ taxTypeProperty[i] }}"
                      *ngFor="let taxtype of taxTypeAll; let i = index"
                    >
                      {{ taxtype }}
                    </option>
                  </select>
                  <select
                    *ngIf="isTaxEdit"
                    class="form-control"
                    name="taxtype"
                    id="taxtype"
                    formControlName="taxtype"
                    style="width: 100%;"
                    (change)="getSelTaxType($event)"
                    [ngClass]="{
                      'is-invalid':
                        submitted && taxGroupForm.controls.taxtype.errors
                    }"
                    disabled
                  >
                    <option value="">
                      Select Tax Type
                    </option>
                    <option
                      value="{{ taxTypeProperty[i] }}"
                      *ngFor="let taxtype of taxTypeAll; let i = index"
                    >
                      {{ taxtype }}
                    </option>
                  </select> -->
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && taxGroupForm.controls.taxtype.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && taxGroupForm.controls.taxtype.errors.required"
                        >
                          Tax Type is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                      <label>Tax Status*</label>
                      <div>
                        <p-dropdown
                          [options]="statusOptions"
                          optionValue="value"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          placeholder="Select a Status"
                          formControlName="status"
                          [ngClass]="{
                            'is-invalid': submitted && taxGroupForm.controls.status.errors
                          }"
                        ></p-dropdown>
                      </div>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && taxGroupForm.controls.status.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && taxGroupForm.controls.status.errors.required"
                        >
                          Tax Status is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div>
                  <div class="row">
                    <!-- <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                      <label>Ledger ID</label>
                      <input
                        id="ledgerId"
                        type="text"
                        class="form-control"
                        placeholder="Enter Ledger ID"
                        formControlName="ledgerId"
                        [ngClass]="{
                          'is-invalid': submitted && taxGroupForm.controls.ledgerId.errors
                        }"
                      />
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && taxGroupForm.controls.ledgerId.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && taxGroupForm.controls.ledgerId.errors.required"
                        >
                          Ledger ID is required.
                        </div>
                      </div>
                      <br />
                    </div> -->
                    <div *ngIf="mvnoId === 1" class="col-lg-4 col-md-4 col-sm-6 col-xs-12 mb-15">
                      <label>{{ mvnoTitle }} List*</label>
                      <p-dropdown
                        id="mvnoId"
                        [disabled]="isTaxEdit"
                        [options]="commondropdownService.mvnoList"
                        filter="true"
                        filterBy="name"
                        formControlName="mvnoId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a mvno"
                      ></p-dropdown>
                      <div
                        *ngIf="submitted && taxGroupForm.controls.mvnoId.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && taxGroupForm.controls.mvnoId.errors.required"
                          class="error text-danger"
                        >
                          Mvno is required.
                        </div>
                      </div>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                      <label>Tax Description*</label>
                      <textarea
                        class="form-control"
                        placeholder="Enter Tax Description"
                        rows="3"
                        formControlName="desc"
                        name="desc"
                        [ngClass]="{
                          'is-invalid': submitted && taxGroupForm.controls.desc.errors
                        }"
                      ></textarea>
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && taxGroupForm.controls.desc.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && taxGroupForm.controls.desc.errors.required"
                        >
                          Tax Description is required.
                        </div>
                        <div
                          class="error text-danger"
                          *ngIf="submitted && taxGroupForm.controls.desc.errors.pattern"
                        >
                          Maximum 150 charecter required.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </fieldset>

              <!-- Tax Type SLAB Mapping  -->
              <div *ngIf="this.taxTypeSlab.enabled">
                <fieldset>
                  <legend>Tax Type Mapping</legend>
                  <div class="boxWhite">
                    <div class="row" [formGroup]="typeslebForm">
                      <div class="col-lg-3 col-md-3 col-sm-3">
                        <input
                          type="text"
                          class="form-control"
                          placeholder="Enter Name"
                          formControlName="name"
                          [ngClass]="{
                            'is-invalid': typeSlebSubmitted && typeslebForm.controls.name.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeSlebSubmitted && typeslebForm.controls.name.errors"
                        >
                          <div class="error text-danger">Name is required.</div>
                        </div>
                      </div>
                      <div class="col-lg-3 col-md-3 col-sm-3">
                        <input
                          class="form-control"
                          type="number"
                          min="1"
                          placeholder="Enter Range From"
                          formControlName="rangeFrom"
                          [ngClass]="{
                            'is-invalid':
                              typeSlebSubmitted && typeslebForm.controls.rangeFrom.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeSlebSubmitted && typeslebForm.controls.rangeFrom.errors"
                        >
                          <div class="error text-danger">Range From is required.</div>
                        </div>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <input
                          class="form-control"
                          type="number"
                          min="1"
                          placeholder="Enter Range UpTo"
                          formControlName="rangeUpTo"
                          [ngClass]="{
                            'is-invalid':
                              typeSlebSubmitted && typeslebForm.controls.rangeUpTo.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeSlebSubmitted && typeslebForm.controls.rangeUpTo.errors"
                        >
                          <div class="error text-danger">Range UpTo is required.</div>
                        </div>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <input
                          class="form-control"
                          type="number"
                          min="1"
                          placeholder="Enter Rate"
                          formControlName="rate"
                          [ngClass]="{
                            'is-invalid': typeSlebSubmitted && typeslebForm.controls.rate.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeSlebSubmitted && typeslebForm.controls.rate.errors"
                        >
                          <div class="error text-danger">Rate is required.</div>
                        </div>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">
                        <label>Is Before Discount </label>
                        <p-dropdown
                          [options]="beforeTax"
                          optionValue="value"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          placeholder="Select  before discount or not"
                          formControlName="beforeDiscount"
                        ></p-dropdown>
                        <br />
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-1">
                        <button
                          id="addAtt"
                          style="object-fit: cover; padding: 5px 8px"
                          class="btn btn-primary"
                          (click)="onAddTaxTypeSlabField()"
                        >
                          <i class="fa fa-plus-square" aria-hidden="true"></i>
                          Add
                        </button>
                      </div>
                    </div>
                    <table class="table coa-table" style="margin-top: 10px">
                      <thead>
                        <tr>
                          <th style="text-align: center; width: 20%">Name*</th>
                          <th style="text-align: center; width: 20%">Range From*</th>
                          <th style="text-align: center; width: 20%">Range UpTo*</th>
                          <th style="text-align: center; width: 20%">Rate*</th>
                          <th style="text-align: right; width: 20%; padding: 8px">Delete</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let row of taxTypeSlab.controls
                              | paginate
                                : {
                                    id: 'taxTypeSlabData',
                                    itemsPerPage: TaxSlabitemsPerPage,
                                    currentPage: currentPageTaxSlab,
                                    totalItems: TaxSlabtotalRecords
                                  };
                            let index = index
                          "
                        >
                          <td style="padding-left: 8px">
                            <input
                              type="text"
                              class="form-control"
                              placeholder="Enter Name"
                              [formControl]="row.get('name')"
                            />
                          </td>
                          <td>
                            <input
                              class="form-control"
                              type="number"
                              min="1"
                              placeholder="Enter Range From"
                              [formControl]="row.get('rangeFrom')"
                              [ngClass]="{
                                'is-invalid': submitted && row.get('rangeFrom').errors
                              }"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="submitted && row.get('rangeFrom').errors"
                            >
                              <div
                                class="error text-danger"
                                *ngIf="submitted && row.get('rangeFrom').errors.pattern"
                              >
                                Only numeric value allowed.
                              </div>
                            </div>
                          </td>
                          <td style="padding-left: 8px">
                            <input
                              class="form-control"
                              type="number"
                              min="1"
                              placeholder="Enter Range UpTo"
                              [formControl]="row.get('rangeUpTo')"
                              [ngClass]="{
                                'is-invalid': submitted && row.get('rangeUpTo').errors
                              }"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="submitted && row.get('rangeUpTo').errors"
                            >
                              <div
                                class="error text-danger"
                                *ngIf="submitted && row.get('rangeUpTo').errors.pattern"
                              >
                                Only numeric value allowed.
                              </div>
                            </div>
                          </td>
                          <td>
                            <input
                              class="form-control"
                              type="number"
                              min="1"
                              placeholder="Enter Rate"
                              [formControl]="row.get('rate')"
                              [ngClass]="{
                                'is-invalid': submitted && row.get('rate').errors
                              }"
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="submitted && row.get('rate').errors"
                            >
                              <div
                                class="error text-danger"
                                *ngIf="submitted && row.get('rate').errors.pattern"
                              >
                                Only numeric value allowed.
                              </div>
                            </div>
                          </td>
                          <td style="text-align: right">
                            <a
                              id="deleteAtt"
                              class="curson_pointer"
                              (click)="deleteConfirmonTaxTypeSlabField(index, row.get('id').value)"
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </a>
                          </td>
                        </tr>
                      </tbody>
                    </table>

                    <div class="row">
                      <div class="col-md-12">
                        <pagination-controls
                          id="taxTypeSlabData"
                          maxSize="10"
                          directionLinks="true"
                          previousLabel=""
                          nextLabel=""
                          (pageChange)="pageChangedTaxSlab($event)"
                        ></pagination-controls>
                      </div>
                    </div>
                    <br />
                  </div>
                </fieldset>
              </div>

              <!-- Tax Type TIERED Mapping  -->
              <div *ngIf="this.taxTypeTiered.enabled">
                <fieldset>
                  <legend>Tax Tier list</legend>
                  <div class="boxWhite">
                    <div class="row" [formGroup]="typeTieredForm" *ngIf="!isTaxEdit">
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <input
                          id="profileAtt"
                          class="form-control"
                          placeholder="Enter Name"
                          formControlName="name"
                          [ngClass]="{
                            'is-invalid': typeTieredSubmitted && typeTieredForm.controls.name.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeTieredSubmitted && typeTieredForm.controls.name.errors"
                        >
                          <div class="error text-danger">Name is required.</div>
                        </div>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <input
                          id="ledgerId"
                          type="text"
                          class="form-control"
                          placeholder="Enter Ledger ID"
                          formControlName="ledgerId"
                          [ngClass]="{
                            'is-invalid':
                              typeTieredSubmitted && typeTieredForm.controls.ledgerId.errors
                          }"
                        />
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <input
                          class="form-control"
                          type="number"
                          min="0"
                          placeholder="Enter Rate"
                          formControlName="rate"
                          [ngClass]="{
                            'is-invalid': typeTieredSubmitted && typeTieredForm.controls.rate.errors
                          }"
                        />
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeTieredSubmitted && typeTieredForm.controls.rate.errors"
                        >
                          <div
                            class="errorWrap text-danger"
                            *ngIf="typeTieredSubmitted && typeTieredForm.controls.rate.errors"
                          >
                            Maximum 2 digit value allowed
                          </div>
                          <div
                            class="error text-danger"
                            *ngIf="typeTieredSubmitted && typeTieredForm.controls.rate.errors.min"
                          >
                            Negative value not allowed.
                          </div>
                          <div
                            class="error text-danger"
                            *ngIf="
                              typeTieredSubmitted && typeTieredForm.controls.rate.errors.pattern
                            "
                          >
                            Only numeric value allowed.
                          </div>
                          <div
                            class="error text-danger"
                            *ngIf="
                              typeTieredSubmitted && typeTieredForm.controls.rate.errors.required
                            "
                          >
                            Rate is required.
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <div>
                          <div *ngIf="infoMessageDisplayed" class="error-message">
                            {{ infoMessage }}
                          </div>
                          <p-dropdown
                            [options]="taxGroupOptions"
                            optionValue="value"
                            optionLabel="label"
                            filter="true"
                            filterBy="label"
                            placeholder="Select a Tax Group"
                            formControlName="taxGroup"
                            (onChange)="handleTaxGroupChange($event)"
                            [ngClass]="{
                              'is-invalid': submitted && typeTieredForm.controls.taxGroup.errors
                            }"
                            [readonly]="isCompound"
                          ></p-dropdown>
                        </div>
                        <div
                          class="errorWrap text-danger"
                          *ngIf="typeTieredSubmitted && typeTieredForm.controls.taxGroup.errors"
                        >
                          <div
                            class="error text-danger"
                            *ngIf="
                              typeTieredSubmitted &&
                              typeTieredForm.controls.taxGroup.errors.required
                            "
                          >
                            Tax Group is required.
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <p-dropdown
                          [options]="beforeTax"
                          optionValue="value"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          placeholder="Select  before discount or not"
                          formControlName="beforeDiscount"
                        ></p-dropdown>
                        <br />
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2">
                        <button
                          id="addAtt"
                          style="object-fit: cover; padding: 5px 8px"
                          class="btn btn-primary"
                          (click)="onAddTaxTypeTieredField()"
                          [disabled]="infoMessageDisplayed"
                        >
                          <i class="fa fa-plus-square" aria-hidden="true"></i>
                          Add
                        </button>
                      </div>
                    </div>
                    <table class="table coa-table" style="margin-top: 10px">
                      <thead>
                        <tr>
                          <th style="text-align: center; width: 30%">Name*</th>
                          <th style="text-align: center; width: 30%">Ledger ID</th>
                          <th style="text-align: center; width: 25%">Rate*</th>
                          <th style="text-align: center; width: 30%">Tax Group*</th>
                          <th style="text-align: center; width: 30%">Before Discount</th>
                          <th
                            style="text-align: right; width: 15%; padding: 8px"
                            *ngIf="!isTaxEdit"
                          >
                            Delete
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let row of taxTypeTiered.controls
                              | paginate
                                : {
                                    id: 'taxTypeTieredData',
                                    itemsPerPage: TaxTiereditemsPerPage,
                                    currentPage: currentPageTaxTiered,
                                    totalItems: TaxTieredtotalRecords
                                  };
                            let index = index
                          "
                        >
                          <td style="padding-left: 8px">
                            <input
                              id="profileAtt"
                              class="form-control"
                              placeholder="Enter Name"
                              [formControl]="row.get('name')"
                              readonly
                            />
                            <!-- <input
                              *ngIf="!isTaxEdit"
                              id="profileAtt"
                              class="form-control"
                              placeholder="Enter Name"
                              [formControl]="row.get('name')"
                            /> -->
                          </td>
                          <td style="padding-left: 8px">
                            <input
                              id="ledgerId"
                              class="form-control"
                              placeholder="Enter ledgerId"
                              [formControl]="row.get('ledgerId')"
                              readonly
                            />
                            <!--                            <input-->
                            <!--                              *ngIf="!isTaxEdit"-->
                            <!--                              id="ledgerId"-->
                            <!--                              class="form-control"-->
                            <!--                              placeholder="Enter ledgerId"-->
                            <!--                              [formControl]="row.get('ledgerId')"-->
                            <!--                            />-->
                          </td>

                          <td>
                            <input
                              class="form-control"
                              type="number"
                              min="1"
                              placeholder="Enter Rate"
                              [formControl]="row.get('rate')"
                              [ngClass]="{
                                'is-invalid': submitted && row.get('rate').errors
                              }"
                              readonly
                            />
                            <div
                              class="errorWrap text-danger"
                              *ngIf="submitted && row.get('rate').errors"
                            >
                              <div
                                class="error text-danger"
                                *ngIf="submitted && row.get('rate').errors.pattern"
                              >
                                Only numeric value allowed.
                              </div>
                            </div>
                          </td>
                          <td>
                            <p-dropdown
                              [options]="taxGroupOptions"
                              optionValue="value"
                              optionLabel="label"
                              filter="true"
                              filterBy="label"
                              placeholder="Select a Tax Group"
                              [formControl]="row.get('taxGroup')"
                              [disabled]="true"
                            ></p-dropdown>
                          </td>
                          <td>
                            <p-dropdown
                              [options]="beforeTax"
                              [formControl]="row.get('beforeDiscount')"
                              [disabled]="true"
                            ></p-dropdown>
                          </td>
                          <td style="text-align: right" *ngIf="!isTaxEdit">
                            <a
                              id="deleteAtt"
                              class="curson_pointer"
                              (click)="
                                deleteConfirmonTaxTypeTieredField(index, row.get('id').value)
                              "
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </a>
                          </td>
                        </tr>
                      </tbody>
                    </table>

                    <div class="row">
                      <div class="col-md-12">
                        <pagination-controls
                          id="taxTypeTieredData"
                          maxSize="10"
                          directionLinks="true"
                          previousLabel=""
                          nextLabel=""
                          (pageChange)="pageChangedTaxTiered($event)"
                        ></pagination-controls>
                      </div>
                    </div>
                    <br />
                  </div>
                </fieldset>
              </div>

              <div class="addUpdateBtn" style="margin: 3.5rem 0 2rem">
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="!isTaxEdit"
                  (click)="addTax('')"
                  id="submit"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Tax
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  *ngIf="isTaxEdit"
                  (click)="addTax(viewTaxListData.id)"
                  id="submit"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Tax
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row" *ngIf="taxALLDeatilsShow">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            type="button"
            class="btn btn-secondary backbtn"
            data-toggle="tooltip"
            data-placement="bottom"
            title="Go to Tax Details"
            (click)="listTax()"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">Tax</h3>
        </div>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#taxalldea"
            aria-expanded="false"
            aria-controls="taxalldea"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="taxalldea" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend>Basic Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl">Name :</label>
                  <span>{{ taxAllData.name }}</span>
                </div>
                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl">Type :</label>
                  <span>{{ taxAllData.taxtype }}</span>
                </div>
                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl">Status :</label>
                  <span *ngIf="taxAllData.status == 'Y'" class="badge badge-success"> Active </span>
                  <span *ngIf="taxAllData.status == 'N'" class="badge badge-danger">
                    Inactive
                  </span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-12 col-md-12 dataGroup">
                  <label class="datalbl">Description :</label>
                  <span>{{ taxAllData.desc }}</span>
                </div>
              </div>
            </div>
          </fieldset>

          <!-- Tax Type slab Mapping -->
          <fieldset *ngIf="taxAllData.slabList.length !== 0">
            <legend>Tax Type Mapping</legend>
            <div class="boxWhite">
              <div class="row table-responsive">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Range From</th>
                        <th>Range UpTo</th>
                        <th>Rate</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let data of taxAllData.slabList
                            | paginate
                              : {
                                  id: 'typeslabPageData',
                                  itemsPerPage: typeslabItemPerPage,
                                  currentPage: currentPagetypeslabList,
                                  totalItems: typeslabtotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ data.name }}</td>
                        <td>{{ data.rangeFrom }}</td>
                        <td>{{ data.rangeUpTo }}</td>
                        <td>{{ data.rate }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <pagination-controls
                    id="typeslabPageData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedtypeslabList($event)"
                  ></pagination-controls>
                </div>
              </div>
            </div>
          </fieldset>

          <!-- Tax Type Tiered Mapping -->
          <fieldset *ngIf="taxAllData.tieredList.length !== 0">
            <legend>Tax Tier list</legend>
            <div class="boxWhite">
              <div class="row table-responsive">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Tax Group</th>
                        <th>Rate</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let data of taxAllData.tieredList
                            | paginate
                              : {
                                  id: 'typeTieredPageData',
                                  itemsPerPage: typeTieredItemPerPage,
                                  currentPage: currentPagetypeTieredList,
                                  totalItems: typeTieredtotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ data.name }}</td>
                        <td>{{ data.taxGroup }}</td>
                        <td>{{ data.rate }}%</td>
                      </tr>
                    </tbody>
                  </table>
                  <pagination-controls
                    id="typeTieredPageData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedtypeTieredList($event)"
                  ></pagination-controls>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </div>
</div>
