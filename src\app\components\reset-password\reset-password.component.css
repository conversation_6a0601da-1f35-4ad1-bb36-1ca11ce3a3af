/* Set a slightly transparent white background color for the login form */
.auth-box {
    background-color: rgba(
      255,
      255,
      255,
      0.9
    ); /* White background with 90% opacity */
    padding: 20px; /* Adjust the padding as needed */
    border-radius: 10px; /* Add rounded corners if desired */
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); /* Add a shadow for better visibility */
  }
  
  /* Optionally, you can style other elements within the login form as well */
  .form-auth-small {
    background-color: transparent; /* Make the form itself transparent */
  }
  
  /* Adjust the text color to make it visible on the white background */
  .form-auth-small label,
  .form-auth-small input,
  .form-auth-small button {
    color: #000; /* Text color for labels, input, and button (black in this case) */
  }
  
  /* Style the input fields and button further as needed */
  .form-auth-small input {
    border: 1px solid #ccc; /* Add a border to the input fields */
  }
  
  .form-auth-small button {
    background-color: #f7b206; /* Button background color */
    border: none;
    color: #fff; /* Text color for the button (white in this case) */
    transition: background-color 0.3s; /* Add a smooth transition effect */
  }
  
  .form-auth-small button:hover {
    background-color: #fdd142; /* Change the background color on hover to a different color */
  }
  
  .btn.btn-primary.btn-lg {
    /* Add your border-radius value (e.g., 5px) */
    border-radius: 5px;
  }
  
  .custom-input {
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    font-size: 16px;
    width: 100%;
    padding-right: 50px;
  }
  
  /* Add styles for the input-wrapper div */
  .input-wrapper {
    position: relative;
  display: flex;
  align-items: center;
  }
  
  /* Add styles for the error messages */
  .errorWrap {
    margin-top: 5px;
  }
  
  .error {
    font-size: 14px;
    color: red;
  }
  
  .lead {
    font-weight: bold;
  }
  
  .toggle-password {
    position: absolute;
    right: 2px; /* adjust based on your preference for the distance from the right edge */
    top: -9%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    z-index: 2; /* to ensure it stays above the input */
  }
  
