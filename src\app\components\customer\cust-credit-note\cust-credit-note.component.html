<div class="panel">
  <div class="panel-heading">
    <div class="displayflex">
      <button
        (click)="customerDetailOpen()"
        class="btn btn-secondary backbtn"
        data-placement="bottom"
        data-toggle="tooltip"
        style="margin-left: 0px"
        title="Go to Customer Details"
        type="button"
      >
        <i class="fa fa-arrow-circle-left" style="color: #f7b206 !important; font-size: 28px"></i>
      </button>
      <h3 class="panel-title">
        {{ custData.title }}
        {{ custData.firstname }}
        {{ custData.firstname }} Credit Note Details
      </h3>
    </div>
    <div class="right">
      <button class="btn refreshbtn" type="reset" (click)="searchPayment()">
        <i class="fa fa-refresh"></i>
      </button>
      <button
        aria-controls="serviceDetailsCust"
        aria-expanded="false"
        class="btn-toggle-collapse"
        data-target="#serviceDetailsCust"
        data-toggle="collapse"
        type="button"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div>
  <div class="panel-collapse collapse in" id="serviceDetailsCust" *ngIf="creditNoteAccess">
    <div class="panel-body">
      <button
        type="submit"
        class="btn btn-primary statusbtn"
        data-title="Add Credit Note"
        style="margin-left: 0"
        data-target="#addCreditNoteModal"
        class="yellowBtn"
        data-toggle="modal"
        data-backdrop="static"
        data-keyboard="false"
        [disabled]="addCreditNoteBtn"
        (click)="addNewCreditNote()"
      >
        Add Credit Note
      </button>

      <div class="table-responsive" *ngIf="searchPaymentData.length !== 0">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <table class="table">
              <thead>
                <tr>
                  <th>Customer</th>
                  <th style="width: 7%">Credit Note Amount</th>
                  <th>Invoice No.</th>
                  <!-- <th>Reference No.</th> -->
                  <!-- <th>Credit Note Amount</th> -->
                  <th>Credit Note No.</th>
                  <th>Credit Date</th>
                  <th>Generated By</th>
                  <th>Remark</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let payment of searchPaymentData
                      | paginate
                        : {
                            id: 'searchPaymentPageData',
                            itemsPerPage: paymentitemsPerPage,
                            currentPage: currentPagePaymentSlab,
                            totalItems: paymenttotalRecords
                          };
                    index as i
                  "
                >
                  <td>
                    {{ payment.customerName }}
                  </td>
                  <!-- <td>{{ payment.type }}</td> -->
                  <td>
                    <span
                      class="curson_pointer"
                      style="color: #f7b206"
                      (click)="openPaymentInvoiceModal('PaymentDetailModal', payment.id)"
                    >
                      {{ payment.amount | currency: currency : "symbol" : "1.2-2" }}
                    </span>
                  </td>
                  <td>{{ payment.invoiceNumber }}</td>
                  <!-- <td>{{ payment.referenceno }}</td> -->
                  <!-- <td>{{ payment.amount | number : "1.2-2" }}</td> -->
                  <td>{{ payment.documentno }}</td>
                  <td>{{ payment.paymentdate }}</td>
                  <td>{{ payment.createbyname }}</td>
                  <td>{{ payment.remarks }}</td>
                  <td *ngIf="payment.status === 'pending'">
                    <span class="badge badge-info">
                      {{ "Generated" | titlecase }}
                    </span>
                  </td>
                  <td *ngIf="payment.status === 'rejected'">
                    <span class="badge badge-danger">
                      {{ payment.status | titlecase }}
                    </span>
                  </td>
                  <td *ngIf="payment.status === 'approved'">
                    <span class="badge badge-success">
                      {{ payment.status | titlecase }}
                    </span>
                  </td>
                  <td *ngIf="payment.status === 'Fully Adjusted'">
                    <span class="badge badge-success">
                      {{ "Adjusted" | titlecase }}
                    </span>
                  </td>
                  <td *ngIf="payment.status === 'Generated'">
                    <span class="badge badge-info">
                      {{ payment.status | titlecase }}
                    </span>
                  </td>
                  <td *ngIf="payment.status === 'Partialy Adjusted'">
                    <span class="badge badge-success">
                      {{ "Partialy Adjusted" | titlecase }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="pagination_Dropdown">
              <pagination-controls
                id="searchPaymentPageData"
                maxSize="10"
                directionLinks="true"
                previousLabel=""
                nextLabel=""
                (pageChange)="pageChangedPaymentList($event)"
              ></pagination-controls>
              <div id="itemPerPageDropdown">
                <p-dropdown
                  [options]="pageLimitOptions"
                  optionLabel="value"
                  optionValue="value"
                  (onChange)="TotalItemPerPage($event)"
                ></p-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="table-responsive" *ngIf="searchPaymentData.length === 0">
        Details are not available
      </div>
    </div>
  </div>
</div>
<app-payment-amount-model
  *ngIf="displayInvoiceDetails"
  dialogId="PaymentDetailModal"
  [paymentId]="paymentId"
  (closeParentCustt)="closeParentCustt()"
></app-payment-amount-model>

<p-dialog
  header="Add Credit Note"
  [(visible)]="displayAddCreditNote"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeDisplayAddCreditNote()"
>
  <form [formGroup]="paymentFormGroup">
    <div class="modal-body">
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Customer*</label>
          <select class="form-control" disabled name="customerid" style="width: 100%">
            <option value="">
              {{ custData?.title }}
              {{ custData?.firstname }}
              {{ custData?.lastname }}
            </option>
          </select>
          <div
            class="errorWrap text-danger"
            *ngIf="submitted && paymentFormGroup.controls.customerid.errors"
          >
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.customerid.errors.required"
            >
              Customer is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Invoice*</label>
          <br />
          <p-dropdown
            [options]="invoiceList"
            optionValue="id"
            optionLabel="docnumber"
            filter="true"
            filterBy="docnumber"
            placeholder="Select a Invoice"
            formControlName="invoiceId"
            styleClass="disableDropdown"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.invoiceId.errors
            }"
            [disabled]="true"
          ></p-dropdown>
          <button
            class="btn btn-primary"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            (click)="modalOpenInvoiceList()"
          >
            <i class="fa fa-plus-square"></i>
          </button>
          <div
            class="errorWrap text-danger"
            *ngIf="submitted && paymentFormGroup.controls.invoiceId.errors"
          >
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.invoiceId.errors.required"
            >
              Invoice is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Amount*</label>
          <input
            type="number"
            step=".01"
            class="form-control"
            min="1"
            placeholder="Enter Amount"
            formControlName="amount"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.amount.errors
            }"
            customDecimal
            (keypress)="keypressId($event)"
          />
          <div
            class="errorWrap text-danger"
            *ngIf="submitted && paymentFormGroup.controls.amount.errors"
          >
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.required"
            >
              Amount is required.
            </div>
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.pattern"
            >
              Only numeric characters allowed.
            </div>
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.amount.errors.min"
            >
              Amount must be greater then 0.
            </div>
          </div>
          <br />
        </div>
        <div
          class="col-lg-3 col-md-3 col-sm-6 col-xs-12"
          *ngIf="paymentFormGroup.controls.paymentreferenceno.enabled"
        >
          <label>Credit Reference No</label>
          <input
            type="text"
            class="form-control"
            placeholder="Credit Reference No"
            formControlName="paymentreferenceno"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.paymentreferenceno.errors
            }"
          />
          <div
            class="errorWrap text-danger"
            *ngIf="submitted && paymentFormGroup.controls.paymentreferenceno.errors"
          >
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.paymentreferenceno.errors.required"
            >
              Credit Reference No is required.
            </div>
          </div>
          <br />
        </div>
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Reference No.*</label>
          <input
            type="text"
            class="form-control"
            placeholder="Enter Reference No."
            formControlName="referenceno"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.referenceno.errors
            }"
          />
          <div
            class="errorWrap text-danger"
            *ngIf="submitted && paymentFormGroup.controls.referenceno.errors"
          >
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.referenceno.errors.required"
            >
              Reference No. is required.
            </div>
          </div>
          <br />
        </div>
      </div>
      <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <label>Remark *</label>
          <textarea
            class="form-control"
            placeholder="Enter Remark"
            rows="3"
            formControlName="remark"
            [ngClass]="{
              'is-invalid': submitted && paymentFormGroup.controls.remark.errors
            }"
          ></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="submitted && paymentFormGroup.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="submitted && paymentFormGroup.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </div>
    <div class="modal-footer" style="display: flex; justify-content: flex-end">
      <div class="addUpdateBtn">
        <div class="addUpdateBtn">
          <button type="submit" class="btn btn-primary" id="submit" (click)="addPayment('')">
            <i class="fa fa-check-circle"></i>
            Add Credit Note
          </button>
        </div>
      </div>
      <div class="addUpdateBtn" style="margin-left: 1.5rem">
        <button type="button" class="btn btn-danger" (click)="closeDisplayAddCreditNote()">
          Close
        </button>
      </div>
    </div>
  </form>
</p-dialog>

<p-dialog
  header="Select Invoice"
  [(visible)]="displaySelectInvoice"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <p-table #dt [value]="invoiceList" responsiveLayout="scroll" [(selection)]="selectedInvoice">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Doc Number</th>
          <th>Created By</th>
          <th>Tax Amount</th>
          <th>Total Invoice</th>
          <th>Pending Amount</th>
          <th>Refundable Amount</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-invoice let-rowIndex="rowIndex">
        <tr>
          <td>
            <p-tableRadioButton [value]="invoice"></p-tableRadioButton>
          </td>
          <td>{{ invoice.docnumber }}</td>
          <td>{{ invoice.createdByName }}</td>
          <td>
            <a (click)="openTaxModal(invoice.id)">{{ invoice.tax }}</a>
          </td>
          <td>{{ invoice.totalamount | number: "1.2-2" }}</td>
          <td *ngIf="!invoice.adjustedAmount">
            {{ invoice.totalamount | number: "1.2-2" }}
          </td>
          <td *ngIf="invoice.adjustedAmount">
            {{ invoice.totalamount - invoice.adjustedAmount | number: "1.2-2" }}
          </td>
          <td>{{ invoice.refundAbleAmount | number: "1.2-2" }}</td>
        </tr>
      </ng-template>
      <!-- <ng-template pTemplate="summary">
            <p-paginator
              [rows]="parentCustomerListdataitemsPerPage"
              [first]="newFirst"
              [totalRecords]="parentCustomerListdatatotalRecords"
              (onPageChange)="paginate($event)"
            ></p-paginator>
          </ng-template> -->
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        style="object-fit: cover; padding: 5px 8px"
        class="btn btn-primary"
        (click)="saveSelInvoice()"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button type="button" class="btn btn-danger" (click)="modalCloseInvoiceList()">Close</button>
    </div>
  </div>
</p-dialog>
