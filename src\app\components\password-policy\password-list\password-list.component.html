<div class="row">
  <div class="col-md-12">
    <div class="panel mb-15">
      <div class="panel-heading">
        <h3 class="panel-title">Password Policy Management</h3>
        <div class="right">
          <button
            aria-controls="searchPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="searchPasswordPolicy" class="panel-collapse collapse in">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchName"
                class="form-control"
                placeholder="Enter Password Policy Name"
                (keydown.enter)="searchPasswordPolicy()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchPasswordPolicy()"
              >
                <i class="fa fa-searchPasswordPolicy"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearMvno()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Password Policy List</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#listmvno"
            aria-expanded="false"
            aria-controls="listmvno"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div id="listmvno" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Pattern</th>
                    <th>Password Expiration</th>
                    <th>Disable Password Recycling Prevention</th>
                    <th>Account Lockout</th>
                    <th>Min. Length</th>
                    <th>Max. Length</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let mvno of passwordListData
                        | paginate
                          : {
                              id: 'passwordListData',
                              itemsPerPage: itemsPerPage,
                              currentPage: currentPage,
                              totalItems: totalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      {{ mvno.name }}
                    </td>
                    <td>{{ mvno.pattern }}</td>
                    <td>{{ mvno.expiration_days }}</td>
                    <td>{{ mvno.disable_recycling_prevention }}</td>
                    <td>{{ mvno.disable_account_lockout }}</td>
                    <td>{{ mvno.min_length }}</td>
                    <td>{{ mvno.max_length }}</td>
                    <td>
                      <span
                        *ngIf="mvno.status == 'Active'"
                        class="badge badge-success"
                      >
                        Active
                      </span>
                      <span
                        *ngIf="mvno.status == 'Inactive'"
                        class="badge badge-danger"
                      >
                        Inactive
                      </span>
                    </td>
                    <td class="btnAction">
                      <a
                        id="edit-button"
                        href="javascript:void(0)"
                        type="button"
                        [routerLink]="['/home/<USER>/edit/', mvno.id]"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        id="delete-button"
                        href="javascript:void(0)"
                        (click)="deleteConfirmonMvno(mvno.id)"
                        disabled
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                      <!-- <a
                          [routerLink]="['/home/<USER>/', mvno.id]"
                          data-placement="mb-15"
                          data-toggle="tooltip"
                          id="editbutton"
                          title="Upload Documents"
                          type="button"
                        >
                          <img height="32" src="assets/img/up.jpg" width="32" />
                        </a>
                        <a
                          id="button"
                          title="mvno"
                          type="button"
                          (click)="sendMvnoId(mvno)"
                          data-placement="mb-15"
                          data-toggle="tooltip"
                        >
                          <img
                            height="32"
                            src="assets/img/icons-02.png"
                            width="32"
                          />
                        </a> -->
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="row">
                <div class="col-md-12" style="display: flex">
                  <pagination-controls
                    id="passwordListData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedList($event)"
                  ></pagination-controls>

                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<p-confirmDialog
  [style]="{ width: '30vw' }"
  [baseZIndex]="10000"
></p-confirmDialog>
