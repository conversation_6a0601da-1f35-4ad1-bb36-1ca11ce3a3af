<button
  *ngIf="assignInventoryAccess"
  (click)="assignInventoryModalOpen()"
  class="btn btn-primary statusbtn"
  style="background-color: #f7b206 !important; font-size: 16px; padding: 3px 12px; margin-top: 10px"
  title="Assign inventory"
  type="submit"
>
  Assign Inventory
</button>
<div class="row">
  <div class="col-lg-12 col-md-12">
    <table class="table">
      <thead>
        <tr>
          <th width="20%">Product Name</th>
          <th width="20%">Mac</th>
          <th width="20%">Serial Number</th>
          <th width="20%">Assigned Qty.</th>
          <th width="30%">Status</th>
          <th width="25%">Next Approver</th>
          <th width="25%">Assigned Date</th>
          <th width="20%">Expiry Date</th>
          <th width="30%" *ngIf="editAccess || approveProgressAccess">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="
            let assignedInventory of assignedInventoryList
              | paginate
                : {
                    id: 'assignedInventoryListData',
                    itemsPerPage: inventoryListItemsPerPage,
                    currentPage: inventoryListDataCurrentPage,
                    totalItems: inventoryListDataTotalRecords
                  };
            index as i
          "
        >
          <td>{{ assignedInventory.productName }}</td>
          <td>
            <span *ngFor="let mac of assignedInventory.inOutWardMACMapping">
              {{ mac.macAddress }}</span
            >
            {{ assignedInventory.macAddress }}
          </td>
          <td>
            <span *ngFor="let serialNumber of assignedInventory.inOutWardMACMapping">
              {{ serialNumber.serialNumber }}</span
            >
            {{ assignedInventory.serialNumber }}
          </td>
          <td>
            {{ assignedInventory.qty }}
            <!-- {{ assignedInventory.productId.unit }} -->
          </td>

          <td>
            <span
              *ngIf="assignedInventory.approvalStatus == 'Approve'"
              class="badge badge-success"
              >{{ assignedInventory.approvalStatus }}</span
            >

            <span
              *ngIf="assignedInventory.approvalStatus == 'Rejected'"
              class="badge badge-danger"
              >{{ assignedInventory.approvalStatus }}</span
            >

            <span *ngIf="assignedInventory.approvalStatus == 'Pending'" class="badge badge-info">
              {{ assignedInventory.approvalStatus }}
            </span>
          </td>
          <td>
            {{ assignedInventory.assigneeName }}
          </td>
          <td>
            {{ assignedInventory.assignedDateTime | date: "dd-MM-yyyy hh:mm:ss" }}
          </td>
          <td>
            {{ assignedInventory.expiryDateTime | date: "dd-MM-yyyy hh:mm:ss" }}
          </td>
          <td class="btnAction" *ngIf="editAccess || approveProgressAccess">
            <a
              *ngIf="editAccess"
              title="Edit"
              style="margin-right: 5px; cursor: pointer"
              (click)="editCustomerInventory(assignedInventory.id, assignedInventory)"
            >
              <img src="assets/img/ioc01.jpg" />
            </a>
            <!-- <button
                        type="button"
                        class="btn btn-success gridbtn"
                        style="border: none; background: transparent; padding: 0; margin-right: 5px"
                        title="Approve"
                        [disabled]="assignedInventory.nextApproverId == null"
                        (click)="approveInventory(assignedInventory.id)"
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>
                      <button
                        type="button"
                        class="btn btn-danger gridbtn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        [disabled]="assignedInventory.nextApproverId == null"
                        title="Reject"
                        (click)="rejectInventory(assignedInventory.id)"
                      >
                        <img src="assets/img/reject.jpg" />
                      </button> -->
            <button
              type="button"
              style="border: none; background: transparent; padding: 0; margin-right: 3px"
              [disabled]="
                assignedInventory.approvalStatus != 'Pending' ||
                assignedInventory.createdById != loggedInStaffId
              "
              title="Approve"
              class="curson_pointer approve-btn"
              (click)="approveChangeStatus(assignedInventory.id)"
            >
              <img src="assets/img/assign.jpg" />
            </button>
            <button
              type="button"
              style="border: none; background: transparent; padding: 0; margin-right: 3px"
              title="Rejected"
              class="curson_pointer approve-btn"
              [disabled]="
                assignedInventory.approvalStatus != 'Pending' ||
                assignedInventory.createdById != loggedInStaffId
              "
              (click)="rejectChangeStatus(assignedInventory.id)"
            >
              <img src="assets/img/reject.jpg" />
            </button>
            <!-- <a
              *ngIf="approveProgressAccess"
              data-backdrop="static"
              data-keyboard="false"
              data-target="#inventoryStatusViewId"
              data-toggle="modal"
              id="delete-button"
              href="javascript:void(0)"
              title="Approval Progress"
              class="curson_pointer approve-btn"
              (click)="checkStatus(assignedInventory.id, assignedInventory.status)"
            >
              <img width="32" height="32" src="assets/img/05_inventory-to-customer_Y.png" />
            </a> -->
          </td>
        </tr>
      </tbody>
    </table>
    <div style="display: flex">
      <pagination-controls
        id="assignedInventoryListData"
        [maxSize]="5"
        [directionLinks]="true"
        previousLabel=""
        nextLabel=""
        (pageChange)="pageChangedEventAssignInventory($event)"
      >
      </pagination-controls>
      <div #itemPerPageDropDownInventory id="itemPerPageDropdown">
        <p-dropdown
          [options]="pageLimitOptions"
          optionLabel="value"
          optionValue="value"
          (onChange)="itemPerPageChangedEventAssignInventory($event)"
        ></p-dropdown>
      </div>
    </div>
  </div>
</div>

<!-- Edit Assign Inventory-->
<div *ngIf="viewAssignInventoryWithSerial" style="margin-top: 10px">
  <h3 class="panel-title">Inventory Details List</h3>
  <div class="row">
    <div class="col-lg-12 col-md-12">
      <table class="table">
        <thead>
          <tr>
            <th style="text-align: left">Serial</th>
            <th style="text-align: left">MAC Address</th>
            <th style="text-align: left">Status</th>
            <th style="text-align: left">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let assignedInventory of this.assignedInventoryListWithSerial">
            <td>{{ assignedInventory.serialNumber }}</td>
            <td>{{ assignedInventory.macAddress }}</td>
            <td>{{ assignedInventory.status }}</td>

            <td>
              <button
                (click)="
                  removeConfirmationInventory(assignedInventory.id, assignedInventory.itemId)
                "
                *ngIf="deleteAccess"
                class="approve-btn"
                style="border: none; background: #f7b206; padding: 2.5px 7px; margin: 3px 3px 0 0"
                title="Remove Inventory"
                type="button"
                [disabled]="assignedInventory.inReplacementProcess"
              >
                <img class="icon" src="assets/img/ioc02.jpg" height="25" width="25" />
              </button>
              <button
                (click)="InventoryReplace(assignedInventory)"
                *ngIf="deleteAccess"
                class="approve-btn"
                style="border: none; background: #f7b206; padding: 2.5px 7px; margin: 3px 3px 0 0"
                title="Replace Inventory"
                type="button"
                [disabled]="assignedInventory.inReplacementProcess"
              >
                <img class="icon" src="assets/img/E_Status_Y.png" height="25" width="25" />
              </button>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- <div style="display: flex">
                <pagination-controls
                  (pageChange)="pageChangedEventCustomerAssignInventoryDetails($event)"
                  [directionLinks]="true"
                  id="assignedInventoryListData"
                  [maxSize]="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div #itemPerPageDropDownInventory id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="totalItemsEventCustomerAssignInventoryDetails($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div> -->
    </div>
  </div>
</div>

<!--Approve Modal-->
<p-dialog
  header="Change Approval Status"
  [(visible)]="approveChangeStatusModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="assignInwardForm">
      <div class="row">
        <div class="col-lg-2 col-md-3 col-sm-4 col-xs-12">
          <label>Remark*:</label>
        </div>
        <div class="col-lg-10 col-md-9 col-sm-8 col-xs-12">
          <textarea class="form-control" formControlName="remark" name="remark"></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="assignInwardSubmitted && assignInwardForm.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="assignInwardSubmitted && assignInwardForm.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="approveInventory()" class="btn btn-primary" id="submit" type="submit">
      <i class="fa fa-check-circle"></i>
      Approve
    </button>
    <button
      class="btn btn-default"
      (click)="closeApproveInventoryModal()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<!--Reject Modal-->
<p-dialog
  header="Change Approval Status"
  [(visible)]="rejectChangeStatusModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="rejectInwardForm">
      <div class="row">
        <div class="col-lg-2 col-md-3 col-sm-4 col-xs-12">
          <label>Remark*:</label>
        </div>
        <div class="col-lg-10 col-md-9 col-sm-8 col-xs-12">
          <textarea class="form-control" formControlName="remark" name="remark"></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="rejectInwardSubmitted && rejectInwardForm.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="rejectInwardSubmitted && rejectInwardForm.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button (click)="rejectInventory()" class="btn btn-primary" id="submit" type="submit">
      <i class="fa fa-check-circle"></i>
      Reject
    </button>
    <button
      class="btn btn-default"
      (click)="closeRejectInventoryModal()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<!--Assign Inventory Modal-->
<p-dialog
  header="Assign Inventory"
  [(visible)]="assignInventory"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="inventoryAssignForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Item Type*</label>
            <p-dropdown
              (onChange)="getSelItemType($event)"
              [options]="ItemSelectionType"
              filter="true"
              filterBy="label"
              formControlName="itemTypeFlag"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Item Type"
            >
            </p-dropdown>
            <div
              *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.itemTypeFlag.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.itemTypeFlag.errors.required
                "
                class="error text-danger"
              >
                Item Type is required.
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="getAllSerializedProductFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Product*</label>
              <p-dropdown
                [options]="allActiveProducts"
                formControlName="productId"
                optionLabel="name"
                optionValue="id"
                filter="true"
                filterBy="name"
                placeholder="Select Product"
                (onChange)="getMacAddressList($event)"
                appendTo="body"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="inventoryAssignSubmitted && inventoryAssignForm.controls.productId.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="
                    inventoryAssignSubmitted &&
                    inventoryAssignForm.controls.productId.errors.required
                  "
                >
                  Product is required.
                </div>
              </div>
            </div>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="macAddressList.length > 0 && getItemSelctionFlag"
          >
            <div class="form-group">
              <p-table
                #dt
                [(selection)]="selectedMACAddress"
                [globalFilterFields]="['condition', 'itemId', 'serialNumber', 'macAddress']"
                [rowHover]="true"
                [value]="macAddressList"
                dataKey="id"
                responsiveLayout="scroll"
                styleClass="p-datatable-customers"
                [filterDelay]="0"
                [scrollable]="true"
                scrollHeight="300px"
              >
                <ng-template pTemplate="caption">
                  <form [formGroup]="searchForm">
                    <div class="row">
                      <div class="col-lg-3 col-md-3">
                        <p-dropdown
                          [options]="macOptionSelect"
                          optionValue="value"
                          optionLabel="label"
                          filter="true"
                          filterBy="label"
                          formControlName="searchOption"
                          (onChange)="selMacSearchOption($event.value)"
                          placeholder="Select a search option"
                        >
                        </p-dropdown>
                      </div>
                      <div
                        class="col-lg-3 col-md-3"
                        *ngIf="this.searchForm.value.searchOption === 'itemId'"
                      >
                        <input
                          id="namesearch"
                          type="text"
                          class="form-control"
                          formControlName="searchMacDeatil"
                          (keydown.enter)="searchMac()"
                          placeholder="Enter Item Id"
                        />
                      </div>
                      <div
                        class="col-lg-3 col-md-3"
                        *ngIf="this.searchForm.value.searchOption === 'mac'"
                      >
                        <input
                          id="mobilesearch"
                          (keydown.enter)="searchMac()"
                          type="text"
                          class="form-control"
                          formControlName="searchMacDeatil"
                          placeholder="Enter Mac"
                        />
                      </div>
                      <div
                        class="col-lg-3 col-md-3"
                        *ngIf="this.searchForm.value.searchOption === 'serialNumber'"
                      >
                        <input
                          id="leadSourcesearch"
                          (keydown.enter)="searchMac()"
                          type="text"
                          class="form-control"
                          formControlName="searchMacDeatil"
                          placeholder="Enter Serial Number"
                        />
                      </div>
                      <div
                        class="col-lg-3 col-md-3"
                        *ngIf="this.searchForm.value.searchOption === 'assetId '"
                      >
                        <input
                          id="serviceArea"
                          (keydown.enter)="searchMac()"
                          type="text"
                          class="form-control"
                          formControlName="searchMacDeatil"
                          placeholder="Enter Asset Id"
                        />
                      </div>
                      <div
                        class="col-lg-3 col-md-3"
                        *ngIf="this.searchForm.value.searchOption === 'itemType'"
                      >
                        <input
                          id="Lead Assigne Name"
                          (keydown.enter)="searchMac()"
                          type="text"
                          class="form-control"
                          formControlName="searchMacDeatil"
                          placeholder="Enter Item Type"
                        />
                      </div>
                      <div class="row">
                        <button
                          type="button"
                          class="btn btn-primary"
                          id="searchbtn"
                          (click)="searchMac()"
                          [disabled]="!this.searchForm.value.searchMacDeatil"
                        >
                          <i class="fa fa-search"></i>
                          Search
                        </button>
                        &nbsp;
                        <button
                          type="button"
                          class="btn btn-default"
                          id="searchbtn"
                          (click)="clearMac()"
                        >
                          <i class="fa fa-refresh"></i>
                          Clear
                        </button>
                      </div>
                    </div>
                  </form>
                </ng-template>
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 10%">
                      <!-- <p-tableRadioButton></p-tableRadioButton> -->
                    </th>
                    <th>Item Id</th>
                    <th>Item Type</th>
                    <th *ngIf="this.hasMac">MAC Address</th>
                    <th *ngIf="this.hasSerial">Serial Number</th>
                    <th>Action</th>
                  </tr>
                </ng-template>
                <ng-template let-product pTemplate="body">
                  <tr>
                    <td style="width: 10%">
                      <p-tableRadioButton
                        *ngIf="product.customerId == null"
                        [value]="product"
                        (click)="editMacMapping(product.id)"
                      ></p-tableRadioButton>
                    </td>
                    <td>
                      <input name="itemId" class="form-control" [value]="product.itemId" disabled />
                    </td>
                    <td>
                      {{ product.condition }}
                    </td>
                    <td *ngIf="this.hasMac">
                      <input
                        type="text"
                        name="macAddress"
                        class="form-control"
                        [value]="product.macAddress"
                        [(ngModel)]="product.macAddress"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="enterMacSerial == product.id ? false : true"
                      />
                    </td>
                    <td *ngIf="this.hasSerial">
                      <input
                        type="text"
                        name="serialNumber"
                        class="form-control"
                        [value]="product.serialNumber"
                        [(ngModel)]="product.serialNumber"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="enterMacSerial == product.id ? false : true"
                      />
                    </td>
                    <td>
                      <button
                        (click)="editMac(product.id)"
                        [disabled]="editMacSerialBtn == product.id && isEditEnable ? false : true"
                        class="curson_pointer approve-btn"
                        title="Edit Mac"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </button>
                      <button
                        id="addAtt"
                        class="btn btn-primary"
                        (click)="saveMacidMapping(product.itemId, product)"
                        style="object-fit: cover; padding: 5px 8px"
                        icon="pi pi-check"
                        [disabled]="
                          enterMacSerial == product.id && editMacSerialBtn == product.id
                            ? false
                            : true
                        "
                      >
                        Add
                      </button>
                    </td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="summary">
                  <p-paginator
                    (onPageChange)="paginateMacAddress($event)"
                    [first]="newFirstMacAddress"
                    [rows]="macAddressListdataitemsPerPage"
                    [totalRecords]="macAddressListtotalRecords"
                    [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                  ></p-paginator>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <div *ngIf="getAllNonSerializedProductFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Product*</label>
              <p-dropdown
                *ngIf="getAllNonSerializedProductFlag"
                (onChange)="getNonTrackableProductQty($event)"
                [options]="allActiveNonTrackableProducts"
                filter="true"
                filterBy="name"
                formControlName="productId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Product"
              ></p-dropdown>
              <div
                *ngIf="inventoryAssignSubmitted && inventoryAssignForm.controls.productId.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSubmitted &&
                    inventoryAssignForm.controls.productId.errors.required
                  "
                  class="error text-danger"
                >
                  Product is required.
                </div>
              </div>
            </div>
          </div>
          <tr>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label style="margin-bottom: 1.5rem"> Available Quantity </label>
                  <!-- &nbsp;&nbsp;&nbsp; -->
                  <br />
                  <label>
                    {{ this.availableQty }}
                  </label>
                </div>
              </div>
            </th>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label>Assign Quantity*</label>
                  <input
                    type="number"
                    (keypress)="assignQuantityValidation($event)"
                    class="form-control"
                    placeholder="Enter Assign Quantity"
                    formControlName="nonSerializedQty"
                    [ngClass]="{
                      'is-invalid':
                        inventoryAssignSumitted &&
                        inventoryAssignForm.controls.nonSerializedQty.errors
                    }"
                  />
                  <div class="error text-danger" *ngIf="this.showQtyError">
                    Assign quantity must be less than available quantity.
                  </div>
                  <div class="error text-danger" *ngIf="this.negativeAssignQtyError">
                    Assign quantity must be greater than 0.
                  </div>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="
                      inventoryAssignSumitted &&
                      inventoryAssignForm.controls.nonSerializedQty.errors
                    "
                  >
                    <div
                      class="error text-danger"
                      *ngIf="
                        inventoryAssignSumitted &&
                        inventoryAssignForm.controls.nonSerializedQty.required
                      "
                    >
                      Assign Quantity is required.
                    </div>
                    <div
                      *ngIf="
                        inventoryAssignSumitted &&
                        inventoryAssignForm.controls.nonSerializedQty.errors.pattern
                      "
                      class="error text-danger"
                    >
                      Only Numeric value are allowed.
                    </div>
                  </div>
                </div>
              </div>
            </th>
          </tr>
        </div>

        <!-- Assign Date -->
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Assign Date*</label>
            <p-calendar
              [style]="{ width: '100%' }"
              formControlName="assignedDateTime"
              [showTime]="true"
              [showSeconds]="true"
              inputId="time"
              [numberOfMonths]="1"
              appendTo="body"
            ></p-calendar>
            <div
              class="errorWrap text-danger"
              *ngIf="
                inventoryAssignSubmitted && inventoryAssignForm.controls.assignedDateTime.errors
              "
            >
              <div
                class="error text-danger"
                *ngIf="
                  inventoryAssignSubmitted &&
                  inventoryAssignForm.controls.assignedDateTime.errors.required
                "
              >
                Assign Date is required.
              </div>
            </div>
          </div>
        </div>

        <!-- Latitude -->
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Latitude</label>
            <input
              class="form-control"
              formControlName="latitude"
              id="latitude"
              placeholder="Enter latitude"
              type="text"
            />
          </div>
        </div>

        <!-- Longitude -->
        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Longitude</label>
            <input
              class="form-control"
              formControlName="longitude"
              id="longitude"
              placeholder="Enter longitude"
              type="text"
            />
          </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
          <div style="margin-bottom: 1rem">
            <span
              class="HoverEffect"
              (click)="mylocation()"
              title="Get Current Location"
              style="border-bottom: 1px solid #f7b206; cursor: pointer"
            >
              <img
                class="LocationIcon LocationIconMargin"
                src="assets/img/B_Find-My-current-location_Y.png"
              />
            </span>
          </div>
        </div>

        <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="form-group">
                <label>Status*</label>
                <p-dropdown
                  [options]="inventoryStatus"
                  formControlName="status"
                  optionLabel="label"
                  optionValue="value"
                  filter="true"
                  filterBy="label"
                  placeholder="Select status"
                ></p-dropdown>
                <div
                  class="errorWrap text-danger"
                  *ngIf="inventoryAssignSubmitted && inventoryAssignForm.controls.status.errors"
                >
                  <div
                    class="error text-danger"
                    *ngIf="
                      inventoryAssignSubmitted &&
                      inventoryAssignForm.controls.status.errors.required
                    "
                  >
                    Status is required.
                  </div>
                </div>
              </div>
            </div> -->
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="assigneInventory()"
      class="btn btn-primary btn-sm"
      id="submit"
      type="submit"
      *ngIf="serializedItemAssignFlag"
    >
      <i class="fa fa-check-circle"></i>
      Assign Inventory
    </button>
    <button
      (click)="assigneOtherInventoryForNonSerializedItem()"
      class="btn btn-primary btn-sm"
      id="submit"
      type="submit"
      *ngIf="nonSerializedItemAssignFlag"
    >
      <i class="fa fa-check-circle"></i>
      Assign Inventory
    </button>
    <button class="btn btn-danger btn-sm" (click)="assignInventoryModalClose()">Close</button>
  </div>
</p-dialog>

<!-- Replace Inventory Model -->
<p-dialog
  header="Replace Inventory"
  [(visible)]="replaceInventory"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="inventoryReplaceForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label>Device name</label>
          <input
            type="text"
            name="deviceName"
            class="form-control"
            [(ngModel)]="deviceName"
            [ngModelOptions]="{ standalone: true }"
            readonly
          />
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Total IN Ports</label>
          <input
            type="text"
            name="deviceName"
            class="form-control"
            [(ngModel)]="deviceTotalInPorts"
            [ngModelOptions]="{ standalone: true }"
            readonly
          />
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Available IN Ports</label>
          <input
            type="text"
            name="deviceName"
            class="form-control"
            [(ngModel)]="deviceAvailableInPorts"
            [ngModelOptions]="{ standalone: true }"
            readonly
          />
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Total OUT Ports</label>
          <input
            type="text"
            name="deviceName"
            class="form-control"
            [(ngModel)]="deviceTotalOutPorts"
            [ngModelOptions]="{ standalone: true }"
            readonly
          />
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
          <label>Available OUT Ports</label>
          <input
            type="text"
            name="deviceName"
            class="form-control"
            [(ngModel)]="deviceAvailableOutPorts"
            [ngModelOptions]="{ standalone: true }"
            readonly
          />
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Replacement Reason*</label>
            <p-dropdown
              [options]="replacementreasonList"
              filter="true"
              filterBy="label"
              formControlName="replacementReason"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Reason"
            >
            </p-dropdown>
            <div
              *ngIf="
                inventoryReplaceSubmitted && inventoryReplaceForm.controls.replacementReason.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryReplaceSubmitted &&
                  inventoryReplaceForm.controls.replacementReason.errors.required
                "
                class="error text-danger"
              >
                Replacement Reason is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Item Type*</label>
            <p-dropdown
              (onChange)="onItemChange($event)"
              [options]="ItemSelectionType"
              filter="true"
              filterBy="label"
              formControlName="itemTypeFlag"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Item Type"
            >
            </p-dropdown>
            <div
              *ngIf="inventoryReplaceSubmitted && inventoryReplaceForm.controls.itemTypeFlag.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryReplaceSubmitted &&
                  inventoryReplaceForm.controls.itemTypeFlag.errors.required
                "
                class="error text-danger"
              >
                Item Type is required.
              </div>
            </div>
          </div>
        </div>
        <br />
        <div *ngIf="getAllSerializedProductFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Product*</label>
              <p-dropdown
                [options]="allActiveProducts"
                formControlName="productId"
                optionLabel="name"
                optionValue="id"
                filter="true"
                filterBy="name"
                placeholder="Select Product"
                #ddlProducts
                (onChange)="onProductChange($event, ddlProducts)"
                appendTo="body"
              ></p-dropdown>
              <div
                class="errorWrap text-danger"
                *ngIf="inventoryReplaceSubmitted && inventoryReplaceForm.controls.productId.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="
                    inventoryReplaceSubmitted &&
                    inventoryReplaceForm.controls.productId.errors.required
                  "
                >
                  Product is required.
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="isProductSelected">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
              <label>Total IN Ports</label>
              <input
                type="text"
                name="deviceName"
                class="form-control"
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="productTotalInPorts"
                readonly
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
              <label>Available IN Ports</label>
              <input
                type="text"
                name="deviceName"
                class="form-control"
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="productAvailableInPorts"
                readonly
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
              <label>Total OUT Ports</label>
              <input
                type="text"
                name="deviceName"
                class="form-control"
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="productTotalOutPorts"
                readonly
              />
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
              <label>Available OUT Ports</label>
              <input
                type="text"
                name="deviceName"
                class="form-control"
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="productavailableOutPorts"
                readonly
              />
            </div>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="macAddressList.length > 0 && getItemSelctionFlag"
          >
            <div class="form-group">
              <p-table
                #dt
                [(selection)]="selectedMACAddress"
                [globalFilterFields]="['condition', 'itemId', 'serialNumber', 'macAddress']"
                [rowHover]="true"
                [value]="macAddressList"
                dataKey="id"
                responsiveLayout="scroll"
                styleClass="p-datatable-customers"
                [loading]="loading"
                [filterDelay]="0"
                [scrollable]="true"
                scrollHeight="300px"
              >
                <ng-template pTemplate="caption">
                  <div class="flex align-items-center justify-content-between">
                    <span class="p-input-icon-left">
                      <input
                        class="form-control"
                        pInputText
                        type="text"
                        [(ngModel)]="fileterGlobal"
                        [ngModelOptions]="{ standalone: true }"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="Global Search Filter"
                      />
                    </span>
                    &nbsp;
                    <button type="button" class="btn btn-default" (click)="clearFilterGlobal(dt)">
                      Clear
                    </button>
                  </div>
                </ng-template>
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 10%">
                      <!-- <p-tableRadioButton></p-tableRadioButton> -->
                    </th>
                    <th>Item Id</th>
                    <th>Item Type</th>
                    <th *ngIf="this.hasMac">MAC Address</th>
                    <th *ngIf="this.hasSerial">Serial Number</th>
                    <th>Action</th>
                  </tr>
                </ng-template>
                <ng-template let-product pTemplate="body">
                  <tr>
                    <td style="width: 10%">
                      <p-tableRadioButton
                        *ngIf="product.customerId == null"
                        [value]="product"
                        (click)="editMacMapping(product.id)"
                      ></p-tableRadioButton>
                    </td>
                    <td>
                      <input name="itemId" class="form-control" [value]="product.itemId" disabled />
                    </td>
                    <td>
                      {{ product.condition }}
                    </td>
                    <td *ngIf="this.hasMac">
                      <input
                        type="text"
                        name="macAddress"
                        class="form-control"
                        [value]="product.macAddress"
                        [(ngModel)]="product.macAddress"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="enterMacSerial == product.id ? false : true"
                      />
                    </td>
                    <td *ngIf="this.hasSerial">
                      <input
                        type="text"
                        name="serialNumber"
                        class="form-control"
                        [value]="product.serialNumber"
                        [(ngModel)]="product.serialNumber"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="enterMacSerial == product.id ? false : true"
                      />
                    </td>
                    <td>
                      <button
                        (click)="editMac(product.id)"
                        [disabled]="editMacSerialBtn == product.id ? false : true"
                        class="curson_pointer approve-btn"
                        title="Edit Mac"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </button>
                      <button
                        id="addAtt"
                        class="btn btn-primary"
                        (click)="saveMacidMapping(product.itemId, product)"
                        style="object-fit: cover; padding: 5px 8px"
                        icon="pi pi-check"
                        [disabled]="
                          enterMacSerial == product.id && editMacSerialBtn == product.id
                            ? false
                            : true
                        "
                      >
                        Add
                      </button>
                    </td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="summary">
                  <p-paginator
                    (onPageChange)="paginateMacAddress($event)"
                    [first]="newFirstMacAddress"
                    [rows]="macAddressListdataitemsPerPage"
                    [totalRecords]="macAddressListtotalRecords"
                    [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                  ></p-paginator>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
        <div *ngIf="getAllNonSerializedProductFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Product*</label>
              <p-dropdown
                *ngIf="getAllNonSerializedProductFlag"
                (onChange)="getNonTrackableProductQty($event)"
                [options]="allActiveNonTrackableProducts"
                filter="true"
                filterBy="name"
                formControlName="productId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Product"
              ></p-dropdown>
              <div
                *ngIf="inventoryReplaceSubmitted && inventoryReplaceForm.controls.productId.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryReplaceSubmitted &&
                    inventoryReplaceForm.controls.productId.errors.required
                  "
                  class="error text-danger"
                >
                  Product is required.
                </div>
              </div>
            </div>
          </div>
          <tr>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label style="margin-bottom: 1.5rem"> Available Quantity </label>
                  <!-- &nbsp;&nbsp;&nbsp; -->
                  <br />
                  <label>
                    {{ this.availableQty }}
                  </label>
                </div>
              </div>
            </th>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label>Assign Quantity*</label>
                  <input
                    type="number"
                    (keypress)="assignQuantityValidation($event)"
                    class="form-control"
                    placeholder="Enter Assign Quantity"
                    formControlName="nonSerializedQty"
                    [ngClass]="{
                      'is-invalid':
                        inventoryReplaceSubmitted &&
                        inventoryReplaceForm.controls.nonSerializedQty.errors
                    }"
                  />
                  <div class="error text-danger" *ngIf="this.showQtyError">
                    Assign quantity must be less than available quantity.
                  </div>
                  <div class="error text-danger" *ngIf="this.negativeAssignQtyError">
                    Assign quantity must be greater than 0.
                  </div>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="
                      inventoryReplaceSubmitted &&
                      inventoryReplaceForm.controls.nonSerializedQty.errors
                    "
                  >
                    <div
                      class="error text-danger"
                      *ngIf="
                        inventoryReplaceSubmitted &&
                        inventoryReplaceForm.controls.nonSerializedQty.required
                      "
                    >
                      Assign Quantity is required.
                    </div>
                    <div
                      *ngIf="
                        inventoryReplaceSubmitted &&
                        inventoryReplaceForm.controls.nonSerializedQty.errors.pattern
                      "
                      class="error text-danger"
                    >
                      Only Numeric value are allowed.
                    </div>
                  </div>
                </div>
              </div>
            </th>
          </tr>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Assign Date*</label>
            <div>
              <p-calendar
                [style]="{ width: '50%' }"
                formControlName="assignedDateTime"
                [showTime]="true"
                [showSeconds]="true"
                inputId="time"
                [numberOfMonths]="1"
                appendTo="body"
              ></p-calendar>
              <div
                class="errorWrap text-danger"
                *ngIf="
                  inventoryReplaceSubmitted && inventoryReplaceForm.controls.assignedDateTime.errors
                "
              >
                <div
                  class="error text-danger"
                  *ngIf="
                    inventoryReplaceSubmitted &&
                    inventoryReplaceForm.controls.assignedDateTime.errors.required
                  "
                >
                  Assign Date is required.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="onReplaceInventory()"
      class="btn btn-primary btn-sm"
      id="submit"
      type="submit"
      *ngIf="serializedItemAssignFlag"
    >
      <i class="fa fa-check-circle"></i>
      Replace Inventory
    </button>
    <button
      (click)="assigneOtherInventoryForNonSerializedItem()"
      class="btn btn-primary btn-sm"
      id="submit"
      type="submit"
      *ngIf="nonSerializedItemAssignFlag"
    >
      <i class="fa fa-check-circle"></i>
      Replace Inventory
    </button>
    <button class="btn btn-danger btn-sm" (click)="replaceInventoryModalClose()">Close</button>
  </div>
</p-dialog>
