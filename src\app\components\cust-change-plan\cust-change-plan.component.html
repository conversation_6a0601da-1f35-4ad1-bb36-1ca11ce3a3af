<div class="panel">
  <div class="panel-heading">
    <div class="displayflex">
      <button
        (click)="backButton.emit(custData.id)"
        class="btn btn-secondary backbtn"
        data-placement="bottom"
        data-toggle="tooltip"
        title="Go to Customer Details"
        type="button"
      >
        <i class="fa fa-arrow-circle-left" style="color: #f7b206 !important; font-size: 28px"></i>
      </button>
      <h3 class="panel-title">
        {{ custData.title }}
        {{ custData.custname }} Change Plan
      </h3>
    </div>
    <div class="right">
      <button
        aria-controls="changePresCustPlan"
        aria-expanded="false"
        class="btn-toggle-collapse"
        data-target="#changePresCustPlan"
        data-toggle="collapse"
        type="button"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div>
  <div class="panel-collapse collapse in" id="changePresCustPlan">
    <div class="panel-body">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-6 col-xs-12">
          <p-table
            #dt
            [value]="currentPlanDetails"
            [(selection)]="selectedPlan"
            dataKey="planId"
            styleClass="p-datatable-customers"
            [rowHover]="true"
            [showCurrentPageReport]="true"
            [globalFilterFields]="['connection_no', 'service', 'planName', 'status']"
          >
            <ng-template pTemplate="caption">
              <div class="table-header">
                <div class="row">
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Change Plan Type</label>
                    <p-dropdown
                      [(ngModel)]="changePlanType"
                      [ngClass]="{
                        'is-invalid': changePlansubmitted && !changePlanType
                      }"
                      [options]="commondropdownService.planPurchaseTypeData"
                      filter="true"
                      filterBy="text"
                      optionLabel="text"
                      optionValue="value"
                      placeholder="Select a Plan Change Type"
                    ></p-dropdown>
                  </div>
                  <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                    <label>Search</label><br />
                    <div class="p-input-icon-left">
                      <i class="pi pi-search"></i>
                      <input
                        pInputText
                        type="text"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="Global Search"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
            <ng-template pTemplate="header">
              <tr>
                <th style="width: 3rem"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                <th>Connection No.</th>
                <th>Service Name</th>
                <th>Current Plan Name</th>
                <th>Select New Plan</th>
                <!-- <th>Select Plan Type</th> -->
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-customer>
              <tr class="p-selectable-row">
                <td>
                  <p-tableCheckbox [pSelectableRow]="customer" [value]="customer"></p-tableCheckbox>
                </td>
                <td>
                  {{ customer.connection_no }}
                </td>
                <td>
                  <span class="image-text">{{ customer.service }}</span>
                </td>
                <td>
                  <span class="image-text">{{ customer.planName }}</span>
                </td>
                <td>
                  <p-dropdown
                    [(ngModel)]="customer.planId"
                    [ngClass]="{
                      'is-invalid': changePlansubmitted && !newPlanId
                    }"
                    [options]="planList"
                    filter="true"
                    filterBy="label"
                    optionLabel="displayName"
                    optionValue="id"
                    placeholder="Select a New Plan"
                  ></p-dropdown>
                </td>
                <!-- <td>
            <p-dropdown
              filter="true"
              filterBy="label"
              [options]="planDetailsCategory"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Change Type"
            ></p-dropdown>
          </td> -->
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="8">No Current Plan found.</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
      <div [formGroup]="planChangeForm">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 form-group" style="margin-top: 20px">
            <label>Billable To</label>
            <br />
            <p-dropdown
              [disabled]="true"
              [options]="billableCustList"
              [showClear]="true"
              filter="true"
              filterBy="name"
              formControlName="billableCustomerId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Billable"
              styleClass="disableDropdown"
            ></p-dropdown>
            <button
              type="button"
              (click)="modalOpenParentCustomer('billable-change-plan')"
              class="btn btn-primary"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-plus-square"></i>
            </button>
            <button
              class="btn btn-danger"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
              (click)="removeSelParentCust('billable-change-plan')"
            >
              <i class="fa fa-trash"></i>
            </button>
          </div>
          <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12" style="margin-top: 20px">
            Payment Received :<br />
            <label style="width: 150px" class="switch">
              <input
                class="switch-input"
                (click)="paymentFlagToggle($event)"
                formControlName="isPaymentReceived"
                type="checkbox"
                ng-model="view"
                ng-true-value="Yes"
                ng-false-value="No"
                ng-checked="view == 'Yes"
              />
              <div class="switch-button">
                <span class="switch-button-left">Yes</span>
                <span class="switch-button-right">No</span>
              </div>
            </label>
          </div>
          <div
            *ngIf="planChangeForm.value.isPaymentReceived"
            class="col-lg-4 col-md-4 col-sm-6 col-xs-12 form-group"
            style="margin-top: 20px"
          >
            <label>Payment Owner *</label>
            <p-dropdown
            [disabled]="true"
            [options]="staffSelectList"
            optionLabel="name"
            optionValue="id"
            filterBy="firstname"
            placeholder="Select a staff"
            [filter]="true"
            formControlName="paymentOwnerId"
            [showClear]="true"
            styleClass="disableDropdown"
            >
            <ng-template let-data pTemplate="item">
              <div class="item-drop1">
                <span class="item-value1"> {{ data.name }} </span>
              </div>
            </ng-template>
            </p-dropdown>
            
            <button
            type="button"
            (click)="modalOpenSelectStaff('paymentCharge')"
            class="btn btn-primary"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
            <i class="fa fa-plus-square"></i>
            </button>
            <button
            [disabled]="paymentOwnerId == null"
            type="button"
            (click)="removeSelectStaff()"
            class="btn btn-danger"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
            <i class="fa fa-trash"></i>
            </button>
            <div></div>
            <div
              *ngIf="changePlansubmitted && planChangeForm.controls.paymentOwnerId.errors"
              class="errorWrap text-danger"
            >
              <div class="error text-danger">payment owner is required</div>
            </div>
          </div>

          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-group">
            <label>External Remark</label>
            <textarea class="form-control" formControlName="externalRemark"></textarea>
            <div
              *ngIf="changePlansubmitted && planChangeForm.controls.externalRemark.errors"
              class="errorWrap text-danger"
            >
              <div class="error text-danger">External Remark is required.</div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-group">
            <label>Remark*</label>
            <textarea class="form-control" formControlName="remarks"></textarea>
            <div
              *ngIf="changePlansubmitted && planChangeForm.controls.remarks.errors"
              class="errorWrap text-danger"
            >
              <div class="error text-danger">Remark is required.</div>
            </div>
          </div>
          <div style="text-align: center" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <button (click)="changePlan()" class="btn btn-primary" id="submit" type="submit">
              <i class="fa fa-check-circle"></i>
              Change Plan
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<app-customer-select
  *ngIf="showParentCustomerModel"
  [type]="customerSelectType"
  [custId]="customerLedgerDetailData.id"
  [selectedCust]="selectedParentCust"
  (selectedCustChange)="selectedCustChange($event)"
  (closeParentCust)="closeParentCust()"
></app-customer-select>

<app-staff-select-model
  *ngIf="showSelectStaffModel"
  (selectedStaffChange)="selectedStaffChange($event)"
  (closeSelectStaff)="closeSelectStaff()"
></app-staff-select-model>