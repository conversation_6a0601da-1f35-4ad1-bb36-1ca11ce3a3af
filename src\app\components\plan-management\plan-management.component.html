<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">Plan Management</h3>
        <div class="right">
          <button
            aria-controls="searchPlan"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPlan"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchPlan">
        <div class="panel-body">
          <div *ngIf="listView" class="row">
            <div class="col-lg-3 col-md-3 m-b-10">
              <p-dropdown
                (onChange)="selSearchOption($event)"
                [(ngModel)]="searchOption"
                [filter]="true"
                [options]="commondropdownService.planSearchOption"
                filterBy="label"
                optionLabel="label"
                optionValue="value"
                placeholder="Select a Search Option"
              ></p-dropdown>
            </div>
            <div
              *ngIf="
                searchOption != 'plantype' &&
                searchOption != 'planvalidity' &&
                searchOption != 'planstatus' &&
                searchOption != 'plancreatedby' &&
                searchOption != 'planstartdate' &&
                searchOption != 'planenddate' &&
                searchOption != 'plancreateddate'
              "
              class="col-lg-3 col-md-3 m-b-10"
            >
              <input
                [(ngModel)]="searchDeatil"
                class="form-control"
                id="username"
                placeholder="Enter Search Detail"
                type="text"
                (keydown.enter)="searchcustomer()"
              />
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'planstatus'">
              <p-dropdown
                [options]="planFilterStatus"
                optionValue="value"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Status"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'plancreatedby'">
              <p-dropdown
                [options]="commondropdownService.activeStaffList"
                optionValue="fullName"
                optionLabel="fullName"
                filter="true"
                filterBy="fullName"
                placeholder="Select a Created By Staff"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption === 'planvalidity'"
              style="display: flex"
            >
              <div style="width: 40%">
                <input
                  id="validity"
                  type="text"
                  min="1"
                  class="form-control"
                  placeholder="Enter Validity"
                  [(ngModel)]="searchDeatil"
                />
              </div>
              <div style="width: 60%; height: 34px">
                <p-dropdown
                  [options]="commondropdownService.validityUnitData"
                  optionValue="label"
                  optionLabel="label"
                  filter="true"
                  filterBy="label"
                  placeholder="Select a Unit"
                  [(ngModel)]="searchUnitsOfValidity"
                ></p-dropdown>
              </div>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption === 'planstartdate' || searchOption === 'planenddate'"
            >
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="searchDeatil"
                placeholder="Enter From Date"
              ></p-calendar>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'plancreateddate'">
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="searchDeatilFromDate"
                placeholder="Enter From Date"
              ></p-calendar>
            </div>

            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'plancreateddate'">
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [minDate]="searchDeatilFromDate"
                [(ngModel)]="searchDeatilToDate"
                placeholder="Enter To Date"
              ></p-calendar>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption === 'planname' || searchOption === 'plantype'"
            >
              <p-dropdown
                [options]="planTypeData"
                optionValue="value"
                optionLabel="text"
                filter="true"
                filterBy="text"
                placeholder="Select a Plan Type"
                [(ngModel)]="searchOptionType"
              ></p-dropdown>
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button (click)="searchPlan('')" class="btn btn-primary" id="searchbtn" type="button">
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                (click)="clearSearchPlan()"
                class="btn btn-default"
                id="searchbtn"
                type="reset"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>

        <div class="panel-body no-padding panel-udata">
          <div class="col-md-6 pcol" *ngIf="createAccess">
            <div class="dbox">
              <a (click)="checkExit('create')" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create Plan</h5>
                <!-- <p>Create Plan</p> -->
              </a>
            </div>
          </div>
          <div class="col-md-6 pcol">
            <div class="dbox">
              <a (click)="checkExit('list')" class="curson_pointer">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>List Plan</h5>
                <!-- <p>Search Plan</p> -->
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div *ngIf="createView" class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isPlanEdit ? "Update" : "Create" }} Plan</h3>
        <div class="right">
          <button
            aria-controls="createplan"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#createplan"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="createplan">
        <div class="panel-body">
          <div class="panel-body" *ngIf="createAccess || (isPlanEdit && editAccess)">
            <form [formGroup]="planGroupForm">
              <!--    Plan Information   -->
              <fieldset style="margin-top: 0px">
                <legend id="plan info">Plan Information</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Plan Name *</label>
                      <input
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.name.errors
                        }"
                        class="form-control"
                        formControlName="name"
                        id="name"
                        placeholder="Enter Plan Name"
                        type="text"
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.name.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.name.errors.required"
                          class="error text-danger"
                        >
                          Plan Name is required.
                        </div>
                        <div
                          class="position"
                          *ngIf="
                            submitted && planGroupForm.controls.name.errors?.cannotContainSpace
                          "
                        >
                          <p class="error">White space are not allowed.</p>
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Display Name *</label>
                      <input
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.displayName.errors
                        }"
                        class="form-control"
                        formControlName="displayName"
                        id="displayName"
                        placeholder="Enter Display Name"
                        type="text"
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.displayName.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.displayName.errors.required"
                          class="error text-danger"
                        >
                          Display Name is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Plan Code</label>
                      <input
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.code.errors
                        }"
                        class="form-control"
                        formControlName="code"
                        id="code"
                        placeholder="Enter Plan Code"
                        type="text"
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.code.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.code.errors.required"
                          class="error text-danger"
                        >
                          Plan Code is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label id="plan type">Plan Type *</label>
                      <p-dropdown
                        (onChange)="plantypeEvntData($event)"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.plantype.errors
                        }"
                        [options]="planTypeData"
                        filter="true"
                        filterBy="text"
                        formControlName="plantype"
                        optionLabel="text"
                        optionValue="value"
                        placeholder="Select a Plan Type"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.plantype.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.plantype.errors.required"
                          class="error text-danger"
                        >
                          Plan Type is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div>
                  <div *ngIf="!ifplanGroup_VB && !ifplanGroup_BWB">
                    <!--  || (!ifplanGroup_VB && ifplanGroup_BWB) -->
                    <div class="row">
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Plan Category *</label>
                        <p-dropdown
                          id="plan category"
                          (onChange)="selPlanCategory($event)"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.category.errors
                          }"
                          [options]="planCategoryData"
                          filter="true"
                          filterBy="text"
                          formControlName="category"
                          optionLabel="text"
                          optionValue="value"
                          placeholder="Select a Plan Category"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.category.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.category.errors.required"
                            class="error text-danger"
                          >
                            Plan Category is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Plan Mode *</label>
                        <p-dropdown
                          id="plan mode"
                          (onChange)="selPlanMode($event)"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.mode.errors
                          }"
                          [options]="type"
                          formControlName="mode"
                          optionLabel="label"
                          optionValue="label"
                          placeholder="Select a Plan Mode"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.mode.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.mode.errors.required"
                            class="error text-danger"
                          >
                            Plan Mode is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Plan Group *</label>
                        <p-dropdown
                          id="plan group"
                          (onChange)="selPlanGroup($event)"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.planGroup.errors
                          }"
                          [options]="planGroupData"
                          filter="true"
                          filterBy="text"
                          formControlName="planGroup"
                          optionLabel="text"
                          optionValue="value"
                          placeholder="Select a Plan Group"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.planGroup.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.planGroup.errors.required"
                            class="error text-danger"
                          >
                            Plan Group is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Service *</label>
                        <p-dropdown
                          id="service"
                          [disabled]="isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.serviceId.errors
                          }"
                          [options]="serviceData"
                          filter="true"
                          filterBy="name"
                          formControlName="serviceId"
                          optionLabel="name"
                          optionValue="id"
                          placeholder="Select a Service"
                          (onChange)="selService($event)"
                        ></p-dropdown>
                        <div></div>

                        <div *ngIf="!this.planGroupForm.value.plantype" class="error text-danger">
                          <div class="error text-danger">Please select Plan Type first!</div>
                        </div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.serviceId.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.serviceId.errors.required"
                            class="error text-danger"
                          >
                            Service is required.
                          </div>
                        </div>
                        <br />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Service Area *</label>

                        <p-multiSelect
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.serviceAreaIds.errors
                          }"
                          [options]="commondropdownService.serviceAreaList"
                          defaultLabel="Select Area"
                          formControlName="serviceAreaIds"
                          id="roles"
                          optionLabel="name"
                          optionValue="id"
                        >
                          <ng-template let-option pTemplate="item">
                            <span>
                              {{ option.name }}
                              &nbsp;
                              <span
                                *ngIf="option.isUnderDevelopment"
                                class="badge badge-info underDevelopBadge"
                              >
                                UnderDevelopment
                              </span>
                            </span>
                          </ng-template>
                        </p-multiSelect>
                        <div
                          *ngIf="submitted && planGroupForm.controls.serviceAreaIds.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.serviceAreaIds.errors.required
                            "
                            class="error text-danger"
                          >
                            Service Area is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Accessibility</label>

                        <p-dropdown
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.accessibility.errors
                          }"
                          [options]="accessibilityData"
                          id="roles"
                          optionLabel="text"
                          optionValue="value"
                          filter="true"
                          filterBy="text"
                          placeholder="Select Accessibility"
                          formControlName="accessibility"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.accessibility.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.accessibility.errors.required
                            "
                            class="error text-danger"
                          >
                            Accessibility is required.
                          </div>
                        </div>
                        <br />
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Start Date *</label>
                        <input
                          id="startdate"
                          (change)="dateDisble($event)"
                          [max]="maxDisbleDate"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.startDate.errors
                          }"
                          class="form-control"
                          formControlName="startDate"
                          placeholder="DD/MM/YYYY"
                          type="date"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.startDate.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.startDate.errors.required"
                            class="error text-danger"
                          >
                            Start Date is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>End Date *</label>
                        <input
                          id="enddate"
                          (change)="dateMaxDisble($event)"
                          [min]="disbleDate"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.endDate.errors
                          }"
                          class="form-control"
                          formControlName="endDate"
                          placeholder="DD/MM/YYYY"
                          type="date"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.endDate.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.endDate.errors.required"
                            class="error text-danger"
                          >
                            End Date is required.
                          </div>
                        </div>
                        <br />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Validity *</label>
                        <div style="display: flex">
                          <div style="width: 60%">
                            <input
                              id="validity"
                              [ngClass]="{
                                'is-invalid': submitted && planGroupForm.controls.validity.errors
                              }"
                              [readonly]="(this.ifPlanEditInput && isPlanEdit) || this.validityHide"
                              class="form-control"
                              formControlName="validity"
                              id="validity"
                              min="1"
                              (keydown)="preventNegative($event)"
                              placeholder="Enter Validity"
                              type="number"
                            />
                            <div
                              *ngIf="submitted && planGroupForm.controls.validity.errors"
                              class="errorWrap text-danger"
                            >
                              <div
                                *ngIf="submitted && planGroupForm.controls.validity.errors.required"
                                class="error text-danger"
                              >
                                Validity is required.
                              </div>
                              <div
                                *ngIf="submitted && planGroupForm.controls.validity.errors.pattern"
                                class="error text-danger"
                              >
                                Only Numeric value are allowed.
                              </div>
                            </div>
                          </div>
                          <div style="width: 40%; height: 34px">
                            <p-dropdown
                              [disabled]="(this.ifPlanEditInput && isPlanEdit) || this.validityHide"
                              [ngClass]="{
                                'is-invalid':
                                  submitted && planGroupForm.controls.unitsOfValidity.errors
                              }"
                              [options]="validityUnit"
                              filter="true"
                              filterBy="label"
                              formControlName="unitsOfValidity"
                              optionLabel="label"
                              optionValue="label"
                              placeholder="Select Unit"
                            ></p-dropdown>

                            <div></div>
                            <div
                              *ngIf="submitted && planGroupForm.controls.unitsOfValidity.errors"
                              class="errorWrap text-danger"
                            >
                              <div
                                *ngIf="
                                  submitted &&
                                  planGroupForm.controls.unitsOfValidity.errors.required
                                "
                                class="error text-danger"
                              >
                                Unit is required.
                              </div>
                            </div>
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Allow Discount *</label>

                        <p-dropdown
                          id="discount"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.allowdiscount.errors
                          }"
                          [options]="planDiscount"
                          formControlName="allowdiscount"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Discount"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.allowdiscount.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.allowdiscount.errors.required
                            "
                            class="error text-danger"
                          >
                            Allow Discount is required.
                          </div>
                        </div>
                        <br />
                        <br />
                      </div>

                      <div
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                        *ngIf="planGroupForm.value.category == 'Business Promotion'"
                      >
                        <label>Invoice To Org: *</label>

                        <p-dropdown
                          id="invoice"
                          [disabled]="editMode"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.invoiceToOrg.errors
                          }"
                          [options]="planDiscount"
                          filter="true"
                          filterBy="label"
                          formControlName="invoiceToOrg"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select Invoice to org or not"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.invoiceToOrg.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.invoiceToOrg.errors.required"
                            class="error text-danger"
                          >
                            Invoice To Org is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                        *ngIf="planGroupForm.value.category == 'Business Promotion'"
                      >
                        <label>Required Approval *</label>

                        <p-dropdown
                          id="required"
                          [disabled]="editMode"
                          [ngClass]="{
                            'is-invalid':
                              submitted && planGroupForm.controls.requiredApproval.errors
                          }"
                          [options]="planDiscount"
                          filter="true"
                          filterBy="label"
                          formControlName="requiredApproval"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Required Approval"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.requiredApproval.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.requiredApproval.errors.required
                            "
                            class="error text-danger"
                          >
                            Required Approval is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div
                        *ngIf="planGroupForm.value.category !== 'Business Promotion'"
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                      >
                        <label>Allow Over Usage *</label>
                        <p-dropdown
                          id="allow"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.allowOverUsage.errors
                          }"
                          [options]="planCategory"
                          filter="true"
                          filterBy="label"
                          formControlName="allowOverUsage"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Plan Category"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.allowOverUsage.errors.required
                            "
                            class="error text-danger"
                          >
                            Allow Over Usage is required.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div
                        *ngIf="planGroupForm.value.category !== 'Business Promotion'"
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                      >
                        <label>Max Current Session *</label>
                        <input
                          [readonly]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid':
                              submitted && planGroupForm.controls.maxconcurrentsession.errors
                          }"
                          class="form-control"
                          formControlName="maxconcurrentsession"
                          id="maxconcurrentsession"
                          min="1"
                          placeholder="Enter Max Current Session"
                          type="number"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.maxconcurrentsession.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted &&
                              planGroupForm.controls.maxconcurrentsession.errors.required
                            "
                            class="error text-danger"
                          >
                            Max Current Session is required.
                          </div>
                          <div
                            *ngIf="
                              submitted &&
                              planGroupForm.controls.maxconcurrentsession.errors.pattern
                            "
                            class="error text-danger"
                          >
                            Only Numeric Character Allowed.
                          </div>
                        </div>
                        <br />
                      </div>
                    </div>
                    <div class="row">
                      <div
                        *ngIf="planGroupForm.value.category == 'Business Promotion'"
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                      >
                        <label>Allow Over Usage *</label>
                        <p-dropdown
                          id="allowover"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.allowOverUsage.errors
                          }"
                          [options]="planCategory"
                          filter="true"
                          filterBy="label"
                          formControlName="allowOverUsage"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Plan Category"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.allowOverUsage.errors.required
                            "
                            class="error text-danger"
                          >
                            Allow Over Usage is required.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div
                        *ngIf="planGroupForm.value.category == 'Business Promotion'"
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                      >
                        <label>Max Current Session *</label>
                        <input
                          [readonly]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid':
                              submitted && planGroupForm.controls.maxconcurrentsession.errors
                          }"
                          class="form-control"
                          formControlName="maxconcurrentsession"
                          id="maxconcurrentsession"
                          min="1"
                          placeholder="Enter Max Current Session"
                          type="number"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.maxconcurrentsession.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted &&
                              planGroupForm.controls.maxconcurrentsession.errors.required
                            "
                            class="error text-danger"
                          >
                            is required.
                          </div>
                          <div
                            *ngIf="
                              submitted &&
                              planGroupForm.controls.maxconcurrentsession.errors.pattern
                            "
                            class="error text-danger"
                          >
                            Only Numeric Character Allowed.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Template</label>

                        <p-dropdown
                          id="temp"
                          [disabled]="editMode"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.templateId.errors
                          }"
                          [options]="templateData"
                          filter="true"
                          filterBy="templatename"
                          formControlName="templateId"
                          optionLabel="templatename"
                          optionValue="id"
                          placeholder="Select a Template"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.templateId.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.templateId.errors.required"
                            class="error text-danger"
                          >
                            Template is required.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div
                        class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15"
                        style="display: initial"
                      >
                        <div
                          class="form-group form-check inputcheckboxCenter"
                          style="padding-top: 25px"
                        >
                          <input
                            class="inputcheckbox"
                            id="useQuota"
                            formControlName="useQuota"
                            type="checkbox"
                            (change)="useQuotaChnage($event)"
                          />
                          <label
                            class="form-check-label"
                            for="acceptTerms"
                            style="margin-left: 2rem; margin-bottom: 0"
                            >Use Quota
                          </label>
                        </div>
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Chunk</label>
                        <input
                          [readonly]="!planGroupForm.value.useQuota"
                          class="form-control"
                          formControlName="chunk"
                          id="chunk"
                          min="0"
                          placeholder="Enter Chunk"
                          type="number"
                        />
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Max Hold Days</label>
                        <!-- [readonly]="this.ifPlanEditInput && isPlanEdit" -->
                        <input
                          [ngClass]="{
                            'is-invalid':
                              submitted && planGroupForm.controls.maxHoldDurationDays.errors
                          }"
                          class="form-control"
                          formControlName="maxHoldDurationDays"
                          id="maxHoldDurationDays"
                          min="1"
                          placeholder="Enter Max Hold Days"
                          type="number"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.maxHoldDurationDays.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted &&
                              planGroupForm.controls.maxHoldDurationDays.errors.required
                            "
                            class="error text-danger"
                          >
                            Max Hold Days is required.
                          </div>
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.maxHoldDurationDays.errors.pattern
                            "
                            class="error text-danger"
                          >
                            Only Numeric Character Allowed.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Max Hold Attempts</label>
                        <!-- [readonly]="this.ifPlanEditInput && isPlanEdit" -->
                        <input
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.maxHoldAttempts.errors
                          }"
                          class="form-control"
                          formControlName="maxHoldAttempts"
                          id="maxHoldAttempts"
                          min="1"
                          placeholder="Enter Max Hold Attempts"
                          type="number"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.maxHoldAttempts.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.maxHoldAttempts.errors.required
                            "
                            class="error text-danger"
                          >
                            Max Hold Attempts is required.
                          </div>
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.maxHoldAttempts.errors.pattern
                            "
                            class="error text-danger"
                          >
                            Only Numeric Character Allowed.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div
                        *ngIf="planGroupForm.value.category !== 'Business Promotion'"
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12 left"
                      >
                        <label>Description *</label>
                        <textarea
                          id="desp"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.desc.errors
                          }"
                          class="form-control"
                          formControlName="desc"
                          placeholder="Enter Description"
                        ></textarea>
                        <div
                          *ngIf="submitted && planGroupForm.controls.desc.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                            class="error text-danger"
                          >
                            Description is required.
                          </div>
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                            class="error text-danger"
                          >
                            Maximum 255 charecter required.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div
                        *ngIf="planGroupForm.value.category == 'Business Promotion'"
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12 left"
                      >
                        <label>Description *</label>
                        <textarea
                          id="description"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.desc.errors
                          }"
                          class="form-control"
                          formControlName="desc"
                          placeholder="Enter Description"
                        ></textarea>
                        <div
                          *ngIf="submitted && planGroupForm.controls.desc.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                            class="error text-danger"
                          >
                            Description is required.
                          </div>
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                            class="error text-danger"
                          >
                            Maximum 255 charecter required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div *ngIf="mvnoId === 1" class="col-lg-3 col-md-3 col-sm-6 col-xs-12 mb-15">
                        <label>{{ mvnoTitle }} List*</label>
                        <p-dropdown
                          id="mvnoId"
                          [disabled]="isPlanEdit"
                          [options]="commondropdownService.mvnoList"
                          filter="true"
                          filterBy="name"
                          formControlName="mvnoId"
                          optionLabel="name"
                          optionValue="id"
                          placeholder="Select a mvno"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.mvnoId.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.mvnoId.errors.required"
                            class="error text-danger"
                          >
                            Mvno is required.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Status *</label>
                        <p-dropdown
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.status.errors
                          }"
                          [options]="planStatus"
                          filter="true"
                          filterBy="label"
                          formControlName="status"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Status"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.status.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.status.errors.required"
                            class="error text-danger"
                          >
                            status is required.
                          </div>
                        </div>
                        <br />
                      </div> -->

                  <!-- <div
                    class="col-lg-3 col-md-6 col-sm-6 col-xs-12 left"
                    *ngIf="
                      planGroupForm.value.category !== 'Business Promotion' && isServiceHideField
                    "
                  >
                    <label>Description *</label>
                    <textarea
                      [ngClass]="{
                        'is-invalid': submitted && planGroupForm.controls.desc.errors
                      }"
                      class="form-control"
                      formControlName="desc"
                      placeholder="Enter Description"
                    ></textarea>
                    <div
                      *ngIf="submitted && planGroupForm.controls.desc.errors"
                      class="errorWrap text-danger"
                    >
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                        class="error text-danger"
                      >
                        Description is required.
                      </div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                        class="error text-danger"
                      >
                        Maximum 255 charecter required.
                      </div>
                    </div>
                    <br />
                  </div> -->
                  <!-- 
                  <div class="row">
                    <div
                      class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                      *ngIf="planGroupForm.value.category == 'Business Promotion'"
                    >
                      <label>Required Approval *</label>

                      <p-dropdown
                        [disabled]="editMode"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.requiredApproval.errors
                        }"
                        [options]="planDiscount"
                        filter="true"
                        filterBy="label"
                        formControlName="requiredApproval"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select a Required Approval"
                      ></p-dropdown>
                      <div
                        *ngIf="submitted && planGroupForm.controls.requiredApproval.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            submitted && planGroupForm.controls.requiredApproval.errors.required
                          "
                          class="error text-danger"
                        >
                          Required Approval is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div> -->

                  <!-- <div
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                        *ngIf="planGroupForm.value.planGroup == 'DTV Addon'"
                      >
                        <label> Require Base Plan *</label>
                        <p-dropdown
                          [options]="RequireBasePlan"
                          filter="true"
                          filterBy="value"
                          formControlName="basePlan"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select Base Plan"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.basePlan.errors
                          }"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.basePlan.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.basePlan.errors.required"
                            class="error text-danger"
                          >
                            Require Base Plan is required.
                          </div>
                        </div>
                        <br />
                      </div> -->

                  <div *ngIf="ifplanGroup_VB || ifplanGroup_BWB">
                    <div class="row">
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Plan Category *</label>
                        <p-dropdown
                          id="plan"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.category.errors
                          }"
                          [options]="planCategoryData"
                          filter="true"
                          filterBy="text"
                          formControlName="category"
                          optionLabel="text"
                          optionValue="value"
                          placeholder="Select a Plan Category"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.category.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.category.errors.required"
                            class="error text-danger"
                          >
                            Plan Category is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Plan Group *</label>
                        <p-dropdown
                          id="plangp"
                          (onChange)="selPlanGroup($event)"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.planGroup.errors
                          }"
                          [options]="planGroupData"
                          filter="true"
                          filterBy="text"
                          formControlName="planGroup"
                          optionLabel="text"
                          optionValue="value"
                          placeholder="Select a Plan Group"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.planGroup.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.planGroup.errors.required"
                            class="error text-danger"
                          >
                            Plan Group is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Service *</label>
                        <p-dropdown
                          id="service"
                          [disabled]="isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.serviceId.errors
                          }"
                          [options]="serviceData"
                          filter="true"
                          filterBy="name"
                          formControlName="serviceId"
                          optionLabel="name"
                          optionValue="id"
                          placeholder="Select a Service"
                          (onChange)="selService($event)"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.serviceId.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.serviceId.errors.required"
                            class="error text-danger"
                          >
                            Service is required.
                          </div>
                        </div>

                        <div *ngIf="!this.planGroupForm.value.plantype" class="error text-danger">
                          <div class="error text-danger">Please select Plan Type first!</div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Service Area *</label>

                        <p-multiSelect
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.serviceAreaIds.errors
                          }"
                          [options]="commondropdownService.serviceAreaList"
                          defaultLabel="Select Area"
                          formControlName="serviceAreaIds"
                          id="roles"
                          optionLabel="name"
                          optionValue="id"
                          (onChange)="selServiceArea($event)"
                        ></p-multiSelect>
                        <div
                          *ngIf="submitted && planGroupForm.controls.serviceAreaIds.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.serviceAreaIds.errors.required
                            "
                            class="error text-danger"
                          >
                            Service Area is required.
                          </div>
                        </div>
                        <br />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Accessibility</label>

                        <p-dropdown
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.accessibility.errors
                          }"
                          [options]="accessibilityData"
                          id="roles"
                          optionLabel="text"
                          optionValue="value"
                          filter="true"
                          filterBy="text"
                          placeholder="Select Accessibility"
                          formControlName="accessibility"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.accessibility.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.accessibility.errors.required
                            "
                            class="error text-danger"
                          >
                            Accessibility is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Start Date *</label>
                        <input
                          id="startdate"
                          (change)="dateDisble($event)"
                          [max]="maxDisbleDate"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.startDate.errors
                          }"
                          class="form-control"
                          formControlName="startDate"
                          placeholder="DD/MM/YYYY"
                          type="date"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.startDate.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.startDate.errors.required"
                            class="error text-danger"
                          >
                            Start Date is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>End Date *</label>
                        <input
                          id="enddate"
                          (change)="dateMaxDisble($event)"
                          [min]="disbleDate"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.endDate.errors
                          }"
                          class="form-control"
                          formControlName="endDate"
                          placeholder="DD/MM/YYYY"
                          type="date"
                        />
                        <div
                          *ngIf="submitted && planGroupForm.controls.endDate.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.endDate.errors.required"
                            class="error text-danger"
                          >
                            End Date is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Validity *</label>
                        <div style="display: flex">
                          <div style="width: 60%">
                            <input
                              [ngClass]="{
                                'is-invalid': submitted && planGroupForm.controls.validity.errors
                              }"
                              [readonly]="this.ifPlanEditInput && isPlanEdit"
                              class="form-control"
                              formControlName="validity"
                              id="validity"
                              min="1"
                              (keydown)="preventNegative($event)"
                              placeholder="Enter Validity"
                              readonly
                              type="number"
                            />
                            <div
                              *ngIf="submitted && planGroupForm.controls.validity.errors"
                              class="errorWrap text-danger"
                            >
                              <div
                                *ngIf="submitted && planGroupForm.controls.validity.errors.required"
                                class="error text-danger"
                              >
                                Validity is required.
                              </div>
                              <div
                                *ngIf="submitted && planGroupForm.controls.validity.errors.pattern"
                                class="error text-danger"
                              >
                                Only Numeric value are allowed.
                              </div>
                            </div>
                          </div>
                          <div style="width: 40%; height: 34px">
                            <p-dropdown
                              [ngClass]="{
                                'is-invalid':
                                  submitted && planGroupForm.controls.unitsOfValidity.errors
                              }"
                              [options]="validityUnit"
                              filter="true"
                              filterBy="label"
                              formControlName="unitsOfValidity"
                              optionLabel="label"
                              optionValue="label"
                              placeholder="Select Unit"
                            ></p-dropdown>

                            <div></div>
                            <div
                              *ngIf="submitted && planGroupForm.controls.unitsOfValidity.errors"
                              class="errorWrap text-danger"
                            >
                              <div
                                *ngIf="
                                  submitted &&
                                  planGroupForm.controls.unitsOfValidity.errors.required
                                "
                                class="error text-danger"
                              >
                                Unit is required.
                              </div>
                            </div>
                          </div>
                        </div>
                        <br />
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Allow Discount *</label>

                        <p-dropdown
                          id="disc"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.allowdiscount.errors
                          }"
                          [options]="planDiscount"
                          filter="true"
                          filterBy="label"
                          formControlName="allowdiscount"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Discount"
                        ></p-dropdown>
                        <div
                          *ngIf="submitted && planGroupForm.controls.allowdiscount.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.allowdiscount.errors.required
                            "
                            class="error text-danger"
                          >
                            Discount is required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <!-- <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                        <label>Status *</label>
                        <p-dropdown
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.status.errors
                          }"
                          [options]="statusOptions"
                          filter="true"
                          filterBy="label"
                          formControlName="status"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Status"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.status.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.status.errors.required"
                            class="error text-danger"
                          >
                            status is required.
                          </div>
                        </div>
                        <br />
                      </div> -->

                      <div
                        *ngIf="
                          planGroupForm.value.category == 'Business Promotion' &&
                          !isServiceHideField
                        "
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                      >
                        <label>Allow Over Usage *</label>
                        <p-dropdown
                          id="allowOverUsage"
                          [disabled]="this.ifPlanEditInput && isPlanEdit"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.allowOverUsage.errors
                          }"
                          [options]="planCategory"
                          filter="true"
                          filterBy="label"
                          formControlName="allowOverUsage"
                          optionLabel="label"
                          optionValue="value"
                          placeholder="Select a Plan Category"
                        ></p-dropdown>
                        <div></div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.allowOverUsage.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              submitted && planGroupForm.controls.allowOverUsage.errors.required
                            "
                            class="error text-danger"
                          >
                            Allow Over Usage is required.
                          </div>
                        </div>
                        <br />
                      </div>

                      <div
                        class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                        *ngIf="planGroupForm.value.category !== 'Business Promotion'"
                      >
                        <label>Description *</label>
                        <textarea
                          id="desc1"
                          [ngClass]="{
                            'is-invalid': submitted && planGroupForm.controls.desc.errors
                          }"
                          class="form-control"
                          formControlName="desc"
                          placeholder="Enter Description"
                        ></textarea>
                        <div
                          *ngIf="submitted && planGroupForm.controls.desc.errors"
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                            class="error text-danger"
                          >
                            Description is required.
                          </div>
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                            class="error text-danger"
                          >
                            Maximum 255 charecter required.
                          </div>
                        </div>
                        <br />
                      </div>
                      <div class="row">
                        <div
                          class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                          *ngIf="planGroupForm.value.category == 'Business Promotion'"
                        >
                          <label>Description *</label>
                          <textarea
                            id="desc2"
                            [ngClass]="{
                              'is-invalid': submitted && planGroupForm.controls.desc.errors
                            }"
                            class="form-control"
                            formControlName="desc"
                            placeholder="Enter Description"
                          ></textarea>
                          <div
                            *ngIf="submitted && planGroupForm.controls.desc.errors"
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="submitted && planGroupForm.controls.desc.errors.required"
                              class="error text-danger"
                            >
                              Description is required.
                            </div>
                            <div
                              *ngIf="submitted && planGroupForm.controls.desc.errors.pattern"
                              class="error text-danger"
                            >
                              Maximum 255 charecter required.
                            </div>
                          </div>
                        </div>
                        <div
                          class="col-lg-3 col-md-6 col-sm-6 col-xs-12 mb-15"
                          style="display: initial"
                          *ngIf="planGroupForm.value.planGroup == 'Bandwidthbooster'"
                        >
                          <div
                            class="form-group form-check inputcheckboxCenter"
                            style="padding-top: 25px"
                          >
                            <p-checkbox
                              binary="true"
                              class="checkbox-align"
                              formControlName="addonToBase"
                              name="addonToBase"
                            ></p-checkbox>
                            <label
                              class="form-check-label"
                              for="acceptTerms"
                              style="margin-left: 2rem; margin-bottom: 0"
                              >Add on To Base *</label
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </fieldset>

              <!--    Quota Details   -->
              <fieldset *ngIf="!isServiceHideField">
                <legend>Quota Details</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                      <!-- *ngIf="!ifplanGroup_BWB" -->
                      <label>Quota Type *</label>
                      <p-dropdown
                        id="quotatype"
                        (onChange)="getSelQoutaType($event)"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.quotatype.errors
                        }"
                        [options]="quotaTypeData"
                        filter="true"
                        filterBy="text"
                        formControlName="quotatype"
                        optionLabel="text"
                        optionValue="value"
                        placeholder="Select a Quota Type"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotatype.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotatype.errors.required"
                          class="error text-danger"
                        >
                          Quota Type is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div
                      *ngIf="!isServiceHideField && !ifplanGroup_VB"
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                    >
                      <label>Base Qos Policy*</label>
                      <p-dropdown
                        id="qos"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.qospolicyid.errors
                        }"
                        [options]="qosPolicyData"
                        filter="true"
                        filterBy="name"
                        formControlName="qospolicyid"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a Base Qos Policy"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.qospolicyid.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.qospolicyid.errors.required"
                          class="error text-danger"
                        >
                          Base Qos Policy is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div
                      *ngIf="this.planGroupForm.controls.quotaUnit.enabled"
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                    >
                      <!--  && !ifplanGroup_BWB -->
                      <label>Quota Unit *</label>
                      <p-dropdown
                        id="unit"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.quotaUnit.errors
                        }"
                        [options]="quotaData"
                        filter="true"
                        filterBy="text"
                        formControlName="quotaUnit"
                        optionLabel="text"
                        optionValue="value"
                        placeholder="Select a Quota Unit"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotaUnit.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotaUnit.errors.required"
                          class="error text-danger"
                        >
                          Quota Unit is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div *ngIf="this.isQuotaEnabled" class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                      <!--  && !ifplanGroup_BWB -->
                      <label>Quota *</label>
                      <input
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.quota.errors
                        }"
                        [readonly]="this.ifPlanEditInput && isPlanEdit"
                        class="form-control"
                        formControlName="quota"
                        id="Quota"
                        min="0"
                        pInputText
                        placeholder="Enter Quota"
                        type="number"
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.quota.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.quota.errors.required"
                          class="error text-danger"
                        >
                          Quota is required.
                        </div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.quota.errors.pattern"
                          class="error text-danger"
                        >
                          Only Numeric Value allowed.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div
                      *ngIf="this.planGroupForm.controls.quotaunittime.enabled"
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                    >
                      <!--  && !ifplanGroup_BWB -->
                      <label>Quota Unit Time *</label>
                      <p-dropdown
                        id="quotaunittime"
                        (onChange)="quotaUnitChange($event)"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.quotaunittime.errors
                        }"
                        [options]="qutaUnitTime"
                        filter="true"
                        filterBy="label"
                        formControlName="quotaunittime"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select a Quota Unit Time"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotaunittime.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotaunittime.errors.required"
                          class="error text-danger"
                        >
                          Quota Unit Time is required.
                        </div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotatime.errors.pattern"
                          class="error text-danger"
                        >
                          Only Numeric Value allowed.
                        </div>
                      </div>
                      <br />
                    </div>

                    <div
                      *ngIf="
                        this.isQuotaTimeEnabled &&
                        this.planGroupForm.value.quotatype !== 'Both' &&
                        this.planGroupForm.value.quotatype
                      "
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                    >
                      <!-- &&
                        !ifplanGroup_BWB -->
                      <label>Quota Time *</label>
                      <input
                        [max]="max"
                        [min]="1"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.quotatime.errors
                        }"
                        [readonly]="this.ifPlanEditInput && isPlanEdit"
                        class="form-control"
                        formControlName="quotatime"
                        id="quotatime"
                        pInputText
                        placeholder="Enter Quota Time"
                        type="number"
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotatime.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotatime.errors.required"
                          class="error text-danger"
                        >
                          Quota Time is required.
                        </div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotatime.errors.pattern"
                          class="error text-danger"
                        >
                          Only Numeric Value allowed.
                        </div>
                      </div>
                      <br />
                    </div>

                    <div
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                      *ngIf="this.planGroupForm.value.quotatype !== 'Both'"
                    >
                      <!--  && !ifplanGroup_BWB -->
                      <label>Quota Reset Interval *</label>
                      <p-dropdown
                        id="reset"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid':
                            submitted && planGroupForm.controls.quotaResetInterval.errors
                        }"
                        [options]="quotaResetIntervalData"
                        filter="true"
                        filterBy="label"
                        formControlName="quotaResetInterval"
                        optionLabel="label"
                        optionValue="label"
                        placeholder="Select a Interval"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotaResetInterval.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            submitted && planGroupForm.controls.quotaResetInterval.errors.required
                          "
                          class="error text-danger"
                        >
                          Quota Type is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <!-- Quota Usage Type-->
                    <div
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                      *ngIf="this.planGroupForm.value.quotatype !== 'Both'"
                    >
                      <!--  && !ifplanGroup_BWB -->
                      <label>Quota Usage Type *</label>
                      <p-dropdown
                        id="reset"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.usageQuotaType.errors
                        }"
                        [options]="usageQuotaType"
                        filter="true"
                        filterBy="label"
                        formControlName="usageQuotaType"
                        optionLabel="label"
                        optionValue="label"
                        placeholder="Select a Interval"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.usageQuotaType.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.usageQuotaType.errors.required"
                          class="error text-danger"
                        >
                          Quota Type is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <!-- Quota usage type-->
                    <div
                      *ngIf="this.planGroupForm.value.quotatype == 'Both'"
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                    >
                      <!--  && !ifplanGroup_BWB -->
                      <label>Quota Time *</label>
                      <input
                        [max]="max"
                        [min]="1"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.quotatime.errors
                        }"
                        [readonly]="this.ifPlanEditInput && isPlanEdit"
                        class="form-control"
                        formControlName="quotatime"
                        id="quotatime"
                        pInputText
                        placeholder="Enter Quota Time"
                        type="number"
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotatime.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotatime.errors.required"
                          class="error text-danger"
                        >
                          Quota Time is required.
                        </div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.quotatime.errors.pattern"
                          class="error text-danger"
                        >
                          Only Numeric Value allowed.
                        </div>
                      </div>
                      <br />
                    </div>

                    <div
                      class="col-lg-4 col-md-6 col-sm-6 col-xs-12"
                      *ngIf="this.planGroupForm.value.quotatype == 'Both'"
                    >
                      <!-- && !ifplanGroup_BWB -->
                      <label>Quota Reset Interval *</label>
                      <p-dropdown
                        id="reset interval"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid':
                            submitted && planGroupForm.controls.quotaResetInterval.errors
                        }"
                        [options]="quotaResetIntervalData"
                        filter="true"
                        filterBy="label"
                        formControlName="quotaResetInterval"
                        optionLabel="label"
                        optionValue="label"
                        placeholder="Select a Interval"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.quotaResetInterval.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            submitted && planGroupForm.controls.quotaResetInterval.errors.required
                          "
                          class="error text-danger"
                        >
                          Quota Type is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div>
                </div>
              </fieldset>

              <!--    Overusage Quota QoS Policy   -->
              <fieldset
                *ngIf="
                  planGroupForm.value.qospolicyid !== null && planGroupForm.value.qospolicyid !== ''
                "
              >
                <legend>Overusage Quota QoS Policy</legend>
                <div class="boxWhite">
                  <div [formGroup]="qospolicyformgroup" class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <input
                        class="form-control"
                        formControlName="frompercentage"
                        id="frompercentage"
                        placeholder="Enter From(%)"
                        type="text"
                        (change)="setToValidation()"
                      />
                      <div
                        *ngIf="
                          qospolicySubmitted && qospolicyformgroup.controls.frompercentage.errors
                        "
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            qospolicySubmitted &&
                            qospolicyformgroup.controls.frompercentage.errors.required
                          "
                          class="error text-danger"
                        >
                          From (%) is required.
                        </div>
                        <div
                          *ngIf="
                            qospolicySubmitted &&
                            qospolicyformgroup.controls.frompercentage.errors.min
                          "
                          class="error text-danger"
                        >
                          From (%) should be greater than {{ minfrompercentage }}.
                        </div>
                      </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <input
                        class="form-control"
                        formControlName="topercentage"
                        id="topercentage"
                        placeholder="Enter To(%)"
                        type="text"
                      />
                      <div
                        *ngIf="
                          qospolicySubmitted && qospolicyformgroup.controls.topercentage.errors
                        "
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            qospolicySubmitted &&
                            qospolicyformgroup.controls.topercentage.errors.required
                          "
                          class="error text-danger"
                        >
                          To (%) is required.
                        </div>
                        <div
                          *ngIf="
                            qospolicySubmitted &&
                            qospolicyformgroup.controls.topercentage.errors.min
                          "
                          class="error text-danger"
                        >
                          To (%) should be greater than {{ mintopercentage }}.
                        </div>
                      </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <p-dropdown
                        [ngClass]="{
                          'is-invalid':
                            qospolicySubmitted && qospolicyformgroup.controls.qosid.errors
                        }"
                        [options]="qosPolicyData"
                        filter="true"
                        filterBy="name"
                        formControlName="qosid"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a Qos Policy"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="qospolicySubmitted && qospolicyformgroup.controls.qosid.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            qospolicySubmitted && qospolicyformgroup.controls.qosid.errors.required
                          "
                          class="error text-danger"
                        >
                          Qos Policy is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <button
                        (click)="onAddQosPolicy()"
                        class="btn btn-primary"
                        id="addAtt"
                        style="object-fit: cover; padding: 5px 8px"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                      >
                        <i aria-hidden="true" class="fa fa-plus-square"></i>
                        Add
                      </button>
                    </div>
                  </div>
                  <div class="scrollbarPlangroupMappingList" style="margin-top: 15px">
                    <table class="table coa-table">
                      <thead>
                        <tr>
                          <th style="text-align: center">From (%)</th>
                          <th style="text-align: center">To (%)</th>
                          <th style="text-align: center">QoS Policy</th>
                          <th style="text-align: right; padding: 8px">Delete</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let row of qospolicyformArray.controls
                              | paginate
                                : {
                                    id: 'qospolicyformArrayData',
                                    itemsPerPage: qospolicyitemsPerPage,
                                    currentPage: currentPageqospolicye,
                                    totalItems: qospolicytotalRecords
                                  };
                            let index = index
                          "
                        >
                          <td style="padding-left: 8px">
                            <input
                              class="form-control"
                              [formControl]="row.get('frompercentage')"
                              id="frompercentage"
                              placeholder="Enter From(%)"
                              type="text"
                            />
                          </td>
                          <td>
                            <input
                              class="form-control"
                              [formControl]="row.get('topercentage')"
                              id="topercentage"
                              placeholder="Enter To(%)"
                              type="text"
                            />
                          </td>
                          <td style="padding-left: 8px">
                            <p-dropdown
                              [disabled]="true"
                              [formControl]="row.get('qosid')"
                              [options]="qosPolicyData"
                              filter="true"
                              filterBy="name"
                              optionLabel="name"
                              optionValue="id"
                              placeholder="Select a Qos Policy"
                            ></p-dropdown>
                            <div></div>
                          </td>
                          <td style="text-align: right">
                            <button
                              (click)="deleteConfirmonQosPolicy(index)"
                              id="deleteAtt"
                              [disabled]="this.ifPlanEditInput && isPlanEdit"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              type="button"
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </fieldset>

              <!--    Additional inforamation   -->
              <fieldset *ngIf="!ifplanGroup_BWB">
                <legend id="additional">Additional Information</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>SAC Code</label>
                      <input
                        class="form-control"
                        formControlName="saccode"
                        id="saccode"
                        placeholder="Enter SAC Code"
                        type="text"
                      />
                      <br />
                    </div>
                    <div
                      *ngIf="!isServiceHideField && !ifplanGroup_VB"
                      class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    >
                      <label>TimeBase Policy</label>
                      <p-dropdown
                        id="policy"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.timebasepolicyId.errors
                        }"
                        [options]="timeBasePolicyData"
                        filter="true"
                        filterBy="name"
                        formControlName="timebasepolicyId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select a TimeBase Policy"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="submitted && planGroupForm.controls.timebasepolicyId.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            submitted && planGroupForm.controls.timebasepolicyId.errors.required
                          "
                          class="error text-danger"
                        >
                          TimeBase Policy is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Param 1</label>
                      <input
                        class="form-control"
                        formControlName="param1"
                        id="param1"
                        placeholder="Enter Param 1"
                        type="text"
                      />
                      <br />
                    </div>
                    <div *ngIf="isServiceHideField" class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Param 2</label>
                      <input
                        class="form-control"
                        formControlName="param2"
                        id="param2"
                        placeholder="Enter Param 2"
                        type="text"
                      />
                      <br />
                    </div>
                    <div *ngIf="isServiceHideField" class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Param 3</label>
                      <input
                        class="form-control"
                        formControlName="param3"
                        id="param3"
                        placeholder="Enter Param 3"
                        type="text"
                      />
                      <br />
                    </div>
                  </div>
                  <div class="row">
                    <div *ngIf="!isServiceHideField" class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Param 2</label>
                      <input
                        class="form-control"
                        formControlName="param2"
                        id="param2"
                        placeholder="Enter Param 2"
                        type="text"
                      />
                      <br />
                    </div>
                    <div *ngIf="!isServiceHideField" class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Param 3</label>
                      <input
                        class="form-control"
                        formControlName="param3"
                        id="param3"
                        placeholder="Enter Param 3"
                        type="text"
                      />
                      <br />
                    </div>
                  </div>
                </div>
              </fieldset>

              <!--    Additional inforamation   -->
              <fieldset *ngIf="!ifplanGroup_VB && ifplanGroup_BWB">
                <legend id="addinfo">Additional Information</legend>
                <div class="boxWhite">
                  <div class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>SAC Code</label>
                      <input
                        class="form-control"
                        formControlName="saccode"
                        id="saccode"
                        placeholder="Enter SAC Code"
                        type="text"
                      />
                      <br />
                    </div>
                  </div>
                </div>
              </fieldset>

              <!-- Charge -->
              <fieldset>
                <legend id="charge">Charge Details</legend>
                <div class="boxWhite">
                  <div [formGroup]="planGroupForm" class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>Plan Price *</label>
                      <input
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.offerprice.errors
                        }"
                        class="form-control"
                        formControlName="offerprice"
                        id="offerprice"
                        min="1"
                        placeholder="Enter Plan Price"
                        type="number"
                        readonly
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.offerprice.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.offerprice.errors.required"
                          class="error text-danger"
                        >
                          Plan Price is required.
                        </div>
                      </div>
                      <br />
                    </div>
                    <div
                      *ngIf="planGroupForm.value.category == 'Business Promotion'"
                      class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    >
                      <label>New Offer Price</label>
                      <input
                        [ngClass]="{
                          'is-invalid': submitted && planGroupForm.controls.newOfferPrice.errors
                        }"
                        class="form-control"
                        formControlName="newOfferPrice"
                        id="newOfferPrice"
                        min="1"
                        placeholder="Enter Offer Price"
                        type="number"
                        readonly
                      />
                      <div
                        *ngIf="submitted && planGroupForm.controls.newOfferPrice.errors"
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="submitted && planGroupForm.controls.newOfferPrice.errors.required"
                          class="error text-danger"
                        >
                          New Offer Price is required.
                        </div>
                        <div
                          *ngIf="submitted && planGroupForm.controls.newOfferPrice.errors.pattern"
                          class="error text-danger"
                        >
                          Only Numeric value is required.
                        </div>
                      </div>
                      <br />
                    </div>
                  </div>
                  <div [formGroup]="chargefromgroup" class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <p-dropdown
                        id="getoffer"
                        (onChange)="getofferPrice($event)"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                        [ngClass]="{
                          'is-invalid': submitted && chargefromgroup.controls.id.errors
                        }"
                        [options]="advanceListData"
                        filter="true"
                        filterBy="name"
                        formControlName="id"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select Charge"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="chargeSubmitted && chargefromgroup.controls.id.errors"
                        class="errorWrap text-danger"
                      >
                        <div id="charge" class="error text-danger">Charge is required.</div>
                      </div>
                    </div>
                    <div
                      *ngIf="chargefromgroup.controls.billingCycle.enabled"
                      class="col-lg-3 col-md-6 col-sm-6 col-xs-12"
                    >
                      <p-dropdown
                        [ngClass]="{
                          'is-invalid': submitted && chargefromgroup.controls.billingCycle.errors
                        }"
                        id="billing"
                        [options]="billingCycle"
                        filter="true"
                        filterBy="label"
                        formControlName="billingCycle"
                        optionLabel="label"
                        optionValue="label"
                        placeholder="Select a Billing Cycle"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="chargeSubmitted && chargefromgroup.controls.billingCycle.errors"
                        class="errorWrap text-danger"
                      >
                        <div class="error text-danger">Billing Cycle is required.</div>
                      </div>
                    </div>

                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <button
                        (click)="onAddChargeField()"
                        class="btn btn-primary"
                        id="addAtt"
                        style="object-fit: cover; padding: 5px 8px"
                        [disabled]="this.ifPlanEditInput && isPlanEdit"
                      >
                        <i aria-hidden="true" class="fa fa-plus-square"></i>
                        Add
                      </button>
                    </div>
                  </div>
                  <div class="scrollbarPlangroupMappingList" style="margin-top: 15px">
                    <table class="table coa-table">
                      <thead>
                        <tr>
                          <th style="text-align: center" id="namecharg">Charge Name</th>
                          <th
                            *ngIf="chargefromgroup.controls.billingCycle.enabled"
                            style="text-align: center"
                          >
                            Biling Cycle
                          </th>
                          <th style="text-align: center" id="currency">Currency</th>
                          <th style="text-align: center" id="actual">Actual Price</th>
                          <th style="text-align: center" id="charg">Charge Price</th>
                          <th style="text-align: center" id="tax">Tax Amount</th>
                          <th style="text-align: center" id="pricetax">Price(including tax)</th>
                          <th style="text-align: right; padding: 8px">
                            Delete
                            <!-- <button id="addAtt" style="object-fit: cover; padding: 5px 8px"
                                                                        class="btn btn-primary" (click)="onAddChargeField()">
                                                                        <i class="fa fa-plus-square" aria-hidden="true"></i> Add
                                                                    </button> -->
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let row of chargeFromArray.controls
                              | paginate
                                : {
                                    id: 'chargeFromArrayData',
                                    itemsPerPage: chargeitemsPerPage,
                                    currentPage: currentPageCharge,
                                    totalItems: chargetotalRecords
                                  };
                            let index = index
                          "
                        >
                          <td style="padding-left: 8px">
                            <p-dropdown
                              id="select"
                              [disabled]="true"
                              [formControl]="row.get('id')"
                              [options]="commondropdownService.chargeList"
                              filter="true"
                              filterBy="name"
                              optionLabel="name"
                              optionValue="id"
                              placeholder="Select a Charge"
                            ></p-dropdown>
                            <div></div>
                          </td>
                          <td
                            *ngIf="chargefromgroup.controls.billingCycle.enabled"
                            style="padding-left: 8px"
                          >
                            <p-dropdown
                              id="billingCycle"
                              [disabled]="this.ifPlanEditInput && isPlanEdit"
                              [formControl]="row.get('billingCycle')"
                              [options]="billingCycle"
                              filter="true"
                              filterBy="label"
                              optionLabel="label"
                              optionValue="label"
                              placeholder="Select a Billing Cycle"
                              appendTo="body"
                            ></p-dropdown>
                            <div></div>
                          </td>
                          <td>
                            <input
                              [formControl]="row.get('currency')"
                              class="form-control"
                              id="currency"
                              min="0"
                              name="currency"
                              placeholder="Enter Currency"
                              [readonly]="true"
                            />
                          </td>
                          <td>
                            <input
                              [formControl]="row.get('actualprice')"
                              class="form-control"
                              id="actualprice        "
                              min="0"
                              name="actualprice"
                              placeholder="Enter Actual Price"
                              type="number"
                              [readonly]="true"
                            />
                          </td>
                          <td>
                            <input
                              [formControl]="row.get('chargeprice')"
                              class="form-control"
                              id="chargeprice"
                              name="chargeprice"
                              placeholder="Enter Charge Price"
                              type="number"
                              (keydown)="preventNegativeInput($event)"
                              (input)="
                                changeActualPrice(
                                  row.value.chargeprice,
                                  row.value.id,
                                  index,
                                  row.value.actualprice,
                                  $event
                                )
                              "
                            />
                            <div
                              class="error text-danger"
                              *ngIf="row.get('chargeprice').hasError('max')"
                            >
                              Charge price can not be greater than actual price.
                            </div>
                          </td>

                          <td>
                            <input
                              data-backdrop="static"
                              data-keyboard="false"
                              data-target="#taxDetailModal"
                              data-toggle="modal"
                              [formControl]="row.get('taxamount')"
                              class="form-control"
                              id="taxamount        "
                              min="0"
                              name="taxamount"
                              placeholder="Enter Actual Price"
                              readonly
                              type="number"
                            />
                          </td>
                          <td>
                            <div
                              style="
                                box-shadow: 0px 1px 2px 0 rgb(0 0 0 / 22%);
                                border-radius: 2px;
                                border-color: #eaeaea;
                                background-color: #eeeeee;
                                display: block;
                                width: 100%;
                                height: 34px;
                                padding: 6px 12px;
                                font-size: 14px;
                                line-height: 1.42857143;
                                color: #555;
                                background-image: none;
                              "
                            >
                              <span *ngFor="let list of totalPriceData; index as j">
                                <span *ngIf="index === j">{{ list | number: "1.2-2" }}</span>
                              </span>
                            </div>
                          </td>

                          <td style="text-align: right">
                            <button
                              (click)="deleteConfirmonChargeField(index, row.get('id').value)"
                              id="deleteAtt"
                              [disabled]="this.ifPlanEditInput && isPlanEdit"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              type="button"
                            >
                              <!-- *ngIf="chargeFromArray.controls.length > 1" -->
                              <img src="assets/img/ioc02.jpg" />
                            </button>
                          </td>
                        </tr>
                        <tr *ngIf="chargeFromArray.controls.length !== 0">
                          <td *ngIf="chargefromgroup.controls.billingCycle.enabled"></td>
                          <td colspan="4" style="text-align: end">
                            <b>Total Price(including tax) :</b>
                          </td>
                          <td colspan="2">{{ countTotalOfferPrice | number: "1.2-2" }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- <div class="row">
                                  <div class="col-md-12">
                                    <pagination-controls
                                      id="chargeFromArrayData"
                                      maxSize="10"
                                      directionLinks="true"
                                      previousLabel=""
                                      nextLabel=""
                                      (pageChange)="pageChangedCharge($event)"
                                    ></pagination-controls>
                                  </div>
                                </div> -->
                  <br />
                </div>
              </fieldset>
              <!-- Product -->
              <fieldset *ngIf="planProductMappingShowFlag">
                <legend id="product">Product Details</legend>
                <div class="boxWhite">
                  <div [formGroup]="planProductfromgroup">
                    <div class="row">
                      <!-- <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                      <div class="form-group">
                        <input
                          class="form-control"
                          formControlName="name"
                          placeholder="Enter Name"
                          type="text"
                        />
                      </div>
                    </div> -->
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                        <div class="form-group">
                          <label>Product Category</label>
                          <p-dropdown
                            id="category"
                            (onChange)="getProductbyCategory($event)"
                            [options]="commondropdownService.productCategoryList"
                            filter="true"
                            filterBy="name"
                            formControlName="productCategoryId"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Select a Product Category"
                          ></p-dropdown>
                          <div
                            *ngIf="
                              submitted &&
                              planProductSubmitted &&
                              planProductfromgroup.controls.productCategoryId.errors
                            "
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.productCategoryId.errors.required
                              "
                              class="error text-danger"
                            >
                              Product Category is required.
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="productFlag">
                        <div class="form-group">
                          <label>Product</label>
                          <p-dropdown
                            id="product"
                            (onChange)="getProductDetails($event)"
                            [options]="productList"
                            filter="true"
                            filterBy="name"
                            formControlName="productId"
                            optionLabel="name"
                            optionValue="id"
                            placeholder="Select a Product"
                          ></p-dropdown>
                          <div
                            *ngIf="
                              submitted &&
                              planProductSubmitted &&
                              planProductfromgroup.controls.productId.errors
                            "
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.productId.errors.required
                              "
                              class="error text-danger"
                            >
                              Product is required.
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- </div> -->
                      <!-- <div class="row"> -->
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="productTypeFlag">
                        <div class="form-group">
                          <label>Product Type</label>
                          <p-dropdown
                            id="prodtype"
                            (onChange)="getChargeAmount($event)"
                            [options]="productType"
                            filter="true"
                            filterBy="label"
                            formControlName="product_type"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Select a Product Type"
                          ></p-dropdown>
                          <div
                            *ngIf="
                              submitted &&
                              planProductSubmitted &&
                              planProductfromgroup.controls.product_type.errors
                            "
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.product_type.errors.required
                              "
                              class="error text-danger"
                            >
                              Product Type is required.
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="ownershipTypeFlag">
                        <div class="form-group">
                          <label id="prodquantty">Product Quantity</label>
                          <input
                            [ngClass]="{
                              'is-invalid':
                                planProductSubmitted &&
                                planProductfromgroup.controls.productQuantity.errors
                            }"
                            class="form-control"
                            formControlName="productQuantity"
                            min="1"
                            type="number"
                            (keypress)="productQtyValidation($event)"
                          />
                          <div
                            *ngIf="
                              submitted &&
                              planProductSubmitted &&
                              planProductfromgroup.controls.productQuantity.errors
                            "
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.productQuantity.errors.required
                              "
                              class="error text-danger"
                            >
                              Product Quantity is required.
                            </div>
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.productQuantity.errors.min
                              "
                              class="error text-danger"
                            >
                              Minimum 1 Allowed.
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- </div>
                    <div class="row"> -->
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="ownershipTypeFlag">
                        <div class="form-group">
                          <label>Ownership Type</label>
                          <p-dropdown
                            id="ownership"
                            (onChange)="checkOwnership($event)"
                            [options]="ownershipType"
                            filter="true"
                            filterBy="label"
                            formControlName="ownershipType"
                            optionLabel="label"
                            optionValue="value"
                            placeholder="Select a Ownership Type"
                          ></p-dropdown>
                          <div
                            *ngIf="
                              submitted &&
                              planProductSubmitted &&
                              planProductfromgroup.controls.ownershipType.errors
                            "
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.ownershipType.errors
                              "
                              class="errorWrap text-danger"
                            >
                              <div
                                *ngIf="
                                  submitted &&
                                  planProductSubmitted &&
                                  planProductfromgroup.controls.ownershipType.errors.required
                                "
                                class="error text-danger"
                              >
                                Owership Type is required.
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="revisedChargeFlag">
                        <div class="form-group">
                          <label id="revised">Revised Charge</label>
                          <input
                            [ngClass]="{
                              'is-invalid':
                                planProductSubmitted &&
                                planProductfromgroup.controls.revisedCharge.errors
                            }"
                            [(ngModel)]="revicedAmount"
                            class="form-control"
                            formControlName="revisedCharge"
                            min="1"
                            type="number"
                            (keypress)="revicedChargeValidation($event)"
                          />
                          <div
                            *ngIf="
                              submitted &&
                              planProductSubmitted &&
                              planProductfromgroup.controls.revisedCharge.errors
                            "
                            class="errorWrap text-danger"
                          >
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.revisedCharge.errors.required
                              "
                              class="error text-danger"
                            >
                              Revised Charge is required.
                            </div>
                            <div
                              *ngIf="
                                submitted &&
                                planProductSubmitted &&
                                planProductfromgroup.controls.revisedCharge.errors.minlength
                              "
                              class="error text-danger"
                            >
                              Minimum 0 Allowed.
                            </div>
                          </div>
                          <div class="error text-danger" *ngIf="this.showError">
                            {{ this.priceErrorMsg }}
                          </div>
                        </div>
                      </div>
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12" *ngIf="productChargeFlag">
                        <div class="form-group">
                          <label id="prodcharg">Product Charge</label>
                          <input
                            [value]="chargeAmount"
                            class="form-control"
                            type="text"
                            required
                            value=""
                            readonly
                          />
                        </div>
                      </div>
                      <!-- </div>
                  <div class="row"> -->
                      <!-- <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                        <div class="form-group">
                          <button
                            (click)="onAddProductField()"
                            class="btn btn-primary"
                            id="addAtt"
                            style="object-fit: cover; padding: 5px 8px"
                          >
                            <i aria-hidden="true" class="fa fa-plus-square"></i>
                            Add
                          </button>
                        </div>
                      </div> -->
                    </div>
                    <div class="row">
                      <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                        <div class="form-group">
                          <button
                            (click)="onAddProductField()"
                            class="btn btn-primary"
                            id="addAtt"
                            style="object-fit: cover; padding: 5px 8px"
                            [disabled]="!planProductfromgroup.valid || showError"
                          >
                            <i aria-hidden="true" class="fa fa-plus-square"></i>
                            Add
                          </button>
                        </div>
                      </div>
                      <div class="error text-danger" *ngIf="this.showQtyError">
                        {{ this.qtyErroMsg }}
                      </div>
                    </div>
                  </div>

                  <div style="margin-top: 15px">
                    <table class="table coa-table">
                      <thead>
                        <tr>
                          <th style="text-align: center" id="name">Name</th>
                          <th style="text-align: center" id="pegodcatg">Product Category</th>
                          <th style="text-align: center" id="Product">Product</th>
                          <th style="text-align: center" id="Producttyp">Product Type</th>
                          <th style="text-align: center" id="quantity">Quantity</th>
                          <th style="text-align: center" id="Revised">Revised Charge</th>
                          <th style="text-align: center" id="Ownership">Ownership Type</th>
                          <th style="text-align: right; padding: 8px" id="delete">Delete</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let row of planProductMappingFromArray.controls
                              | paginate
                                : {
                                    id: 'planProductFromArrayData',
                                    itemsPerPage: planProductMappingItemsPerPage,
                                    currentPage: currentPagePlanProductMapping,
                                    totalItems: planProductMappingTotalRecords
                                  };
                            let index = index
                          "
                        >
                          <td style="padding-left: 8px">
                            <div class="form-group">
                              <p-dropdown
                                id="prodid"
                                [options]="this.commondropdownService.activeProductList"
                                filter="true"
                                filterBy="name"
                                [formControl]="row.get('productId')"
                                optionLabel="name"
                                optionValue="id"
                                placeholder="Select a Product"
                                [disabled]="true"
                              ></p-dropdown>
                            </div>
                          </td>
                          <td style="padding-left: 8px">
                            <p-dropdown
                              id="selectprodcateg"
                              [options]="commondropdownService.productCategoryList"
                              filter="true"
                              filterBy="name"
                              [formControl]="row.get('productCategoryId')"
                              optionLabel="name"
                              optionValue="id"
                              placeholder="Select a Product Category"
                              [disabled]="true"
                            ></p-dropdown>
                          </td>
                          <td style="padding-left: 8px">
                            <p-dropdown
                              id="selectprod"
                              [options]="allActiveProduct"
                              filter="true"
                              filterBy="name"
                              [formControl]="row.get('productId')"
                              optionLabel="name"
                              optionValue="id"
                              placeholder="Select a Product"
                              [disabled]="true"
                            ></p-dropdown>
                          </td>
                          <td>
                            <p-dropdown
                              id="prodtype"
                              [options]="productType"
                              filter="true"
                              filterBy="label"
                              [formControl]="row.get('product_type')"
                              optionLabel="label"
                              optionValue="value"
                              placeholder="Select a Product Type"
                              [disabled]="true"
                            ></p-dropdown>
                          </td>
                          <td>
                            <input
                              id="prodquan"
                              class="form-control"
                              [formControl]="row.get('productQuantity')"
                              min="1"
                              type="number"
                              readonly
                            />
                          </td>
                          <td>
                            <input
                              id="revisecharge"
                              [formControl]="row.get('revisedCharge')"
                              class="form-control"
                              min="1"
                              placeholder="Enter Revised Charge"
                              type="number"
                              readonly
                            />
                          </td>
                          <td>
                            <p-dropdown
                              id="ownershptyp"
                              [options]="ownershipType"
                              filter="true"
                              filterBy="label"
                              [formControl]="row.get('ownershipType')"
                              optionLabel="label"
                              optionValue="value"
                              placeholder="Select a Ownership Type"
                              [disabled]="true"
                            ></p-dropdown>
                          </td>

                          <td style="text-align: right">
                            <button
                              (click)="deleteConfirmonPlanProductField(index, row.get('id').value)"
                              class="curson_pointer approve-btn"
                              id="deleteAtt"
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <div class="pagination_Dropdown">
                      <pagination-controls
                        (pageChange)="pageChangePlanProductData($event)"
                        directionLinks="true"
                        id="planProductFromArrayData"
                        maxSize="10"
                        nextLabel=""
                        previousLabel=""
                      ></pagination-controls>
                    </div>
                  </div>
                </div>
              </fieldset>

              <!-- CasMapping -->
              <fieldset *ngIf="ifCasMapping">
                <legend id="casmapping">Cas Mapping</legend>
                <div class="boxWhite">
                  <div [formGroup]="planCasMappingFromGroup" class="row">
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>CAS*</label>
                      <p-dropdown
                        id="cas"
                        (onChange)="getAllCASPackage($event.value)"
                        [options]="casmasterData"
                        filter="true"
                        filterBy="casname"
                        formControlName="casId"
                        optionLabel="casname"
                        optionValue="id"
                        placeholder="Select a CAS"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="
                          submitted &&
                          planCasMappingSubmitted &&
                          planCasMappingFromGroup.controls.casId.errors
                        "
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            submitted &&
                            planCasMappingSubmitted &&
                            planCasMappingFromGroup.controls.casId.errors.required
                          "
                          class="error text-danger"
                        >
                          <div class="error text-danger">CAS is required.</div>
                        </div>
                      </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <label>CAS Package*</label>
                      <p-dropdown
                        id="caspcge"
                        [options]="casPackageData"
                        filter="true"
                        filterBy="packageName"
                        formControlName="packageId"
                        optionLabel="packageName"
                        optionValue="packageId"
                        placeholder="Select a CAS Package"
                      ></p-dropdown>
                      <div></div>
                      <div
                        *ngIf="
                          submitted &&
                          planCasMappingSubmitted &&
                          planCasMappingFromGroup.controls.packageId.errors
                        "
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            submitted &&
                            planCasMappingSubmitted &&
                            planCasMappingFromGroup.controls.packageId.errors.required
                          "
                        >
                          <div class="error text-danger">CAS Package is required.</div>
                        </div>
                      </div>
                    </div>

                    <div class="col-lg-3 col-md-6 col-sm-6 col-xs-12">
                      <button
                        (click)="
                          onAddCasMappingField(this.planCasMappingFromGroup.controls.casId.value)
                        "
                        class="btn btn-primary"
                        id="addAtt"
                        style="object-fit: cover; padding: 5px 8px"
                      >
                        <i aria-hidden="true" class="fa fa-plus-square"></i>
                        Add
                      </button>
                    </div>
                  </div>
                  <div class="scrollbarPlangroupMappingList" style="margin-top: 15px">
                    <table class="table coa-table">
                      <thead>
                        <tr>
                          <th style="text-align: center" id="CAS">CAS</th>
                          <th style="text-align: center" id="caspcg">CAS Package</th>
                          <th style="text-align: center; padding: 8px" id="delet">Delete</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let row of planCasMappingFromArray.controls; index as index">
                          <td style="padding-left: 8px">
                            <p-dropdown
                              id="selectcas"
                              [disabled]="true"
                              [options]="casmasterData"
                              filter="true"
                              filterBy="casname"
                              optionLabel="casname"
                              optionValue="id"
                              placeholder="Select a CAS"
                              [formControl]="row.get('casId')"
                            ></p-dropdown>

                            <div></div>
                          </td>
                          <td style="padding-left: 8px">
                            <p-dropdown
                              id="selectacas"
                              [options]="casPackegeAllData"
                              filter="true"
                              filterBy="packageName"
                              optionLabel="packageName"
                              optionValue="packageId"
                              placeholder="Select a CAS Package"
                              [disabled]="true"
                              [formControl]="row.get('packageId')"
                            ></p-dropdown>
                            <div></div>
                          </td>
                          <td style="text-align: center">
                            <button
                              (click)="deleteConfirmonCasMappingField(index)"
                              id="deleteAtt"
                              class="approve-btn"
                              style="
                                border: none;
                                background: transparent;
                                padding: 0;
                                margin-right: 3px;
                              "
                              type="button"
                            >
                              <img src="assets/img/ioc02.jpg" />
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <br />
                </div>
              </fieldset>

              <fieldset *ngIf="fieldsArr.length != 0 && commondropdownService.isPlanOnDemand">
                <legend id="service">Service Parameters</legend>
                <div class="boxWhite" [formGroup]="customerGroupForm">
                  <div class="row" style="margin: 2px">
                    <div *ngFor="let item of fieldsArr; let i = index">
                      <div
                        *ngIf="
                          item.fieldType != 'select' &&
                          item.fieldType != 'multi-select' &&
                          item.fieldType != 'checkbox' &&
                          item.fieldType != 'object' &&
                          item.fieldType != 'objectList'
                        "
                      >
                        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                          <div class="form-group">
                            <label style="margin-top: 25px"
                              >{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label
                            >
                            <input
                              class="form-control"
                              name="{{ item.fieldname }}"
                              id="{{ item.fieldname }}"
                              placeholder="Enter {{ item.fieldname }}"
                              type="{{ item.fieldType }}"
                              [formControlName]="item.fieldname"
                              [(ngModel)]="item.defaultValue"
                            />
                            <div
                              *ngIf="
                                customerGroupForm.controls[item.fieldname].errors &&
                                (customerGroupForm.controls[item.fieldname].touched ||
                                  templateSubmitted)
                              "
                              class="errorWrap text-danger"
                            >
                              <div class="error text-danger">{{ item.name }} is required.</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div *ngIf="item.fieldType == 'checkbox'">
                        <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                          <label style="margin-top: 25px"
                            >{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label
                          >
                          <input
                            class="form-check-input"
                            name="{{ item.fieldname }}"
                            id="{{ item.fieldname }}"
                            placeholder="Enter {{ item.fieldname }}"
                            type="checkbox"
                            [formControlName]="item.fieldname"
                            style="width: 20px; height: 20px; margin-left: 15px; margin-top: 50px"
                            [(ngModel)]="item.defaultValue"
                          />
                        </div>
                        <div
                          *ngIf="
                            customerGroupForm.controls[item.fieldname].errors &&
                            (customerGroupForm.controls[item.fieldname].touched ||
                              templateSubmitted)
                          "
                          class="errorWrap text-danger"
                        >
                          <div class="error text-danger">{{ item.name }} is required.</div>
                        </div>
                      </div>

                      <div *ngIf="item.fieldType == 'select' && !item.isdependant">
                        <div *ngFor="let list of optionList">
                          <div
                            *ngIf="
                              list.fieldname == item.fieldname &&
                              item.backendrequired == 'displayName'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                              <label style="margin-top: 25px; margin-bottom: 5px"
                                >{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label
                              >
                              <div class="dropdown-container">
                                <p-dropdown
                                  id="displayname"
                                  [options]="list.options.dataList"
                                  optionValue="displayName"
                                  optionLabel="displayName"
                                  filter="true"
                                  filterBy="displayName"
                                  placeholder="Select a {{ list.fieldname }}"
                                  [formControlName]="item.fieldname"
                                  name="{{ i + 1 }}module"
                                  [(ngModel)]="item.defaultValue"
                                >
                                </p-dropdown>
                              </div>
                              <div
                                *ngIf="
                                  customerGroupForm.controls[item.fieldname].errors &&
                                  (customerGroupForm.controls[item.fieldname].touched ||
                                    templateSubmitted)
                                "
                                class="errorWrap text-danger"
                              >
                                <div class="error text-danger">{{ item.name }} is required.</div>
                              </div>
                            </div>
                          </div>

                          <div
                            *ngIf="
                              list.fieldname == item.fieldname &&
                              item.backendrequired == 'displayId'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                              <label style="margin-top: 25px; margin-bottom: 5px"
                                >{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label
                              >
                              <div class="dropdown-container">
                                <p-dropdown
                                  id="selactalistname"
                                  [options]="list.options.dataList"
                                  optionValue="displayId"
                                  optionLabel="displayName"
                                  filter="true"
                                  filterBy="displayName"
                                  placeholder="Select a {{ list.fieldname }}"
                                  [formControlName]="item.fieldname"
                                  name="{{ i + 1 }}module"
                                  [(ngModel)]="item.defaultValue"
                                >
                                </p-dropdown>
                                <div
                                  *ngIf="
                                    customerGroupForm.controls[item.fieldname].errors &&
                                    (customerGroupForm.controls[item.fieldname].touched ||
                                      templateSubmitted)
                                  "
                                  class="errorWrap text-danger"
                                >
                                  <div class="error text-danger">{{ item.name }} is required.</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- <div *ngIf="item.fieldType == 'select' && item.isdependant == true">
                        <div *ngFor="let list of dependantOptionList">
                          <div
                            *ngIf="
                              list.fieldname == item.fieldname &&
                              item.backendrequired == 'displayName'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                              <label style="margin-top: 25px; margin-bottom: 5px"
                                >{{ item.name }}<span *ngIf="item.mandatoryFlag"> * </span></label
                              >
                              <div class="dropdown-container">
                                <p-dropdown
                                  [options]="list.options.dataList"
                                  optionValue="displayName"
                                  optionLabel="displayName"
                                  filter="true"
                                  filterBy="text"
                                  placeholder="Select a {{ list.fieldname }}"
                                  [formControlName]="item.fieldname"
                                  name="{{ i + 1 }}module"
                                >
                                </p-dropdown>
                                <div
                                  *ngIf="
                                    customerGroupForm.controls[item.fieldname].errors &&
                                    (customerGroupForm.controls[item.fieldname].touched ||
                                      templateSubmitted)
                                  "
                                  class="errorWrap text-danger"
                                >
                                  <div class="error text-danger">{{ item.name }} is required.</div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            *ngIf="
                              list.fieldname == item.fieldname &&
                              item.backendrequired == 'displayId'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                              <label style="margin-top: 25px; margin-bottom: 5px"
                                >{{ item.name }}<span *ngIf="item.mandatoryFlag"> * </span></label
                              >
                              <div class="dropdown-container">
                                <p-dropdown
                                  [options]="list.options.dataList"
                                  optionValue="displayId"
                                  optionLabel="displayName"
                                  filter="true"
                                  filterBy="displayName"
                                  placeholder="Select a {{ list.fieldname }}"
                                  [formControlName]="item.fieldname"
                                  name="{{ i + 1 }}module"
                                >
                                </p-dropdown>
                                <div
                                  *ngIf="
                                    customerGroupForm.controls[item.fieldname].errors &&
                                    (customerGroupForm.controls[item.fieldname].touched ||
                                      templateSubmitted)
                                  "
                                  class="errorWrap text-danger"
                                >
                                  <div class="error text-danger">{{ item.name }} is required.</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div> -->
                      <div *ngIf="item.fieldType == 'multi-select'">
                        <div *ngFor="let list of multiOptionList">
                          <div
                            *ngIf="
                              list.fieldname == item.fieldname &&
                              item.backendrequired == 'displayName'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                              <label style="margin-top: 25px; margin-bottom: 5px"
                                >{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label
                              >
                              <div class="multiselect">
                                <p-multiSelect
                                  id="selectdisplay"
                                  [options]="list.options.dataList"
                                  optionValue="displayName"
                                  defaultLabel="Select a {{ item.fieldname }}"
                                  optionLabel="displayName"
                                  [formControlName]="item.fieldname"
                                ></p-multiSelect>
                                <div
                                  *ngIf="
                                    customerGroupForm.controls[item.fieldname].errors &&
                                    (customerGroupForm.controls[item.fieldname].touched ||
                                      templateSubmitted)
                                  "
                                  class="errorWrap text-danger"
                                >
                                  <div class="error text-danger">{{ item.name }} is required.</div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                            *ngIf="
                              list.fieldname == item.fieldname &&
                              item.backendrequired == 'displayId'
                            "
                          >
                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                              <label style="margin-top: 25px; margin-bottom: 5px"
                                >{{ item.name }}<span *ngIf="item.mandatoryFlag">*</span></label
                              >
                              <div class="multiselect">
                                <p-multiSelect
                                  id="namedisp"
                                  [options]="list.options.dataList"
                                  optionValue="displayId"
                                  defaultLabel="Select a {{ item.fieldname }}"
                                  optionLabel="displayName"
                                  [formControlName]="item.fieldname"
                                ></p-multiSelect>
                                <div
                                  *ngIf="
                                    customerGroupForm.controls[item.fieldname].errors &&
                                    (customerGroupForm.controls[item.fieldname].touched ||
                                      templateSubmitted)
                                  "
                                  class="errorWrap text-danger"
                                >
                                  <div class="error text-danger">{{ item.name }} is required.</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <br />
                  </div>
                </div>
              </fieldset>

              <!-- <button type="submit" class="btn btn-primary" style="margin-top: 1rem">Submit</button> -->
              <div class="addUpdateBtn addeditbtntop">
                <button
                  (click)="addEditPostPaidPlan(null)"
                  *ngIf="!isPlanEdit"
                  class="btn btn-primary"
                  id="submit"
                  type="submit"
                >
                  <i class="fa fa-check-circle"></i>
                  Add Plan
                </button>
                <button
                  (click)="addEditPostPaidPlan(viewPlanListData.id)"
                  *ngIf="isPlanEdit"
                  class="btn btn-primary"
                  id="submit"
                  type="submit"
                >
                  <i class="fa fa-check-circle"></i>
                  Update Plan
                </button>
                <br />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="listView" class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Plan</h3>
        <div class="right">
          <button
            id="butt"
            aria-controls="listplanData"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#listplanData"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="listplanData">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th id="'name">Name</th>
                    <th id="plantyp">Plan Type</th>
                    <th id="validity">Validity</th>
                    <th id="startdat">Start Date</th>
                    <th id="enddate">End Date</th>
                    <th id="planprice">Plan Price</th>
                    <th id="taxamount">Tax Amount</th>
                    <th style="width: 10%" id="stat">Status</th>
                    <th id="create">Data Type</th>
                    <th id="createby">Create By</th>
                    <th id="createby">ISP Name</th>
                    <th style="width: 15%">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let plan of postPaidPlanList
                        | paginate
                          : {
                              id: 'planListData',
                              itemsPerPage: postPaidPlanitemsPerPage,
                              currentPage: currentPagePostPaidPlan,
                              totalItems: postPaidPlantotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <a
                        (click)="planDetail(plan.id)"
                        href="javascript:void(0)"
                        style="color: #f7b206"
                      >
                        {{ plan.displayName }}
                      </a>
                    </td>
                    <td>{{ plan.plantype }}</td>
                    <td>
                      <div *ngIf="plan.plantype === 'Prepaid'">
                        <span> {{ plan.validity }} {{ plan.unitsOfValidity }} </span>
                      </div>

                      <div *ngIf="plan.plantype == 'Postpaid'">
                        <span
                          *ngIf="
                            (plan?.planGroup | lowercase) === 'volume booster' ||
                            (plan?.planGroup | lowercase) === 'bandwidthbooster' ||
                            (plan?.planGroup | lowercase) === 'dtv addon'
                          "
                        >
                          <span> {{ plan.validity }} {{ plan.unitsOfValidity }} </span>
                        </span>
                        <span
                          *ngIf="
                            (plan?.planGroup | lowercase) !== 'volume booster' &&
                            (plan?.planGroup | lowercase) !== 'bandwidthbooster' &&
                            (plan?.planGroup | lowercase) !== 'dtv addon'
                          "
                        >
                          N/A
                        </span>
                      </div>
                    </td>
                    <td>{{ plan.startDate }}</td>
                    <td>
                      <div>
                        <span>{{ plan.endDate }}</span>
                      </div>
                    </td>
                    <td>
                      <!-- {{
                        plan.currency
                          ? plan.category === "Business Promotion"
                            ? plan.newOfferPrice.toFixed(2) + " " + plan.currency
                            : plan.offerprice.toFixed(2) + " " + plan.currency
                          : plan.category === "Business Promotion"
                            ? plan.newOfferPrice.toFixed(2) + " " + currency
                            : plan.offerprice.toFixed(2) + " " + currency
                      }} -->

                      {{
                        plan.currency
                          ? plan.category === "Business Promotion"
                            ? (plan.newOfferPrice | currency: plan.currency : "symbol" : "1.2-2")
                            : (plan.offerprice | currency: plan.currency : "symbol" : "1.2-2")
                          : plan.category === "Business Promotion"
                            ? (plan.newOfferPrice | currency: currency : "symbol" : "1.2-2")
                            : (plan.offerprice | currency: currency : "symbol" : "1.2-2")
                      }}
                    </td>
                    <td>
                      <!-- {{
                        (plan?.currency ? plan.currency : currency) +
                          " " +
                          plan.taxamount.toFixed(2)
                      }} -->

                      {{
                        plan.currency
                          ? (plan.taxamount | currency: plan.currency : "symbol" : "1.2-2")
                          : (plan.taxamount | currency: currency : "symbol" : "1.2-2")
                      }}
                    </td>
                    <td>
                      <div *ngIf="plan.status == 'ACTIVE' || plan.status == 'Active'">
                        <span class="badge badge-success" id="actv">Active</span>
                      </div>
                      <div *ngIf="plan.status == 'INACTIVE' || plan.status == 'Inactive'">
                        <span class="badge badge-danger" id="inactv">Inactive</span>
                      </div>
                      <div *ngIf="plan.status == 'Rejected' || plan.status == 'Rejected'">
                        <span class="badge badge-danger" id="reject">Rejected</span>
                      </div>
                      <div *ngIf="plan.status == 'NewActivation'">
                        <span class="badge badge-success" id="newactv">New Activation</span>
                      </div>
                      <div *ngIf="plan.status == 'Expired' || plan.status == 'Expired'">
                        <span class="badge badge-danger" id="exp">Expired</span>
                      </div>
                    </td>
                    <td>{{ plan.quotatype }}</td>
                    <td>{{ plan.createdByName }}</td>
                    <td>{{ plan.mvnoName }}</td>
                    <td class="btnAction">
                      <a
                        *ngIf="editAccess"
                        (click)="editPostPaidPlan(plan.id)"
                        href="javascript:void(0)"
                        id="edit-button"
                        type="button"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>

                      <button
                        *ngIf="changeStatusAccess"
                        (click)="openChangeStatusModal(plan.id, plan.status)"
                        [disabled]="plan.status == 'NewActivation'"
                        class="approve-btn detailOnAnchorClick"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Change Status"
                        type="button"
                      >
                        <img class="icon" src="assets/img/E_Status_Y.png" />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        *ngIf="
                          plan.status == 'Active' ||
                          plan.status == 'INACTIVE' ||
                          plan.status == 'Expired' ||
                          plan.status == 'Rejected'
                        "
                        [disabled]="
                          plan.nextStaff != null ||
                          plan.status == 'Rejected' ||
                          plan.status == 'Active'
                        "
                        (click)="pickModalOpen(plan)"
                      >
                        <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        type="button"
                        title="Pick"
                        *ngIf="plan.status == 'NewActivation'"
                        [disabled]="plan.nextStaff != null"
                        (click)="pickModalOpen(plan)"
                      >
                        <img src="assets/img/17_Pick-ticket-to-resolve_Y.png" />
                      </button>
                      <button
                        id="approv"
                        (click)="approvePlanOpen(plan.id, '')"
                        [disabled]="
                          plan.nextStaff !== this.staffID ||
                          plan.nextStaff == null ||
                          plan.status == 'Active' ||
                          plan.status == 'ACTIVE' ||
                          plan.status == 'INACTIVE' ||
                          plan.status == 'Expired' ||
                          plan.status == 'Rejected'
                        "
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Approve"
                        type="button"
                      >
                        <img src="assets/img/assign.jpg" />
                      </button>
                      <button
                        id="reject"
                        (click)="rejectPlanOpen(plan.id, '')"
                        [disabled]="
                          plan.nextStaff !== this.staffID ||
                          plan.nextStaff == null ||
                          plan.status == 'Active' ||
                          plan.status == 'ACTIVE' ||
                          plan.status == 'INACTIVE' ||
                          plan.status == 'Expired' ||
                          plan.status == 'Rejected'
                        "
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        title="Reject"
                        type="button"
                      >
                        <img src="assets/img/reject.jpg" />
                      </button>

                      <!-- <a
                                          class="detailOnAnchorClick"
                                          title="Status"
                                          (click)="openstatus(plan.id)"
                                        >
                                          <img class="icon" src="assets/img/E_Status_Y.png" />
                                        </a> -->
                      <a
                        class="detailOnAnchorClick"
                        title="Workflow Status Details"
                        id="openpay"
                        (click)="openPaymentWorkFlow('custauditWorkflowModal', plan.id)"
                      >
                        <img
                          width="32"
                          height="32"
                          src="assets/img/05_inventory-to-customer_Y.png"
                        />
                      </a>
                      <button
                        style="border: none; background: transparent; padding: 0; margin-right: 3px"
                        id="assign-button"
                        title="Reassign PLAN"
                        class="approve-btn"
                        (click)="StaffReasignList(plan)"
                        [disabled]="
                          plan.status == 'Active' ||
                          plan.status == 'ACTIVE' ||
                          plan.status == 'INACTIVE' ||
                          plan.status == 'Expired' ||
                          plan.status == 'Rejected'
                        "
                      >
                        <img
                          width="32"
                          height="32"
                          alt="Assign PLAN"
                          src="assets/img/icons-02.png"
                        />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  (pageChange)="pageChangedPlanList((currentPagePostPaidPlan = $event))"
                  directionLinks="true"
                  id="planListData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    id="showitem"
                    [(ngModel)]="showItemPerPage"
                    (onChange)="TotalItemPerPage($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-workflow-audit-details-modal
    *ngIf="ifModelIsShow"
    [auditcustid]="auditcustid"
    dialogId="custauditWorkflowModal"
    (closeParentCustt)="closeParentCustt()"
  ></app-workflow-audit-details-modal>

  <div *ngIf="detailView" class="col-md-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            idd="listpln"
            (click)="listPlan()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Plan Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title" id="plandetil">Plan Detail</h3>
        </div>
        <div class="right">
          <button
            id="alldetail"
            aria-controls="allDeatils"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#allDeatils"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="allDeatils">
        <div class="panel-body">
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend id="basic">Basic Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="naam">Name :</label>
                  <span>{{ planDetailData.name }}</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="dispnam">Display Name :</label>
                  <span>{{ planDetailData.displayName }}</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="cod">Code :</label>
                  <span>{{ planDetailData.code }}</span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="plntype">Plan Type :</label>
                  <span>{{ planDetailData.plantype }}</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="catog">Category:</label>
                  <span>{{ planDetailData.category }}</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="plngrp">Plan Group :</label>
                  <span>{{ planDetailData.planGroup }}</span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="service">Service :</label>
                  <span>{{ planDetailData.serviceName }}</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="strtdate">Start Date :</label>
                  <span>{{ planDetailData.startDate }}</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="enddat">End Date :</label>
                  <span>{{ planDetailData.endDate }}</span>
                </div>
              </div>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="planprice">Plan Price :</label>
                  <span>{{
                    planDetailData.currency
                      ? (planDetailData.offerprice
                        | currency: planDetailData.currency : "symbol" : "1.2-2")
                      : (planDetailData.offerprice | currency: currency : "symbol" : "1.2-2")
                  }}</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="validity">Validity :</label>
                  <span *ngIf="planDetailData.plantype == 'Prepaid'">
                    {{ planDetailData.validity }}
                    {{ planDetailData.unitsOfValidity }}
                  </span>
                  <span *ngIf="planDetailData.plantype == 'Postpaid'">
                    <span
                      *ngIf="
                        (planDetailData?.planGroup | lowercase) === 'volume booster' ||
                        (planDetailData?.planGroup | lowercase) === 'bandwidthbooster' ||
                        (planDetailData?.planGroup | lowercase) === 'dtv addon'
                      "
                    >
                      <span>
                        {{ planDetailData.validity }} {{ planDetailData.unitsOfValidity }}
                      </span>
                    </span>
                    <span
                      *ngIf="
                        (planDetailData?.planGroup | lowercase) !== 'volume booster' &&
                        (planDetailData?.planGroup | lowercase) !== 'bandwidthbooster' &&
                        (planDetailData?.planGroup | lowercase) !== 'dtv addon'
                      "
                    >
                      N/A
                    </span>
                  </span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="servicearea">Service Area :</label>
                  <!-- <span
                                    style="word-break: break-all"
                                    *ngFor="let serviceName of planDetailData.serviceAreaNameList"
                                  >
                                    <span>{{ serviceName.name }},&nbsp;</span>
                                  </span> -->
                  <span
                    id="arealist"
                    class="HoverEffect"
                    (click)="showServiceArea = true"
                    title="Go To service Area List"
                  >
                    Click here
                  </span>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="accessbility">Accessibility:</label>
                  <span>{{ planDetailData.accessibility }}</span>
                  <br />
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="planmod">Plan Mode :</label>
                  <span>
                    {{ planDetailData.mode }}
                  </span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="statuss">Status :</label>
                  <span>{{ planDetailData.status }}</span>
                </div>
              </div>
              <div class="row">
                <div
                  *ngIf="!isServiceHideField && !ifplanGroup_VB"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                >
                  <label class="datalbl" id="allowower">Allow Over Usage :</label>
                  <span>{{ planDetailData.allowOverUsage }}</span>
                </div>

                <div
                  *ngIf="!isServiceHideField && !ifplanGroup_VB"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                >
                  <label class="datalbl" id="maxcurentsessin">Max Current Session :</label>
                  <span>{{ planDetailData.maxconcurrentsession }}</span>
                </div>

                <div class="col-lg-4 col-md-4 dataGroup">
                  <label class="datalbl" id="allowdisc">Allow Discount :</label>
                  <span *ngIf="planDetailData.allowdiscount == true">Yes</span>
                  <span *ngIf="planDetailData.allowdiscount == false">No</span>
                </div>
                <div
                  class="col-lg-4 col-md-4 dataGroup"
                  *ngIf="planDetailData.category == 'Business Promotion'"
                >
                  <label class="datalbl" id="invoicetoorg">Invoice To Org :</label>
                  <span *ngIf="planDetailData.invoiceToOrg == true">Yes</span>
                  <span *ngIf="planDetailData.invoiceToOrg == false">No</span>
                </div>
                <div
                  class="col-lg-4 col-md-4 dataGroup"
                  *ngIf="planDetailData.category == 'Business Promotion'"
                >
                  <label class="datalbl" id="reqapprov">Required Approval :</label>
                  <span *ngIf="planDetailData.requiredApproval == true">Yes</span>
                  <span *ngIf="planDetailData.requiredApproval == false">No</span>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="descc">Desc :</label>
                  <span>{{ planDetailData.desc }}</span>
                  <br />
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="maxHoldDurationDays">Max Hold Days :</label>
                  <span>{{ planDetailData.maxHoldDurationDays }}</span>
                  <br />
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="maxHoldAttempts">Max Hold Attempts :</label>
                  <span>{{ planDetailData.maxHoldAttempts }}</span>
                  <br />
                </div>
              </div>
            </div>
          </fieldset>

          <!--    Quota Details   -->
          <fieldset *ngIf="!isServiceHideField" style="margin-top: 0rem; margin-bottom: 2rem">
            <legend id="quotadetail">Quota Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="qotatypp">Quota Type :</label>
                  <span>{{ planDetailData.quotatype }}</span>
                  <br />
                </div>
                <div
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                  *ngIf="!isServiceHideField"
                >
                  <label class="datalbl">Base Qos Policy:</label>
                  <span
                    id="basspolicy"
                    (click)="getQosPolicyById(planDetailData.qospolicyid)"
                    *ngIf="planDetailData.qospolicyName"
                    class="HoverEffect"
                  >
                    {{ planDetailData.qospolicyName }}
                  </span>
                  <span *ngIf="!planDetailData.qospolicyName">-</span>
                  <br />
                </div>
                <div
                  *ngIf="planDetailData.quota"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                >
                  <label class="datalbl" id="quotaa">Quota :</label>
                  <span>{{ planDetailData.quota }}</span>
                  <br />
                </div>

                <div
                  *ngIf="planDetailData.quotaUnit"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                >
                  <label class="datalbl" id="quotaaunit">Quota Unit :</label>
                  <span>{{ planDetailData.quotaUnit }}</span>
                  <br />
                </div>

                <div
                  *ngIf="planDetailData.quotatime"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                >
                  <label class="datalbl" id="qutaatime">Quota Time:</label>
                  <span>{{ planDetailData.quotatime }}</span>
                  <br />
                </div>

                <div
                  *ngIf="planDetailData.quotaunittime"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                >
                  <label class="datalbl" id="quotaaunit">Quota Unit Time:</label>
                  <span>{{ planDetailData.quotaunittime }}</span>
                  <br />
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="quotaaareset">Quota Reset Interval :</label>
                  <span>{{ planDetailData.quotaResetInterval }}</span>
                  <br />
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset
            style="margin-top: 0rem; margin-bottom: 2rem"
            *ngIf="
              planDetailData.allowOverUsage &&
              planDetailData.qospolicyid !== null &&
              planDetailData.qospolicyid !== ''
            "
          >
            <legend id="overusage">Overusage Quota QoS Polic</legend>
            <div class="boxWhite">
              <div class="row table-responsive">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th id="from">From (%)</th>
                        <th id="to">To (%)</th>
                        <th id="qospolicyy">QoS Policy</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let qosPolicy of planDetailData.planQosMappingEntityList
                            | paginate
                              : {
                                  id: 'qospolicypageData',
                                  itemsPerPage: qosPolicyDeatilItemPerPage,
                                  currentPage: currentPagecustQosPolicyDeatilList,
                                  totalItems: qosPolicyDeatiltotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ qosPolicy.frompercentage }}</td>
                        <td>{{ qosPolicy.topercentage }}</td>
                        <td>{{ qosPolicy.qosPolicyName }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <pagination-controls
                    (pageChange)="pageChangedQosPolicyDetailList($event)"
                    directionLinks="true"
                    id="qospolicypageData"
                    maxSize="10"
                    nextLabel=""
                    previousLabel=""
                  ></pagination-controls>
                </div>
              </div>
            </div>
          </fieldset>

          <fieldset
            *ngIf="!ifplanGroup_VB && !ifplanGroup_BWB"
            style="margin-top: 0rem; margin-bottom: 2rem"
          >
            <legend id="addiionaldetil">Additional Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                  <label class="datalbl" id="saccodee">SAC Code :</label>
                  <span *ngIf="planDetailData.saccode">
                    {{ planDetailData.saccode }}
                  </span>
                  <span *ngIf="!planDetailData.saccode">-</span>
                </div>
                <div
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                  *ngIf="!isServiceHideField"
                >
                  <label class="datalbl" id="timeepolicyy">TimeBase Policy :</label>
                  <span>{{ planDetailData.timebasepolicyName }}</span>
                </div>

                <div
                  *ngIf="isServiceHideField"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                >
                  <label class="datalbl" id="param1">Param 1 :</label>
                  <span *ngIf="planDetailData.param1">
                    {{ planDetailData.param1 }}
                  </span>
                  <span *ngIf="!planDetailData.param1">-</span>
                </div>
                <div
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                  *ngIf="isServiceHideField"
                >
                  <label class="datalbl" id="param2">Param 2 :</label>
                  <span *ngIf="planDetailData.param2">
                    {{ planDetailData.param2 }}
                  </span>
                  <span *ngIf="!planDetailData.param2">-</span>
                </div>
              </div>
              <div class="row">
                <div
                  *ngIf="!isServiceHideField"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                >
                  <label class="datalbl" id="paramm1">Param 1 :</label>
                  <span *ngIf="planDetailData.param1">
                    {{ planDetailData.param1 }}
                  </span>
                  <span *ngIf="!planDetailData.param1">-</span>
                </div>
                <div
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0"
                  *ngIf="!isServiceHideField"
                >
                  <label class="datalbl" id="paramm2">Param 2 :</label>
                  <span *ngIf="planDetailData.param2">
                    {{ planDetailData.param2 }}
                  </span>
                  <span *ngIf="!planDetailData.param2">-</span>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup m-0">
                  <label class="datalbl" id="paramm3">Param 3 :</label>
                  <span *ngIf="planDetailData.param3">
                    {{ planDetailData.param3 }}
                  </span>
                  <span *ngIf="!planDetailData.param3">-</span>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend id="chrgedetil">Charge Details</legend>
            <div class="boxWhite">
              <div class="row">
                <div
                  *ngIf="planDetailData.category == 'Business Promotion'"
                  class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                >
                  <label class="datalbl" id="newpric">New Offer Price :</label>
                  <span>{{ planDetailData.newOfferPrice }}</span>
                </div>
              </div>
              <div class="row table-responsive">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th id="chrgenamee">Charge Name</th>
                        <th id="chargetypp">Charge Type</th>
                        <th id="pricee">Price</th>
                        <th id="actualpricee">Actual Price</th>
                        <th id="taxnaam">Tax Name</th>
                        <th id="taxamountt">Tax Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let charge of planDetailData.chargeList
                            | paginate
                              : {
                                  id: 'chargepageData',
                                  itemsPerPage: chargeDeatilItemPerPage,
                                  currentPage: currentPagecustChargeDeatilList,
                                  totalItems: chargeDeatiltotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ charge.charge.name }}</td>
                        <td>
                          <!-- {{ charge.charge.chargetype }} -->

                          <span *ngFor="let list of chargeTypeGetDataData; index as j">
                            <span *ngIf="i === j">
                              {{ list.type }}
                            </span>
                          </span>
                        </td>
                        <td>{{ charge.charge.price }}</td>
                        <td>
                          <span *ngIf="charge.chargeprice">{{ charge.chargeprice }}</span>
                          <span *ngIf="!charge.chargeprice">{{ charge.charge.price }}</span>
                        </td>
                        <td>{{ charge.charge.taxName }}</td>
                        <td>
                          <!-- {{ charge.charge.taxamount }} -->
                          <span *ngFor="let list of chargeTaxAmountArray; index as j">
                            <span *ngIf="i === j">
                              {{ list | number: "1.2-2" }}
                            </span>
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <pagination-controls
                    id="pagechangee"
                    (pageChange)="pageChangedChargeDetailList($event)"
                    directionLinks="true"
                    id="chargepageData"
                    maxSize="10"
                    nextLabel=""
                    previousLabel=""
                  ></pagination-controls>
                </div>
              </div>
            </div>
          </fieldset>

          <!-- Overusage Quota QoS Policy -->
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend id="chrgedetil">Overusage Quota QoS Policy</legend>
            <div class="boxWhite">
              <div class="row table-responsive">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th id="chrgenamee">From (%)</th>
                        <th id="chargetypp">To (%)</th>
                        <th id="pricee">QoS Policy</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let qos of planDetailData.planQosMappingEntityList
                            | paginate
                              : {
                                  id: 'chargepageData',
                                  itemsPerPage: chargeDeatilItemPerPage,
                                  currentPage: currentPagecustChargeDeatilList,
                                  totalItems: chargeDeatiltotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ qos.frompercentage }}</td>
                        <td>{{ qos.topercentage }}</td>
                        <td>{{ qos.qosPolicyName }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <pagination-controls
                    id="pagechangee"
                    (pageChange)="pageChangedChargeDetailList($event)"
                    directionLinks="true"
                    id="chargepageData"
                    maxSize="10"
                    nextLabel=""
                    previousLabel=""
                  ></pagination-controls>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend id="poductdetail">Product Details</legend>
            <div class="boxWhite">
              <div class="row table-responsive">
                <div class="col-lg-12 col-md-12">
                  <table class="table">
                    <thead>
                      <tr>
                        <th id="namee">Name</th>
                        <th id="prodcategoryy">Product Category</th>
                        <th id="prodd">Product</th>
                        <th id="prodtypee">Product Type</th>
                        <th id="revisechrge">Revised Charge</th>
                        <th id="ownertyppp">Ownership Type</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let product of planDetailData.productplanmappingList
                            | paginate
                              : {
                                  id: 'productDetailPageData',
                                  itemsPerPage: productDeatilItemPerPage,
                                  currentPage: productPageChargeDeatilList,
                                  totalItems: productDeatiltotalRecords
                                };
                          index as i
                        "
                      >
                        <td>{{ product.name }}</td>
                        <td>
                          <span *ngIf="product.productCategoryName != null">
                            {{ product.productCategoryName }}</span
                          >
                          <span *ngIf="product.productCategoryName == null">-</span>
                        </td>
                        <td>
                          <span *ngIf="product.productName != null">
                            {{ product.productName }}</span
                          >
                          <span *ngIf="product.productName == null">-</span>
                        </td>
                        <td>{{ product.product_type }}</td>
                        <td>
                          <span *ngIf="product.revisedCharge != null">
                            {{ product.revisedCharge }}</span
                          >
                          <span *ngIf="product.revisedCharge == null">-</span>
                        </td>
                        <td>{{ product.ownershipType }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <pagination-controls
                    (pageChange)="pageChangedProductPlanMappingDetailList($event)"
                    directionLinks="true"
                    id="productDetailPageData"
                    maxSize="5"
                    nextLabel=""
                    previousLabel=""
                  >
                  </pagination-controls>
                </div>
              </div>
            </div>
          </fieldset>
          <fieldset>
            <legend id="planworkingaudit">Plan Workflow Audit</legend>
            <div class="boxWhite">
              <div class="table-responsive">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th id="plannaamm">Plan Name</th>
                          <th id="actionn">Action</th>
                          <th id="staffnaamm">Staff name</th>
                          <th id="remarkk">Remark</th>
                          <th id="actionndatee">Action Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let data of workflowAuditData1
                              | paginate
                                : {
                                    id: 'searchMasterPageData',
                                    itemsPerPage: MasteritemsPerPage1,
                                    currentPage: currentPageMasterSlab1,
                                    totalItems: MastertotalRecords1
                                  };
                            index as i
                          "
                        >
                          <td>
                            <div *ngIf="data.entityName">
                              {{ data.entityName }}
                            </div>
                            <div *ngIf="data.planName">
                              {{ data.planName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.action }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.actionByName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.remark }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.actionDateTime | date: "yyyy-MM-dd hh:mm a" }}
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <br />
                    <div class="pagination_Dropdown">
                      <pagination-controls
                        (pageChange)="pageChangedMasterList($event)"
                        directionLinks="true"
                        id="searchMasterPageData"
                        maxSize="10"
                        nextLabel=""
                        previousLabel=""
                      ></pagination-controls>
                      <div id="itemPerPageDropdown">
                        <!-- <p-dropdown
                                                  [options]="pageLimitOptions"
                                                  optionLabel="value"
                                                  optionValue="value"
                                                  (onChange)="TotalItemPerPageWorkFlow($event)"
                                                ></p-dropdown> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>
          <br />
          <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
            <legend id="audittdetil">Audit Details</legend>
            <div class="boxWhite">
              <div class="table-responsive">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th id="auditdatee">Audit Date</th>
                          <th id="empame">Enployee Name</th>
                          <th id="modulee">Module</th>
                          <th id="operationn">Operation</th>
                          <th id="remarkk">Remark</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let audit of AuditData1
                              | paginate
                                : {
                                    id: 'searchAuditPageData',
                                    itemsPerPage: AudititemsPerPage1,
                                    currentPage: currentPageAuditSlab1,
                                    totalItems: AudittotalRecords1
                                  };
                            index as i
                          "
                        >
                          <td style="width: 5%">{{ audit.auditDate }}</td>
                          <td style="width: 5%">{{ audit.employeeName }}</td>
                          <td style="width: 5%">{{ audit.module }}</td>
                          <td style="width: 5%">{{ audit.operation }}</td>
                          <td>{{ audit.remark }}</td>
                        </tr>
                      </tbody>
                    </table>
                    <br />
                    <div class="pagination_Dropdown">
                      <pagination-controls
                        (pageChange)="pageChangedAuditList($event)"
                        directionLinks="true"
                        id="searchAuditPageData"
                        maxSize="10"
                        nextLabel=""
                        previousLabel=""
                      ></pagination-controls>
                      <div id="itemPerPageDropdown"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- service area list -->
<!-- <div class="modal fade" id="serviceareaModal" role="dialog">
  <div class="modal-dialog" style="width: 35%">
    <div class="modal-content">
      <div class="modal-header">
        <button class="close" data-dismiss="modal" type="button">&times;</button>
        <h3 class="panel-title">{{ planDetailData.name }} Service Area List</h3>
      </div>
      <div class="modal-body">
        <div class="panel-body table-responsive" id="networkDeviceTabel">
          <table class="table">
            <tbody>
              <tr>
                <td><label class="networkLabel" id="serviceareaa">Service Area :</label></td>
                <td>
                  <span
                    *ngFor="let serviceName of planDetailData.serviceAreaNameList"
                    style="word-break: break-all"
                  >
                    <span>
                      {{ serviceName.name }},
                      <br />
                    </span>
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div> -->

<p-dialog
  header="{{ planDetailData.name }} Service Area List"
  [(visible)]="showServiceArea"
  [style]="{ width: '50%' }"
  [modal]="true"
>
  <ng-template pTemplate="content">
    <div class="panel-body table-responsive" id="networkDeviceTabel">
      <table class="table">
        <tbody>
          <tr>
            <td><label class="networkLabel" id="serviceareaa">Service Area :</label></td>
            <td>
              <span
                *ngFor="let serviceName of planDetailData.serviceAreaNameList"
                style="word-break: break-all"
              >
                <span>
                  {{ serviceName.name }},
                  <br />
                </span>
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="addUpdateBtn">
      <button (click)="showServiceArea = false" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </ng-template>
</p-dialog>

<!-- Qos policy -->
<p-dialog
  header="{{ planDetailData.qospolicyName }} Qos Policy"
  [(visible)]="qosPolicyID"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="min-height: 40rem; max-height: 65rem; overflow: auto">
    <div class="panel-body table-responsive" id="networkDeviceTabel">
      <table class="table">
        <tbody>
          <tr>
            <td><label class="networkLabel" id="nameee">Name :</label></td>
            <td>
              <span>{{ viewQosPolicyListData.name }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="policynaamm">Policy Name :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.thpolicyname }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="basepolicynamme">Base Policy Name :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.basepolicyname }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="descriptionn">Description :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.description }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="downloadspeedd">Download Speed :</label>
            </td>
            <td *ngFor="let data of viewQosPolicyListData1">
              <span>{{ data.downloadSpeed }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="basedownloadspeedd">Base Download Speed :</label>
            </td>
            <td *ngFor="let data of viewQosPolicyListData1">
              <span>{{ data.baseDownloadSpeed }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="baseuploadspeedd">Base Upload Speed :</label>
            </td>
            <td *ngFor="let data of viewQosPolicyListData1">
              <span>{{ data.baseUploadSpeed }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="parammm1">Param 1 :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.thparam1 }}</span>
            </td>
          </tr>
          <tr>
            <td>
              <label class="networkLabel" id="parammm2">Param 2 :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.thparam2 }}</span>
            </td>
          </tr>

          <tr>
            <td>
              <label class="networkLabel" id="paramm3">Param 3 :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.thparam3 }}</span>
            </td>
          </tr>

          <tr>
            <td>
              <label class="networkLabel" id="baseeparam1">Base Param 1 :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.baseparam1 }}</span>
            </td>
          </tr>

          <tr>
            <td>
              <label class="networkLabel" id="baseeparam2">Base Param 2 :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.baseparam2 }}</span>
            </td>
          </tr>

          <tr>
            <td>
              <label class="networkLabel" id="baseeparam3">Base Param 3 :</label>
            </td>
            <td>
              <span>{{ viewQosPolicyListData.baseparam3 }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-default" (click)="closeQosPolicyID()" type="button">Close</button>
  </div>
</p-dialog>
<p-dialog
  header="Approve Plan"
  [(visible)]="assignApporvePlanModal"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
  (onHide)="approvePlanClosed()"
>
  <div class="modal-body" style="margin: 10px">
    <form [formGroup]="assignPlanForm">
      <div class="row">
        <div class="row">
          <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="row">
              <div class="col-md-6">
                <input
                  id="searchStaffName"
                  type="text"
                  name="username"
                  class="form-control"
                  placeholder="Global Search Filter"
                  [(ngModel)]="searchStaffDeatil"
                  (keydown.enter)="searchStaffByName()"
                  [ngModelOptions]="{ standalone: true }"
                />
              </div>
              <div class="col-lg-6 col-md-6 col-sm-12">
                <button
                  (click)="searchStaffByName()"
                  class="btn btn-primary"
                  id="searchbtn"
                  type="submit"
                  [disabled]="!searchStaffDeatil"
                >
                  <i class="fa fa-search"></i>
                  Search
                </button>
                <button
                  (click)="clearSearchForm()"
                  class="btn btn-default"
                  id="searchbtn"
                  type="reset"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
              </div>
            </div>
            <div class="card">
              <h5 id="selectstafff">Select Staff</h5>
              <p-table
                [(selection)]="selectStaff"
                [value]="approvePlanData"
                responsiveLayout="scroll"
                [paginator]="true"
                [rows]="5"
                [rowsPerPageOptions]="[5, 10, 15, 20]"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 3rem"></th>
                    <th id="nameee">Name</th>
                    <th id="username">Username</th>
                  </tr>
                </ng-template>
                <ng-template let-product pTemplate="body">
                  <tr>
                    <td>
                      <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                    </td>
                    <td>{{ product.fullName }}</td>
                    <td>
                      {{ product.username }}
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
          <div *ngIf="!approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <label id="remarkk">Remark*</label>
            <textarea
              [ngClass]="{
                'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
              }"
              class="form-control"
              formControlName="remark"
              name="remark"
            ></textarea>
            <div
              *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
                class="error text-danger"
              >
                Remark is required.
              </div>
            </div>
          </div>
          <br />
        </div>
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="assignPlan()"
      *ngIf="!approved"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Approve
    </button>
    <button
      (click)="assignToStaff(true)"
      *ngIf="approved"
      class="btn btn-primary"
      id="submitButtonForApprove"
      type="submit"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      (click)="approvePlanClosed()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="assignApporvePlanModal"
  role="dialog"
  tabindex="-1"
>
  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important" id="approvvpln">
          Approve Plan
        </h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="assignPlanForm">
          <div class="row">
            <div class="row">
              <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card">
                  <h5 id="selectstafff">Select Staff</h5>
                  <p-table
                    [(selection)]="selectStaff"
                    [value]="approvePlanData"
                    responsiveLayout="scroll"
                  >
                    <ng-template pTemplate="header">
                      <tr>
                        <th style="width: 3rem"></th>
                        <th id="nameee">Name</th>
                        <th id="username">Username</th>
                      </tr>
                    </ng-template>
                    <ng-template let-product pTemplate="body">
                      <tr>
                        <td>
                          <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                        </td>
                        <td>{{ product.fullName }}</td>
                        <td>
                          {{ product.username }}
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
              </div>
              <div *ngIf="!approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <label id="remarkk">Remark*</label>
                <textarea
                  [ngClass]="{
                    'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
                  }"
                  class="form-control"
                  formControlName="remark"
                  name="remark"
                ></textarea>
                <div
                  *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
              <br />
            </div>
          </div>
          
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="assignPlan()"
          *ngIf="!approved"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Approve
        </button>
        <button
          (click)="assignToStaff(true)"
          *ngIf="approved"
          class="btn btn-primary"
          id="submitButtonForApprove"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div> -->
  <!-- <div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="rejectPlanModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Reject Plan</h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="rejectPlanForm">
          <div class="row">
            <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <h5 id="selectstaff">Select Staff</h5>
                <p-table
                  [(selection)]="selectStaffReject"
                  [value]="rejectPlanData"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th id="naaam">Name</th>
                      <th id="usernaaam">Username</th>
                    </tr>
                  </ng-template>
                  <ng-template let-product pTemplate="body">
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div *ngIf="!reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label id="remarkk">Remark*</label>
              <textarea
                [ngClass]="{
                  'is-invalid': rejectPlanSubmitted && rejectPlanForm.controls.remark.errors
                }"
                class="form-control"
                formControlName="remark"
                name="remark"
              ></textarea>
              <div
                *ngIf="rejectPlanSubmitted && rejectPlanForm.controls.remark.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="rejectPlanSubmitted && rejectPlanForm.controls.remark.errors.required"
                  class="error text-danger"
                >
                  Remark is required.
                </div>
              </div>
            </div>
            <br />
          </div>
          
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="rejectPlan()"
          *ngIf="!reject"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Reject
        </button>
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject && !selectStaffReject"
          class="btn btn-primary"
          disabled
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject && selectStaffReject"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div> -->

  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Change Status</h4>
      </div>
      <div class="modal-body" style="height: 15rem">
        <div>
          <div class="dataGroup">
            <label class="datalbl" id="currentstatus">Current Status :</label>
            <span>{{ currentStatus }}</span>
          </div>
        </div>
        <div class="row" style="margin-top: 1rem">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p-dropdown
              id="plannstatuss"
              [(ngModel)]="changeStatusValue"
              [options]="planStatus"
              filter="true"
              filterBy="label"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a New Status"
            ></p-dropdown>
            <br />
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          (click)="ChangeStatus()"
          [disabled]="!changeStatusValue"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Save
        </button>
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div> -->
  <div
    aria-labelledby="myModalLabel"
    class="modal fade"
    id="taxDetailModal"
    role="dialog"
    tabindex="-1"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button aria-label="Close" class="close" data-dismiss="modal" type="button">
            <span aria-hidden="true">&times;</span>
          </button>
          <h4 class="modal-title" id="myModalLabel" style="color: #fff !important" id="taxdetailss">
            Tax Details
          </h4>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <p-table [value]="taxDetails" responsiveLayout="scroll">
                  <ng-template pTemplate="header">
                    <tr>
                      <th id="taxxnaam">Tax Name</th>
                      <th id="taxxamout">Tax Amount (%)</th>
                    </tr>
                  </ng-template>
                  <ng-template let-product pTemplate="body">
                    <tr>
                      <td>{{ product.name }}</td>
                      <td>
                        {{ product.rate }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <br />
          </div>
        </div>
        <div class="modal-footer">
          <button
            class="btn btn-default"
            (click)="closeModalForStatusFlag()"
            data-dismiss="modal"
            type="button"
            id="close"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important" id="approveplannn">
          Approve Plan
        </h4>
      </div>
    
    </div>
  </div>
</div> -->
</div>

<p-dialog
  header="Policy Detail"
  [(visible)]="changeStatusFlag"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModalForStatusFlag()"
>
  <div class="modal-body" style="height: 15rem">
    <div>
      <div class="dataGroup">
        <label class="datalbl" id="currentstatus">Current Status :</label>
        <span>{{ currentStatus }}</span>
      </div>
    </div>
    <div class="row" style="margin-top: 1rem">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <p-dropdown
          id="plannstatuss"
          [(ngModel)]="changeStatusValue"
          [options]="planStatus"
          filter="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a New Status"
        ></p-dropdown>
        <br />
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      (click)="ChangeStatus()"
      [disabled]="!changeStatusValue"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Save
    </button>
    <button
      class="btn btn-default"
      data-dismiss="modal"
      (click)="closeModalForStatusFlag()"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Approve Plan"
  [(visible)]="reAssignPlanModel"
  [modal]="true"
  [style]="{ width: '40%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModalForreAssign()"
>
  <div class="modal-body">
    <form [formGroup]="assignPlanForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="card">
            <h5 id="selecttstaff">Select Staff</h5>
            <p-table
              [value]="approvableStaff"
              [(selection)]="selectStaff"
              responsiveLayout="scroll"
            >
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 3rem"></th>
                  <th id="naam">Name</th>
                  <th id="usernaamm">Username</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-product>
                <tr>
                  <td>
                    <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                  </td>
                  <td>{{ product.fullName }}</td>
                  <td>
                    {{ product.username }}
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label id="remarkk">Remark</label>
          <textarea
            class="form-control"
            name="remark"
            formControlName="remark"
            [ngClass]="{
              'is-invalid': assignPlansubmitted && assignPlanForm.controls.remark.errors
            }"
          ></textarea>
          <div
            class="errorWrap text-danger"
            *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors"
          >
            <div
              class="error text-danger"
              *ngIf="assignPlansubmitted && assignPlanForm.controls.remark.errors.required"
            >
              Remark is required.
            </div>
          </div>
          <br />
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      type="submit"
      class="btn btn-primary"
      id="submit"
      (click)="reassignWorkflow()"
      [disabled]="!selectStaff"
    >
      <i class="fa fa-check-circle"></i>
      Assign
    </button>
    <button
      type="button"
      (click)="closeModalForreAssign()"
      class="btn btn-default"
      data-dismiss="modal"
    >
      Close
    </button>
  </div>
</p-dialog>

<p-dialog
  header="Reject Plan"
  [(visible)]="rejectPlanModal"
  [modal]="true"
  [style]="{ width: '40%' }"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeModalRejectPlan()"
>
  <div class="modal-body">
    <form [formGroup]="rejectPlanForm">
      <div class="row">
        <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="card">
            <h5 id="selectstaff">Select Staff</h5>
            <p-table
              [(selection)]="selectStaffReject"
              [value]="rejectPlanData"
              responsiveLayout="scroll"
            >
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 3rem"></th>
                  <th id="naaam">Name</th>
                  <th id="usernaaam">Username</th>
                </tr>
              </ng-template>
              <ng-template let-product pTemplate="body">
                <tr>
                  <td>
                    <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                  </td>
                  <td>{{ product.fullName }}</td>
                  <td>
                    {{ product.username }}
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>
        <div *ngIf="!reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <label id="remarkk">Remark*</label>
          <textarea
            [ngClass]="{
              'is-invalid': rejectPlanSubmitted && rejectPlanForm.controls.remark.errors
            }"
            class="form-control"
            formControlName="remark"
            name="remark"
          ></textarea>
          <div
            *ngIf="rejectPlanSubmitted && rejectPlanForm.controls.remark.errors"
            class="errorWrap text-danger"
          >
            <div
              *ngIf="rejectPlanSubmitted && rejectPlanForm.controls.remark.errors.required"
              class="error text-danger"
            >
              Remark is required.
            </div>
          </div>
        </div>
        <br />
      </div>
      <!-- <input type="file" formControlName="fileName" name="fileName"> -->
    </form>
  </div>
  <div class="modal-footer">
    <button
      (click)="rejectPlan()"
      *ngIf="!reject"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Reject
    </button>
    <button
      (click)="assignToStaff(false)"
      *ngIf="reject && !selectStaffReject"
      class="btn btn-primary"
      disabled
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button
      (click)="assignToStaff(false)"
      *ngIf="reject && selectStaffReject"
      class="btn btn-primary"
      id="submit"
      type="submit"
    >
      <i class="fa fa-times-circle"></i>
      Assign
    </button>
    <button
      class="btn btn-default"
      (click)="closeModalRejectPlan()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>
