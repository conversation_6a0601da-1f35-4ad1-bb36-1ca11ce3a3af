button[type="reset"] {
  padding: 0;
  margin-left: 5px;
  background-color: transparent;
  border: none;
  outline: none;
  color: #000 !important;
  vertical-align: middle;
  margin-top: 10px;
}

:host ::ng-deep .p-dialog .p-dialog-header {
  background: #f7b206 !important;
}
:host ::ng-deep .p-tabview-nav {
  display: flex;
  flex-wrap: nowrap;
  list-style-type: none;
  margin: 0;
  margin-top: 15px;
  padding: 0;
}
.close-button {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

:host ::ng-deep .p-tabview .p-tabview-nav li {
  width: unset !important;
  margin-right: unset !important;
}

:host ::ng-deep .p-tabview .p-tabview-nav li .p-tabview-nav-link:hover,
:host ::ng-deep .p-tabview .p-tabview-nav li .p-tabview-nav-link:focus {
  outline: none !important;
  box-shadow: none !important;
}
.closeBtn {
  text-align: center;
}
:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border-bottom: none !important; 
  padding: 10px;
  box-shadow: none !important;
}
:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav {
  display: flex;
  flex-wrap: nowrap;
  list-style-type: none;
  margin: 0;
  margin-top: 15px;
  padding: 0;
}


:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li {
  width: unset !important;
  margin-right: unset !important;
}

:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li .p-tabview-nav-link:hover,
:host ::ng-deep .second-tabView .p-tabview .p-tabview-nav li .p-tabview-nav-link:focus {
  outline: none !important;
  box-shadow: none !important;
  border-bottom: 2px solid #f7b206 !important;

}
