<!DOCTYPE html>
<html lang="en" class="fullscreen-bg">
  <head>
    <title>Login | Adopt Dashboard</title>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <!-- VENDOR CSS -->
    <!-- <link rel="stylesheet" href="assets/vendor/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/vendor/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="assets/vendor/linearicons/style.css"> -->
    <!-- MAIN CSS -->
    <!-- <link rel="stylesheet" href="assets/css/main.css"> -->

    <!-- GOOGLE FONTS -->
    <link
      href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700"
      rel="stylesheet"
    />
    <!-- ICONS -->
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="assets/img/apple-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="96x96"
      href="assets/img/favicon.png"
    />
  </head>

  <body class="loginbg">
    <p-toast
      [style]="{ height: 'auto', width: '20vw', fontSize: '16px' }"
    ></p-toast>
    <!-- WRAPPER -->
    <div id="wrapper" pFocusTrap>
      <div class="vertical-align-wrap">
        <div class="vertical-align-middle">
          <div class="auth-box">
            <div class="left">
              <div class="content">
                <div class="header">
                  <div class="logo text-center">
                    <img
                      src="../../assets/img/loginlogo.jpg"
                      alt="Adopt Logo"
                      class="adoptlogo"
                    />
                  </div>
                  <p class="lead">Reset Password</p>
                </div>
                <form
                  class="form-auth-small"
                  [formGroup]="generatePasswordForm"
                >
                  <div class="form-group">
                    <p class="text-left">Username*</p>
                    <label for="signin-email" class="control-label sr-only"
                      >Username</label
                    >
                    <div class="input-wrapper">
                      <input
                        type="email"
                        name="userName"
                        class="form-control custom-input"
                        id="username"
                        formControlName="userName"
                        [ngClass]="{
                          'is-invalid':
                            submitted &&
                            generatePasswordForm.controls.userName.errors
                        }"
                        placeholder="Username"
                      />
                    </div>
                    <div
                      class="errorWrap text-danger"
                      style="text-align: left"
                      *ngIf="
                        submitted &&
                        generatePasswordForm.controls.userName.errors
                      "
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          generatePasswordForm.controls.userName.errors.required
                        "
                      >
                        Username is required
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <p class="text-left">Old Password*</p>
                    <label for="signin-email" class="control-label sr-only"
                      >Old Password</label
                    >
                    <div class="input-wrapper">
                      <input
                        type="password"
                        name="password"
                        class="form-control custom-input"
                        id="oldPassword"
                        formControlName="oldPassword"
                        [ngClass]="{
                          'is-invalid':
                            submitted &&
                            generatePasswordForm.controls.oldPassword.errors
                        }"
                        placeholder="Enter Old Password"
                      />
                    </div>
                    <div
                      class="errorWrap text-danger"
                      style="text-align: left"
                      *ngIf="
                        submitted &&
                        generatePasswordForm.controls.oldPassword.errors
                      "
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          generatePasswordForm.controls.oldPassword.errors
                            .required
                        "
                      >
                        Old Password is required
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <p class="text-left">New Password*</p>
                    <label for="signin-email" class="control-label sr-only"
                      >New Password</label
                    >
                    <div class="input-wrapper">
                      <input
                        type="password"
                        name="password"
                        class="form-control custom-input"
                        id="newPassword"
                        formControlName="newPassword"
                        [ngClass]="{
                          'is-invalid':
                            submitted &&
                            generatePasswordForm.controls.newPassword.errors
                        }"
                        placeholder="Enter New Password"
                      />
                    </div>
                    <div
                      class="errorWrap text-danger"
                      style="text-align: left"
                      *ngIf="
                        submitted &&
                        generatePasswordForm.controls.newPassword.errors
                      "
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          generatePasswordForm.controls.newPassword.errors
                            .required
                        "
                      >
                        New Password is required
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <p class="text-left">Confirm Password*</p>
                    <label for="signin-password" class="control-label sr-only"
                      >Password</label
                    >
                    <div class="input-wrapper">
                      <input
                        type="{{ showPassword ? 'text' : 'password' }}"
                        name="password"
                        class="form-control custom-input"
                        id="confirmpassword"
                        placeholder="Enter Confirm Password"
                        formControlName="confirmpassword"
                        [ngClass]="{
                          'is-invalid':
                            submitted &&
                            generatePasswordForm.controls.confirmpassword.errors
                        }"
                      />
                      <button
                        type="button"
                        class="btn btn-light btn-sm position-absolute toggle-password"
                        (click)="showPassword = !showPassword"
                      >
                        <i
                          style="font-size: 1.8rem"
                          [ngClass]="
                            showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'
                          "
                        ></i>
                      </button>
                    </div>
                    <div
                      class="errorWrap text-danger"
                      style="text-align: left"
                      *ngIf="
                        submitted &&
                        generatePasswordForm.controls.confirmpassword.errors
                      "
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          generatePasswordForm.controls.confirmpassword.errors
                            .required
                        "
                      >
                        Confirm Password is required
                      </div>
                    </div>
                    <div
                      class="errorWrap text-danger"
                      style="text-align: left"
                      *ngIf="
                        submitted &&
                        generatePasswordForm.controls.newPassword.value &&
                        generatePasswordForm.controls.confirmpassword.value &&
                        generatePasswordForm.errors?.mismatch
                      "
                    >
                      <div class="error text-danger">
                        Password and Confirm Password do not match
                      </div>
                    </div>
                  </div>

                  <button
                    type="submit"
                    class="btn btn-primary btn-lg"
                    data-title="Save"
                    data-toggle="tooltip"
                    data-placement="bottom"
                    (click)="savePassword()"
                  >
                    <i class="fa fa-check-circle"></i> Reset Password
                  </button>
                  <div class="bottom">
                    <span class="helper-text"
                      ><a href="javascript:void(0)" [routerLink]="['/login']"
                        >Login ?</a
                      ></span
                    >
                  </div>
                </form>
              </div>
            </div>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- END WRAPPER -->
  </body>
</html>
