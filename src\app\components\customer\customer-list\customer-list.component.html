<div class="row">
  <div class="col-md-12">
    <div class="panel mb-15">
      <div class="panel-heading">
        <h3 class="panel-title">{{ custTitle }} Management</h3>
        <div class="right">
          <button
            aria-controls="searchPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="searchPreCust">
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3 m-b-10">
              <p-dropdown
                [(ngModel)]="searchOption"
                [filter]="true"
                [options]="searchOptionSelect"
                filterBy="label"
                optionLabel="label"
                optionValue="value"
                placeholder="Select a Search Option"
                (onChange)="selSearchOption($event)"
              ></p-dropdown>
              <!-- <select
              class="form-control"
              (change)="selSearchOption($event)"
              name="custtype"
              [(ngModel)]="searchOption"
              style="width: 100%;"
            >
              <option value="">Select Search Option</option>
              <option value="name">Firstname</option>
              <option value="username">Username</option>
              <option value="email">Email</option>
              <option value="mobile">Phone</option>
              <option value="any">Any</option>
            </select> -->
            </div>
            <div
              *ngIf="
                searchOption != 'status' &&
                searchOption != 'cafStatus' &&
                searchOption != 'custtype' &&
                searchOption != 'currentAssigneeName' &&
                searchOption != 'cafCreatedDate' &&
                searchOption != 'currentAssignedTeam' &&
                searchOption != 'currentAssignedTeamAndStaff' &&
                searchOption != 'expiryDate' &&
                searchOption != 'subscriptionMode' &&
                searchOption != 'firstactivationdate' &&
                searchOption != 'createbyname' &&
                searchOption != 'activationbyname'
              "
              class="col-lg-3 col-md-3 m-b-10"
            >
              <input
                [(ngModel)]="searchDeatil"
                class="form-control"
                id="username"
                placeholder="Enter Search Detail"
                type="text"
                (keydown.enter)="searchCustomer()"
              />
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption === 'status' || searchOption === 'cafStatus'"
            >
              <p-dropdown
                [options]="commondropdownService.CustomerStatusValue"
                optionValue="value"
                optionLabel="text"
                filter="true"
                filterBy="text"
                placeholder="Select a Status"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'custtype'">
              <p-dropdown
                [options]="commondropdownService.customerTypeSearchOption"
                optionValue="value"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Customer Type"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'subscriptionMode'">
              <p-dropdown
                id="custtype"
                [options]="customerType"
                (onChange)="getCustomerType($event)"
                filter="true"
                filterBy="text"
                [(ngModel)]="searchDeatil"
                optionLabel="text"
                optionValue="text"
                placeholder="Select Subscription Type"
              >
              </p-dropdown>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'currentAssigneeName'">
              <p-dropdown
                [options]="commondropdownService.activeStaffList"
                optionValue="username"
                optionLabel="username"
                filter="true"
                filterBy="username"
                placeholder="Select a Assigned Staff"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption === 'createbyname' || searchOption === 'activationbyname'"
            >
              <p-dropdown
                [options]="commondropdownService.activeStaffList"
                optionValue="fullName"
                optionLabel="fullName"
                filter="true"
                filterBy="fullName"
                placeholder="Select a Full Name"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchOption === 'currentAssignedTeam'">
              <p-dropdown
                [options]="commondropdownService.teamListData"
                optionValue="name"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a Assigned Team"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="searchOption === 'currentAssignedTeamAndStaff'"
            >
              <p-dropdown
                [options]="commondropdownService.teamListData"
                optionValue="name"
                optionLabel="name"
                filter="true"
                filterBy="name"
                placeholder="Select a Assigned Team for Staff"
                [(ngModel)]="searchDeatil"
              ></p-dropdown>
            </div>
            <div
              class="col-lg-3 col-md-3 m-b-10"
              *ngIf="
                searchOption === 'cafCreatedDate' ||
                searchOption === 'expiryDate' ||
                searchOption === 'firstactivationdate'
              "
            >
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="searchDeatil"
                placeholder="Enter Date"
              ></p-calendar>
            </div>
            <div class="col-lg-3 col-md-3 m-b-10">
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="fromDate"
                placeholder="Select Start Date"
              ></p-calendar>
            </div>

            <div class="col-lg-3 col-md-3 m-b-10">
              <p-calendar
                [hideOnDateTimeSelect]="true"
                [showButtonBar]="true"
                [showIcon]="true"
                [style]="{ width: '100%' }"
                dateFormat="yy-MM-dd"
                [(ngModel)]="toDate"
                placeholder="Select End Date"
              ></p-calendar>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-4 col-md-6 col-sm-12" style="margin-top: 15px">
              <button
                (click)="searchCustomer()"
                class="btn btn-primary"
                id="searchbtn"
                type="button"
                [disabled]="!searchDeatil && !fromDate && !toDate"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                (click)="clearSearchcustomer()"
                class="btn btn-default"
                id="searchbtn"
                type="reset"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
              <button
                class="btn btn-success"
                (click)="exportCustomer()"
                id="searchbtn"
                type="reset"
                [disabled]="!customerListData || customerListData.length === 0"
              >
                <i class="fa fa-file-excel-o"></i>
                Export
              </button>
            </div>
          </div>
        </div>
        <!-- <div class="panel-body no-padding panel-udata">
          <div [ngClass]="'col-md-6'" class="pcol">
            <div
              [ngClass]="{
                activeSubMenu: true
              }"
              class="dbox"
            >
              <a
                [routerLink]="['/home/<USER>/Prepaid/create']"
                class="curson_pointer"
                href="javascript:void(0)"
              >
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Create {{ custType }} Customer</h5>
              </a>
            </div>
          </div>
          <div [ngClass]="'col-md-6'" class="pcol">
            <div
              [ngClass]="{
                activeSubMenu: true
              }"
              class="dbox"
            >
              <a class="curson_pointer" href="javascript:void(0)">
                <img src="../../../assets/img/i01.png" style="width: 32px" />
                <h5>Search {{ custType }} Customer</h5>
              </a>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ custTitle }} List</h3>
        <div class="right">
          <button
            aria-controls="listPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#listPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="listPreCust">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th width="17%">Name</th>
                    <th width="10%">AAA Username</th>
                    <th width="13%">Service Area</th>
                    <th width="10%">Mobile Number</th>
                    <th width="18%">Account No</th>
                    <th width="12%">Connection Status</th>
                    <th width="12%">ISP Name</th>
                    <th width="20%">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of customerListData
                        | paginate
                          : {
                              id: 'customerListpageData',
                              itemsPerPage: customerListdataitemsPerPage,
                              currentPage: currentPagecustomerListdata,
                              totalItems: customerListdatatotalRecords
                            };
                      index as i
                    "
                  >
                    <td>
                      <a
                        [routerLink]="['/home/<USER>/details/' + custType + '/x/', data.id]"
                        style="color: #f7b206"
                        href="javascript:void(0)"
                      >
                        {{ data.name }}
                      </a>
                    </td>
                    <td>{{ data.username }}</td>
                    <td>{{ data.serviceArea }}</td>
                    <td>{{ data.mobile }}</td>
                    <td>{{ data.acctno }}</td>
                    <td *ngIf="data.connectionMode">
                      <div
                        *ngIf="data.connectionMode == 'online' || data.connectionMode == 'Online'"
                      >
                        <span class="badge badge-success">
                          {{ data.connectionMode }}
                        </span>
                      </div>
                      <div *ngIf="data.connectionMode == 'Offline'">
                        <span class="badge offlineStatus">
                          {{ data.connectionMode }}
                        </span>
                      </div>
                    </td>
                    <td *ngIf="!data.connectionMode">-</td>
                    <td>{{ data.mvnoName }}</td>
                    <td class="btnAction">
                      <button
                        *ngIf="addNotesAccess"
                        style="
                          border: none;
                          background: transparent;
                          padding: 0;
                          margin-right: 3px;
                          cursor: pointer;
                        "
                        aria-label="Add Notes"
                        id=" addNotes"
                        (click)="addNotesSetFunction(data.id)"
                        data-toggle="tooltip"
                        data-placement="top"
                        title="Add Notes"
                      >
                        <img src="assets/img/icons-03.png" />
                      </button>
                      <a
                        *ngIf="editAccess"
                        [routerLink]="['/home/<USER>/edit/', custType, data.id]"
                        href="javascript:void(0)"
                        id="editbutton"
                        title="Edit"
                        type="button"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        *ngIf="uploadAccess"
                        [routerLink]="['/home/<USER>/', custType, data.id]"
                        data-placement="mb-15"
                        data-toggle="tooltip"
                        id="editbutton"
                        title="Upload Documents"
                        type="button"
                      >
                        <img height="32" src="assets/img/up.jpg" width="32" />
                      </a>
                      <a
                        *ngIf="nearByDeviceAccess"
                        class="detailOnAnchorClick"
                        (click)="openNearLocationModal(data.id)"
                        title="Near By Device"
                      >
                        <img height="32" src="assets/img/A_Nearby_Device_Y.png" width="32" />
                      </a>
                      <a
                        *ngIf="
                          this.commondropdownService.ifPaytmLinkSendBtn && sendPaymentLinkAccess
                        "
                        (click)="getPaytmLink(data.id)"
                        class="detailOnAnchorClick"
                        title="Send Paytem Link"
                      >
                        <img height="32" src="assets/img/money.png" width="32" />
                      </a>
                      <a
                        *ngIf="changeStatusAccess"
                        (click)="openChangeStatus(data.id, data.status)"
                        class="detailOnAnchorClick"
                        title="Change Status"
                      >
                        <img class="icon" src="assets/img/E_Status_Y.png" />
                      </a>

                      <button
                        (click)="stopBilling(data.id)"
                        *ngIf="data.isinvoicestop !== true && isPlanOnDemand"
                        class="approve-btn"
                        style="
                          border: none;
                          background: #f7b206;
                          padding: 3.5px 7px;
                          margin: 3px 3px 0 0;
                        "
                        title="Stop Billing"
                        type="button"
                      >
                        <i class="fa fa-pause-circle-o" style="color: white; font-size: 14px"></i>
                      </button>

                      <!-- [disabled]="data.isinvoicestop == false" -->
                      <button
                        (click)="playstartBilling(data.id)"
                        *ngIf="data.isinvoicestop == true && isPlanOnDemand"
                        class="approve-btn"
                        style="
                          border: none;
                          background: #f7b206;
                          padding: 3.5px 7px;
                          margin: 3px 3px 0 0;
                        "
                        title="Start Billing"
                        type="button"
                      >
                        <i class="fa fa-play-circle" style="color: white; font-size: 14px"></i>
                      </button>
                      <!-- <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="Invoice Payment"
                        (click)="openPaymentGateways(data.id,false)"
                      >
                        <img
                          src="assets/img/19_Promise-to-Pay_Y.png"
                          alt="Invoice Payment"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button> -->
                      <button
                        *ngIf="renewPaymentLinkAccess"
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="Renew Payment"
                        (click)="openRenewPaymentGateways(data.id, true)"
                      >
                        <img
                          src="assets/img/refresh.png"
                          alt="Renew Payment"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                      <button
                        class="approve-btn"
                        style="border: none; background: transparent; padding: 0"
                        title="View Details"
                        (click)="openModal(data.id)"
                      >
                        <img
                          src="assets/img/eye-icon.png"
                          alt="View Details"
                          style="width: 25px; height: 25px; margin-right: 3px"
                        />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div style="display: flex">
                <pagination-controls
                  (pageChange)="pageChangedcustomerList($event)"
                  directionLinks="true"
                  id="customerListpageData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="TotalItemPerPage($event)"
                    [options]="pageLimitOptions"
                    [(ngModel)]="customerListdataitemsPerPage"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Add Notes"
  [(visible)]="addNotesPopup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeNotesModal()"
>
  <div class="modal-body">
    <form [formGroup]="addNotesForm">
      <div>
        <label class="datalbl">Notes: </label>
        <textarea
          type="text"
          class="form-control"
          id="notes"
          placeholder="Enter Notes..."
          formControlName="notes"
          [ngClass]="{
            'is-invalid': notesSubmitted && addNotesForm.controls.notes.errors
          }"
        ></textarea>

        <div
          class="errorWrap text-danger"
          *ngIf="notesSubmitted && addNotesForm.controls.notes.errors"
        >
          <div
            class="error text-danger"
            *ngIf="notesSubmitted && addNotesForm.controls.notes.errors.required"
          >
            Notes is required.
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="submit"
        id="submit"
        (click)="saveNotes(this.custIdForNotes)"
        class="btn btn-success btn-sm"
      >
        Save
      </button>
      <button
        type="button"
        class="btn btn-danger btn-sm"
        (click)="closeNotesModal()"
        data-dismiss="modal"
      >
        Close
      </button>
    </div>
  </div>
  <!-- </div>
    </div>
  </div> -->
</p-dialog>
<app-customer-near-by-devices
  *ngIf="showNearLocationModal"
  [custId]="selectedCustId"
  (closeNearLocationModal)="closeNearLocationModal()"
></app-customer-near-by-devices>

<app-cust-change-status
  *ngIf="showChangeStatusModal"
  [custId]="selectedCustId"
  [custStatus]="custStatus"
  [moduleType]="'cms'"
  (closeChangeStatusEvent)="closeChangeStatusEvent()"
></app-cust-change-status>

<app-customer-view-details
  *ngIf="dialog"
  (closeCustomerViewDetails)="closeSelectStaff()"
  [custId]="custId"
  [sourceType]="'customer'"
></app-customer-view-details>
