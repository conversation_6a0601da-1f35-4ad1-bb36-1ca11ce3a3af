<div class="row">
  <div class="col-md-12">
    <div class="panel top">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }} Management</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#searchDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="searchDataCountry" class="panel-collapse collapse in">
        <div id="" class="panel-body">
          <div class="row">
            <div class="col-lg-3 col-md-3">
              <input
                id="taxName"
                type="text"
                [(ngModel)]="searchCountryName"
                class="form-control"
                placeholder="Global Search Filter"
                (keydown.enter)="searchCountry()"
              />
            </div>
            <div class="col-lg-3 col-md-4 marginTopSearchBtn">
              <button
                type="button"
                class="btn btn-primary"
                id="searchbtn"
                (click)="searchCountry()"
              >
                <i class="fa fa-search"></i>
                Search
              </button>
              <button
                type="reset"
                class="btn btn-default"
                id="searchbtn"
                (click)="clearSearchCountry()"
              >
                <i class="fa fa-refresh"></i>
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-md-6 left">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#allDataCountry"
            aria-expanded="false"
            aria-controls="allDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="allDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Status</th>
                    <th *ngIf="editAccess || deleteAccess">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let country of countryListData
                        | paginate
                          : {
                              id: 'countryListData',
                              itemsPerPage: countryitemsPerPage,
                              currentPage: currentPageCountrySlab,
                              totalItems: countrytotalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ country.name }}</td>
                    <td *ngIf="country.status == 'ACTIVE' || country.status == 'Active'">
                      <span class="badge badge-success">Active</span>
                    </td>
                    <td *ngIf="country.status == 'INACTIVE' || country.status == 'Inactive'">
                      <span class="badge badge-danger">Inactive</span>
                    </td>
                    <td class="btnAction" *ngIf="editAccess || deleteAccess">
                      <a
                        id="edit-button"
                        href="javascript:void(0)"
                        type="button"
                        *ngIf="editAccess"
                        (click)="editCountry(country.id)"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </a>
                      <a
                        id="delete-button"
                        href="javascript:void(0)"
                        *ngIf="deleteAccess"
                        (click)="deleteConfirmonCountry(country.id)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>

              <div class="row">
                <div class="col-md-12" style="display: flex">
                  <pagination-controls
                    id="countryListData"
                    maxSize="10"
                    directionLinks="true"
                    previousLabel=""
                    nextLabel=""
                    (pageChange)="pageChangedCountryList($event)"
                  ></pagination-controls>
                  <div id="itemPerPageDropdown">
                    <p-dropdown
                      [options]="pageLimitOptions"
                      optionLabel="value"
                      optionValue="value"
                      (onChange)="TotalItemPerPage($event)"
                    ></p-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 right">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">{{ isCountryEdit ? "Update" : "Create" }} {{ title }}</h3>
        <div class="right">
          <button
            type="button"
            class="btn-toggle-collapse"
            data-toggle="collapse"
            data-target="#createDataCountry"
            aria-expanded="false"
            aria-controls="createDataCountry"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div id="createDataCountry" class="panel-collapse collapse in">
        <div class="panel-body table-responsive" *ngIf="!createAccess && !isCountryEdit">
          Sorry you have not privilege to create operation!
        </div>
        <div class="panel-body" *ngIf="createAccess || (isCountryEdit && editAccess)">
          <form [formGroup]="countryFormGroup">
            <label>{{ title }} Name*</label>
            <input
              id="name"
              type="text"
              class="form-control"
              placeholder="Enter {{ title }} Name"
              formControlName="name"
              [ngClass]="{ 'is-invalid': submitted && countryFormGroup.controls.name.errors }"
              maxLength="250"
            />
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && countryFormGroup.controls.name.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && countryFormGroup.controls.name.errors.required"
              >
                {{ title }} Name is required.
              </div>
              <div
                class="position"
                *ngIf="submitted && countryFormGroup.controls.name.errors?.cannotContainSpace"
              >
                <p class="error">White space are not allowed.</p>
              </div>
            </div>
            <br />
            <label>Status*</label>
            <div>
              <p-dropdown
                [options]="statusOptions"
                optionValue="label"
                optionLabel="label"
                filter="true"
                filterBy="label"
                placeholder="Select a Status"
                formControlName="status"
                [ngClass]="{
                  'is-invalid': submitted && countryFormGroup.controls.status.errors
                }"
              ></p-dropdown>
            </div>
            <div
              class="errorWrap text-danger"
              *ngIf="submitted && countryFormGroup.controls.status.errors"
            >
              <div
                class="error text-danger"
                *ngIf="submitted && countryFormGroup.controls.status.errors.required"
              >
                {{ title }} Status is required.
              </div>
            </div>
            <br />
            <div class="addUpdateBtn">
              <button
                *ngIf="!isCountryEdit"
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="addEditCountry('')"
              >
                <i class="fa fa-check-circle"></i>
                Add {{ title }}
              </button>
              <button
                *ngIf="isCountryEdit"
                type="submit"
                class="btn btn-primary"
                id="submit"
                (click)="addEditCountry(viewCountryListData.id)"
              >
                <i class="fa fa-check-circle"></i>
                Update {{ title }}
              </button>
              <br />
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
