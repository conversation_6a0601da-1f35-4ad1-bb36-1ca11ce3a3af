<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 *ngIf="!isEdit" class="panel-title">Create Password-Policy</h3>
        <h3 *ngIf="isEdit" class="panel-title">Update Password-Policy</h3>
        <div class="right">
          <button
            id="create"
            aria-controls="createPreCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#createPreCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="createPreCust">
        <div class="panel-body">
          <form [formGroup]="passwordPolicyForm">
            <fieldset style="margin-top: 1.5rem">
              <legend>Password-Policy Details</legend>
              <div class="boxWhite">
                <div class="row">
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Password-Policy Name*</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter Password-Policy Name"
                      formControlName="name"
                      [ngClass]="{
                        'is-invalid': submitted && passwordPolicyForm.controls.name.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.name.errors"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && passwordPolicyForm.controls.name.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && passwordPolicyForm.controls.name.errors.required"
                        >
                          Password-Policy Name is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Pattern*</label>
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Enter pattern Name"
                      formControlName="pattern"
                      [ngClass]="{
                        'is-invalid': submitted && passwordPolicyForm.controls.pattern.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.pattern.errors"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && passwordPolicyForm.controls.pattern.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="submitted && passwordPolicyForm.controls.pattern.errors.required"
                        >
                          Pattern is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Expiration Days*</label>
                    <input
                      type="number"
                      class="form-control"
                      placeholder="Enter Expiration Days"
                      formControlName="expiration_days"
                      [min]="1"
                      (keydown)="onlyNumberKey($event)"
                      [ngClass]="{
                        'is-invalid':
                          submitted && passwordPolicyForm.controls.expiration_days.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.expiration_days.errors"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && passwordPolicyForm.controls.expiration_days.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && passwordPolicyForm.controls.expiration_days.errors.required
                          "
                        >
                          Expiration Days is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Min Length*</label>
                    <input
                      type="number"
                      class="form-control"
                      placeholder="Enter Min Length"
                      formControlName="min_length"
                      (keydown)="onlyNumberKey($event)"
                      [min]="1"
                      [ngClass]="{
                        'is-invalid': submitted && passwordPolicyForm.controls.min_length.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.min_length.errors"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && passwordPolicyForm.controls.min_length.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && passwordPolicyForm.controls.min_length.errors.required
                          "
                        >
                          Min length is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Max Length*</label>
                    <input
                      type="number"
                      class="form-control"
                      placeholder="Enter Max Length"
                      formControlName="max_length"
                      [min]="1"
                      [ngClass]="{
                        'is-invalid': submitted && passwordPolicyForm.controls.max_length.errors
                      }"
                      (keydown)="onlyNumberKey($event)"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.max_length.errors"
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="submitted && passwordPolicyForm.controls.max_length.errors"
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted && passwordPolicyForm.controls.max_length.errors.required
                          "
                        >
                          Max length is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>No Password Recycling Prevention*</label>
                    <input
                      type="number"
                      class="form-control"
                      placeholder="Enter No Password Recycling Prevention"
                      formControlName="disable_recycling_prevention"
                      (keydown)="onlyNumberKey($event)"
                      [min]="1"
                      [ngClass]="{
                        'is-invalid':
                          submitted &&
                          passwordPolicyForm.controls.disable_recycling_prevention.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        submitted && passwordPolicyForm.controls.disable_recycling_prevention.errors
                      "
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted &&
                          passwordPolicyForm.controls.disable_recycling_prevention.errors
                        "
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            passwordPolicyForm.controls.disable_recycling_prevention.errors.required
                          "
                        >
                          No Password Recycling Prevention is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>No Account Lockout Attempt*</label>
                    <input
                      type="number"
                      class="form-control"
                      placeholder="Enter No Account Lockout Attempt"
                      formControlName="disable_account_lockout"
                      (keydown)="onlyNumberKey($event)"
                      [min]="1"
                      [ngClass]="{
                        'is-invalid':
                          submitted && passwordPolicyForm.controls.disable_account_lockout.errors
                      }"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        submitted && passwordPolicyForm.controls.disable_account_lockout.errors
                      "
                    >
                      <div
                        class="errorWrap text-danger"
                        *ngIf="
                          submitted && passwordPolicyForm.controls.disable_account_lockout.errors
                        "
                      >
                        <div
                          class="error text-danger"
                          *ngIf="
                            submitted &&
                            passwordPolicyForm.controls.disable_account_lockout.errors.required
                          "
                        >
                          No Account Lockout Attempt is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div
                    class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15"
                    style="margin: 16px 0px; margin-top: 4rem; display: flex"
                  >
                    <label for="notification"> Is Notification Required*</label>
                    <div style="margin-left: 10px">
                      <p-inputSwitch
                        id="notification"
                        formControlName="isNotificationRequired"
                      ></p-inputSwitch>
                    </div>
                  </div> -->
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Status*</label>
                    <p-dropdown
                      [options]="statusOptions"
                      optionValue="label"
                      optionLabel="label"
                      filter="true"
                      filterBy="label"
                      placeholder="Select a Status"
                      formControlName="status"
                      [ngClass]="{
                        'is-invalid': submitted && passwordPolicyForm.controls.status.errors
                      }"
                    ></p-dropdown>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.status.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="submitted && passwordPolicyForm.controls.status.errors.required"
                      >
                        Status is required.
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15"></div>
                  <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 mb-15">
                    <label>Pattern Description*</label>
                    <textarea
                      class="form-control"
                      rows="4"
                      formControlName="pattern_description"
                      placeholder="Enter Pattern Description"
                      [ngClass]="{
                        'is-invalid':
                          submitted && passwordPolicyForm.controls.pattern_description.errors
                      }"
                    ></textarea>
                    <div
                      class="errorWrap text-danger"
                      *ngIf="submitted && passwordPolicyForm.controls.pattern_description.errors"
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          submitted &&
                          passwordPolicyForm.controls.pattern_description.errors.required
                        "
                      >
                        Pattern Description is required.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
          </form>

          <div class="addUpdateBtn" style="margin-top: 3.5rem">
            <button
              type="submit"
              class="btn btn-primary"
              *ngIf="!isEdit"
              id="submit"
              (click)="addEditPassword('')"
            >
              <i class="fa fa-check-circle"></i>
              Add Password-Policy
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              *ngIf="isEdit"
              id="submit"
              (click)="addEditPassword(viewpasswordData.id)"
            >
              <i class="fa fa-check-circle"></i>
              Update Password-Policy
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
