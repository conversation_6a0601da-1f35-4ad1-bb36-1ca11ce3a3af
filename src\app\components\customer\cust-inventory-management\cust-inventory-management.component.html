<!-- <div class="card">
  <h5>Subheader Grouping</h5>
  <p-table [value]="customers" sortField="representative.name" sortMode="single">
    <ng-template pTemplate="header">
      <tr>
        <th>Name</th>
        <th>Country</th>
        <th>Company</th>
        <th>Status</th>
        <th>Date</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-customer let-rowIndex="rowIndex">
      <tr *ngIf="rowGroupMetadata[customer?.representative?.name]?.index === rowIndex">
        <td colspan="4">
          <span class="p-text-bold p-ml-2">{{ customer.representative.name }}</span>
        </td>
        <td>
          <span class="p-text-bold p-ml-2">{{ customer.id }}</span>
        </td>
      </tr>
      <tr>
        <td>
          {{ customer.name }}
        </td>
        <td>
          <span class="image-text">{{ customer.country.name }}</span>
        </td>
        <td>
          {{ customer.company }}
        </td>
        <td>
          <span [class]="'customer-badge status-' + customer.status">{{ customer.status }}</span>
        </td>
        <td>
          {{ customer.date }}
        </td>
      </tr>
    </ng-template>
  </p-table>
</div> -->
<div class="panel">
  <div class="panel-heading">
    <div class="displayflex">
      <button
        (click)="customerDetailOpen()"
        class="btn btn-secondary backbtn"
        data-placement="bottom"
        data-toggle="tooltip"
        title="Go to Customer Details"
        type="button"
      >
        <i class="fa fa-arrow-circle-left" style="color: #f7b206 !important; font-size: 28px"></i>
      </button>
      <h3 class="panel-title">
        {{ custData.title }}
        {{ custData.firstname }}
        {{ custData.lastname }} Inventory List
      </h3>
    </div>
    <div class="right">
      <button class="btn refreshbtn" (click)="getCustomerAssignedList()">
        <i class="fa fa-refresh"></i>
      </button>
      <button
        aria-controls="inventoryListPreCust1"
        aria-expanded="false"
        class="btn-toggle-collapse"
        data-target="#inventoryListPreCust1"
        data-toggle="collapse"
        type="button"
      >
        <i class="fa fa-minus-circle"></i>
      </button>
    </div>
  </div>
  <!-- </div> -->
  <div class="panel-collapse collapse in" id="inventoryListPreCust1">
    <div class="panel-body table-responsive">
      <button
        *ngIf="planInventoryAccess"
        [disabled]="custData.status == 'Terminate'"
        (click)="assignPlanInventoryModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Assign Plan Inventory"
        type="submit"
      >
        Plan Inventory
      </button>

      <!-- <button [disabled]="custData.status == 'Terminate'" (click)="assignPlanInventoryModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Assign Plan Inventory"
        type="submit"
      >
        Plan Inventory
      </button> -->

      <button
        *ngIf="otherInventoryAccess"
        [disabled]="custData.status == 'Terminate'"
        (click)="assignOtherInventoryModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Assign Inventory"
        type="submit"
      >
        Other Inventory
      </button>
      <button
        *ngIf="externalInventoryAccess"
        [disabled]="custData.status == 'Terminate'"
        (click)="assignExternalInventoryModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Assign External Inventory"
        type="submit"
      >
        External Inventory
      </button>
      <button
        *ngIf="InventoryHistoryAccess"
        [disabled]="custData.status == 'Terminate'"
        (click)="getAllInventoryHistoryModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Assign Inventory"
        type="submit"
      >
        Inventory History
      </button>

      <!-- <button [disabled]="custData.status == 'Terminate'" (click)="swapInventoryPlanModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Swap Inventory Plan"
        type="submit"
      >
        Swap Inventory Plan
      </button> -->

      <button
        *ngIf="swapInventoryAccess"
        [disabled]="custData.status == 'Terminate'"
        (click)="swapInventoryPlanModalOpen()"
        class="btn btn-primary statusbtn"
        [disabled]="custData.status == 'Terminate'"
        (click)="swapInventoryPlanModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Swap Inventory Plan"
        type="submit"
      >
        Swap Inventory Plan
      </button>
      <button
        *ngIf="DTVHistoryAccess"
        [disabled]="custData.status == 'Terminate'"
        (click)="inventoryLogModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="DTV History"
        type="submit"
      >
        DTV History
      </button>
      <!-- <button
        [disabled]="custData.status == 'Terminate'"
        (click)="integrationinventoryLogModalOpen()"
        class="btn btn-primary statusbtn"
        style="
          background-color: #f7b206 !important;
          font-size: 16px;
          padding: 3px 12px;
          margin-top: 10px;
        "
        title="Inventory Integration"
        type="submit"
      >
        Integration
      </button> -->

      <div class="row" style="margin-top: 15px">
        <div class="col-lg-12 col-md-12">
          <p-table [value]="assignedInventoryList" styleClass="custTable">
            <ng-template pTemplate="header">
              <tr>
                <!-- <th style="width:20%">Connection Number(Service Name)</th> -->
                <th style="text-align: center">Product Name</th>
                <th style="text-align: center">Current Plan</th>

                <!-- <th width="20%">Mac</th>
          <th width="20%">Serial Number</th> -->
                <th style="text-align: center">Item Type</th>
                <!-- <th width="20%">Warranty</th> -->
                <th style="text-align: center">Assigned Qty.</th>
                <th style="text-align: center">Status</th>
                <!-- <th>Sync Status</th> -->
                <th style="text-align: center">Next Approver</th>
                <!-- <th width="25%">Assigned Date</th>
          <th width="20%">Expiry Date</th> -->
                <th style="text-align: center">Action</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-inventory let-rowIndex="rowIndex">
              <tr
                *ngIf="rowGroupMetadata[inventory?.custInventoryListId]?.index === rowIndex"
                style="background-color: #dddddd; color: rgb(0, 0, 0)"
              >
                <td colspan="4" *ngIf="isShowConnection">
                  <span class="p-ml-2"
                    >{{ inventory.connectionNo }} ({{ inventory.serviceName }})</span
                  >
                </td>
                <td colspan="4" *ngIf="!isShowConnection">
                  <span class="p-ml-2"
                    >{{ inventory?.inOutWardMACMapping[0]?.serialNumber }}
                    <!-- {{ getSerialNumber(inventory) }}  -->
                    ({{ inventory.connectionNo }}
                    -
                    {{ inventory.serviceName }})</span
                  >
                </td>
                <td style="text-align: center">
                  <div *ngIf="inventory.ezyBillStockId != null && inventory.hasCas == true">
                    <span class="badge badge-success">Synced</span>
                  </div>
                  <div *ngIf="inventory.ezyBillStockId == null && inventory.hasCas == true">
                    <span class="badge badge-danger">Unsynced</span>
                  </div>
                </td>
                <td style="text-align: center">
                  <div *ngIf="inventory.pairStatus == 'Paired'">
                    <span class="badge badge-success">{{ inventory.pairStatus }}</span>
                  </div>
                  <div *ngIf="inventory.pairStatus == 'Unpaired'">
                    <span class="badge badge-danger">{{ inventory.pairStatus }}</span>
                  </div>
                </td>
                <td class="btnAction" colspan="2" style="text-align: right">
                  <!-- <button
              (click)="approveInventory(inventory.itemAssemblyId)"
              [disabled]="inventory.nextApproverId != staffUserId || inventory.status != 'PENDING'"
              class="curson_pointer approve-btn"
              title="Approve"
              type="button"
            >
              <img src="assets/img/assign.jpg" />
            </button>
            <button
              (click)="rejectInventory(inventory.itemAssemblyId)"
              [disabled]="inventory.nextApproverId != staffUserId || inventory.status != 'PENDING'"
              class="curson_pointer approve-btn"
              title="Reject"
              type="button"
            >
              <img src="assets/img/reject.jpg" />
            </button> -->
                  <!-- Assign Inventory Workflow -->
                  <button
                    (click)="
                      approveAssignInventoryOpen(inventory.custInventoryListId, '', inventory.id)
                    "
                    [disabled]="
                      inventory.nextApproverId != staffUserId || inventory.status != 'PENDING'
                    "
                    class="curson_pointer approve-btn"
                    title="Approve"
                    type="button"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/assign.jpg" />
                  </button>
                  <button
                    (click)="
                      rejectAssignInventoryOpen(inventory.custInventoryListId, '', inventory.id)
                    "
                    [disabled]="
                      inventory.nextApproverId != staffUserId || inventory.status != 'PENDING'
                    "
                    class="curson_pointer approve-btn"
                    title="Reject"
                    type="button"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/reject.jpg" />
                  </button>
                  <button
                    *ngIf="uploadDocumentAccess"
                    (click)="uploadDocument(inventory)"
                    data-placement="top"
                    data-toggle="tooltip"
                    id="editbutton"
                    title="Upload Documents"
                    type="button"
                    href="javascript:void(0)"
                    class="curson_pointer approve-btn"
                    style="background-color: #dddddd"
                    [disabled]="inventory.status == 'REJECTED'"
                  >
                    <img height="32" src="assets/img/up.jpg" width="32" />
                  </button>
                  <button
                    *ngIf="downloadDocumentsAccess"
                    (click)="downloadDocument(inventory)"
                    data-placement="top"
                    data-toggle="tooltip"
                    id="editbutton"
                    title="Download Documents"
                    type="button"
                    href="javascript:void(0)"
                    class="curson_pointer approve-btn"
                    style="background-color: #dddddd"
                  >
                    <!-- [disabled]="!inventory?.filename" -->
                    <img height="32" src="assets/img/pdf.png" width="32" />
                  </button>
                  <button
                    *ngIf="replaceAccess"
                    (click)="replaceInventoryModalOpen(inventory)"
                    [disabled]="
                      inventory?.status == 'PENDING' ||
                      inventory?.status == 'REJECTED' ||
                      inventory?.inOutWardMACMapping.length > 1 ||
                      (inventory.hasMac == false &&
                        inventory.hasSerial == false &&
                        inventory.hasTrackable == false) ||
                      inventory?.externalItemId != null ||
                      isDisableReplace(inventory)
                    "
                    class="curson_pointer approve-btn"
                    id="delete-button"
                    title="Replace"
                    style="background-color: #dddddd"
                  >
                    <img class="icon" src="assets/img/E_Status_Y.png" width="32" height="32" />
                  </button>
                  <button
                    *ngIf="editAccess"
                    (click)="editCustomerInventory(inventory)"
                    [disabled]="
                      inventory.status == 'PENDING' ||
                      inventory.status == 'REJECTED' ||
                      (inventory.hasMac == false &&
                        inventory.hasSerial == false &&
                        inventory.hasTrackable == false) ||
                      inventory?.externalItemId != null
                    "
                    class="curson_pointer approve-btn"
                    title="Edit"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/ioc01.jpg" />
                  </button>
                  <button
                    (click)="reactivateBoxResponse(inventory.custInventoryListId)"
                    *ngIf="inventory.hasCas == true"
                    [disabled]="
                      inventory.hasCas == false ||
                      (inventory.hasCas == true && inventory.status != 'ACTIVE') ||
                      isDisableReactiveBox(inventory)
                    "
                    class="curson_pointer approve-btn"
                    title="ReactiveBox"
                    type="button"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/All_Icons/17_Inventory_Management/02_Reactive-Box.png" />
                  </button>

                  <button
                    (click)="pairBox(inventory.custInventoryListId)"
                    *ngIf="inventory.itemAssemblyId != null"
                    [disabled]="
                      inventory.hasCas == false ||
                      (inventory.hasCas == true && inventory.status != 'ACTIVE') ||
                      (inventory.hasCas == true && inventory.pairStatus == 'Paired')
                    "
                    class="curson_pointer approve-btn"
                    title="PairBox"
                    type="button"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/All_Icons/17_Inventory_Management/05_Pair-Box.png" />
                  </button>

                  <button
                    (click)="unpairBox(inventory.custInventoryListId)"
                    *ngIf="inventory.itemAssemblyId != null"
                    [disabled]="
                      inventory.hasCas == false ||
                      (inventory.hasCas == true && inventory.status != 'ACTIVE') ||
                      (inventory.hasCas == true && inventory.pairStatus != 'Paired')
                    "
                    class="curson_pointer approve-btn"
                    title="UnpairBox"
                    type="button"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/All_Icons/17_Inventory_Management/06_Unpair-Box.png" />
                  </button>

                  <button
                    [disabled]="nms_enable == false && fiber_home_enable == false"
                    (click)="wifiModalOpen(inventory)"
                    class="curson_pointer approve-btn"
                    title="Wifi Config"
                    type="button"
                    style="background-color: #dddddd"
                  >
                    <img src="assets/img/All_Icons/17_Inventory_Management/06_Unpair-Box.png" />
                  </button>
                </td>
              </tr>
              <tr>
                <!-- <td></td>
          <td>
            <a
              href="javascript:void(0)"
              style="color: #f7b206"
              (click)="openInventoryDetailModal('customerInventoryDetailModal', inventory)"
            >
              {{ inventory.serviceName }}
            </a>
          </td> -->

                <td style="text-align: center">
                  <a
                    href="javascript:void(0)"
                    style="color: #f7b206"
                    (click)="openInventoryDetailModal('customerInventoryDetailModal', inventory)"
                  >
                    {{ inventory.productName }}
                  </a>
                </td>
                <td style="text-align: center">{{ inventory.currentPlan }}</td>
                <!-- <td>
            <span *ngFor="let mac of inventory.inOutWardMACMapping"> {{ mac.macAddress }}</span>
            {{ inventory.macAddress }}
          </td>
          <td>
            <span *ngFor="let serialNumber of inventory.inOutWardMACMapping">
              {{ serialNumber.serialNumber }}</span
            >
            {{ inventory.serialNumber }}
          </td> -->
                <td style="text-align: center">{{ inventory.itemType }}</td>
                <!-- <td>{{ inventory.warranty }}</td> -->
                <td style="text-align: center">
                  {{ inventory.qty }}
                  <!-- {{ assignedInventory.productId.unit }} -->
                </td>
                <td style="text-align: center">
                  <div *ngIf="inventory.status.toLowerCase() === 'active'">
                    <span class="badge badge-success">Active</span>
                  </div>
                  <div *ngIf="inventory.status.toLowerCase() == 'rejected'">
                    <span class="badge badge-danger">Rejected</span>
                  </div>
                  <div *ngIf="inventory.status.toLowerCase() == 'pending'">
                    <span class="badge badge-info"> Pending For Approve </span>
                  </div>
                  <div *ngIf="inventory.status.toLowerCase() == 'pending for remove'">
                    <span class="badge badge-info"> Pending For Remove </span>
                  </div>
                  <div *ngIf="inventory.status.toLowerCase() == 'inactive'">
                    <span class="badge badge-danger"> InActive </span>
                  </div>
                  <div *ngIf="inventory.status.toLowerCase() == 'activationpending'">
                    <span class="badge badge-info"> ActivationPending </span>
                  </div>
                </td>
                <!-- <td style="text-align: center">
            <div *ngIf="inventory.ezyBillStockId != null">
              <span class="badge badge-success">Synced</span>
            </div>
            <div *ngIf="inventory.ezyBillStockId == null">
              <span class="badge badge-danger">Unsynced</span>
            </div>
          </td> -->
                <td style="text-align: center">
                  {{ inventory.assigneeName }}
                </td>
                <!-- <td>
            {{ inventory.assignedDateTime | date : "dd-MM-yyyy hh:mm:ss" }}
          </td>
          <td>
            {{ inventory.expiryDateTime | date : "dd-MM-yyyy hh:mm:ss" }}
          </td> -->
                <td style="text-align: right" class="btnAction" colspan="2">
                  <button
                    (click)="replaceInventorySTBCARDModalOpen(inventory)"
                    *ngIf="inventory.itemAssemblyId != null && replaceAccess"
                    [disabled]="
                      inventory?.status == 'PENDING' ||
                      inventory?.status == 'REJECTED' ||
                      inventory.itemAssemblyId == null ||
                      isDisableReplace(inventory) ||
                      inventory?.status == 'PENDING' ||
                      inventory?.status == 'REJECTED' ||
                      inventory.itemAssemblyId == null ||
                      isDisableReplace(inventory)
                    "
                    class="curson_pointer approve-btn"
                    id="delete-button"
                    title="Replace"
                  >
                    <img class="icon" src="assets/img/E_Status_Y.png" width="32" height="32" />
                  </button>
                  <button
                    (click)="editSTBCARDCustomerInventory(inventory)"
                    [disabled]="inventory.status == 'PENDING' || inventory.status == 'REJECTED'"
                    *ngIf="inventory.itemAssemblyId != null && editAccess"
                    class="curson_pointer approve-btn"
                    title="Edit"
                  >
                    <img src="assets/img/ioc01.jpg" />
                  </button>
                  <button
                    (click)="generateRemoveInventoryRequest(inventory)"
                    class="curson_pointer approve-btn"
                    [disabled]="
                      inventory?.status == 'PENDING' ||
                      inventory?.status == 'REJECTED' ||
                      inventory?.removeRequestStatus == 'PENDING' ||
                      inventory?.removeRequestStatus == 'REJECTED' ||
                      inventory?.inOutWardMACMapping.length > 1 ||
                      (inventory.hasMac == false &&
                        inventory.hasSerial == false &&
                        inventory.hasTrackable == false)
                    "
                    title="Remove Inventory"
                    id="delete-button"
                  >
                    <img src="assets/img/ioc02.jpg" />
                  </button>
                  <!-- || isDisableRemove(inventory)  This is condition for check service status for remove inventory  -->

                  <button
                    (click)="approveRemoveInventoryOpen(inventory, '')"
                    class="curson_pointer approve-btn"
                    [disabled]="
                      inventory?.generateRemoveRequest == false ||
                      (inventory.nextApproverId != staffUserId &&
                        inventory?.status == 'PENDING FOR REMOVE') ||
                      inventory.removeRequestStatus == 'REJECTED'
                    "
                    title="Approve Remove Inventory"
                    type="button"
                  >
                    <img src="assets/img/assign.jpg" />
                  </button>
                  <button
                    (click)="rejectRemoveInventoryOpen(inventory, '')"
                    [disabled]="
                      inventory?.generateRemoveRequest == false ||
                      (inventory.nextApproverId != staffUserId &&
                        inventory?.status == 'PENDING FOR REMOVE') ||
                      inventory.removeRequestStatus == 'REJECTED'
                    "
                    class="curson_pointer approve-btn"
                    title="Reject Remove Inventory"
                    type="button"
                  >
                    <img src="assets/img/reject.jpg" />
                  </button>
                  <a
                    (click)="inventoryWorkFlowList(inventory.id)"
                    href="javascript:void(0)"
                    id="delete-button"
                    title="Approval Progress"
                  >
                    <img height="32" src="assets/img/05_inventory-to-customer_Y.png" width="32" />
                  </a>
                  <button
                    *ngIf="editAccess"
                    (click)="editProductParams(inventory)"
                    [disabled]="inventory.status == 'ACTIVE' || inventory.status == 'REJECTED'"
                    class="curson_pointer approve-btn"
                    title="Edit"
                  >
                    <img src="assets/img/ioc01.jpg" height="32" width="32" />
                  </button>
                  <button
                    class="approve-btn"
                    style="border: none; background: transparent; padding: 0; margin-right: 3px"
                    [disabled]="inventory.status == 'ACTIVE'"
                    title="Re-activate"
                    *ngIf="inventory.status == 'ActivationPending'"
                    (click)="reActivate(inventory)"
                  >
                    <img width="32" height="32" src="assets/img/refresh.png" />
                  </button>
                </td>
              </tr>
            </ng-template>
          </p-table>

          <!-- <table class="table">
      <thead>
        <tr>
          <th width="20%">ServiceName</th>
          <th width="20%">CurrentPlan</th>
          <th width="20%">ProductName</th>
          <th width="20%">Mac</th>
          <th width="20%">SerialNumber</th>
          <th width="20%">ItemType</th>
          <th width="20%">Warranty</th>
          <th width="5%">Assigned Qty.</th>
          <th style="text-align: center" width="30%">Status</th>
          <th width="25%">Next Approver</th>
          <th width="25%">Assigned Date</th>
          <th width="20%">Expiry Date</th>
          <th width="30%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="
            let assignedInventory of assignedInventoryList
              | paginate
                : {
                    id: 'assignedInventoryListData',
                    itemsPerPage: customerInventoryListItemsPerPage,
                    currentPage: customerInventoryListDataCurrentPage,
                    totalItems: customerInventoryListDataTotalRecords
                  };
            index as i
          "
        >
          <td>
            <a
              href="javascript:void(0)"
              style="color: #f7b206"
              (click)="openInventoryDetailModal('customerInventoryDetailModal', assignedInventory)"
            >
              {{ assignedInventory.serviceName }}
            </a>
          </td>
          <td>{{ assignedInventory.currentPlan }}</td>
          <td>{{ assignedInventory.productName }}</td>
          <td>
            <span *ngFor="let mac of assignedInventory.inOutWardMACMapping">
              {{ mac.macAddress }}</span
            >
            {{ assignedInventory.macAddress }}
          </td>
          <td>
            <span *ngFor="let serialNumber of assignedInventory.inOutWardMACMapping">
              {{ serialNumber.serialNumber }}</span
            >
            {{ assignedInventory.serialNumber }}
          </td>
          <td>{{ assignedInventory.itemType }}</td>
          <td>{{ assignedInventory.warranty }}</td>
          <td>
            {{ assignedInventory.qty }}
          
          </td>
          <td style="text-align: center">
            <div *ngIf="assignedInventory.status == 'ACTIVE'">
              <span class="badge badge-success">Active</span>
            </div>
            <div *ngIf="assignedInventory.status == 'REJECTED'">
              <span class="badge badge-danger">Rejected</span>
            </div>
            <div *ngIf="assignedInventory.status == 'PENDING'">
              <span class="badge badge-info"> Pending For Approve </span>
            </div>
          </td>
          <td>
            {{ assignedInventory.assigneeName }}
          </td>
          <td>
            {{ assignedInventory.assignedDateTime | date : "dd-MM-yyyy hh:mm:ss" }}
          </td>
          <td>
            {{ assignedInventory.expiryDateTime | date : "dd-MM-yyyy hh:mm:ss" }}
          </td>
          <td class="btnAction">
            <button
              (click)="replaceInventoryModalOpen(assignedInventory.inOutWardMACMapping[0].id)"
              [disabled]="
                assignedInventory?.status == 'PENDING' || assignedInventory?.status == 'REJECTED'
              "
              class="curson_pointer approve-btn"
              id="delete-button"
              title="Replace"
            >
              <img class="icon" src="assets/img/E_Status_Y.png" width="32" height="32" />
            </button>
            <button
              (click)="editCustomerInventory(assignedInventory.id)"
              [disabled]="
                assignedInventory?.status == 'PENDING' || assignedInventory?.status == 'REJECTED'
              "
              class="curson_pointer approve-btn"
              title="Edit"
            >
              <img src="assets/img/ioc01.jpg" />
            </button>
            <button
              (click)="approveInventory(assignedInventory.id)"
              [disabled]="assignedInventory.nextApproverId != staffUserId"
              class="curson_pointer approve-btn"
              title="Approve"
              type="button"
            >
              <img src="assets/img/assign.jpg" />
            </button>
            <button
              (click)="rejectInventory(assignedInventory.id)"
              [disabled]="assignedInventory.nextApproverId != staffUserId"
              class="curson_pointer approve-btn"
              title="Reject"
              type="button"
            >
              <img src="assets/img/reject.jpg" />
            </button>
            <button
              (click)="
                removeInvantryFunction(
                  assignedInventory?.inOutWardMACMapping[0]?.id,
                  assignedInventory.inOutWardMACMapping[0].custInventoryMappingId,
                  assignedInventory.itemId
                )
              "
              class="curson_pointer approve-btn"
              [disabled]="
                assignedInventory?.status == 'PENDING' ||
                assignedInventory?.inOutWardMACMapping[0]?.status == 'PENDING'
              "
              title="Remove Inventory"
              id="delete-button"
            >
              <img src="assets/img/ioc02.jpg" />
            </button>
            <a
              (click)="inventoryWorkFlowList(assignedInventory.id)"
              href="javascript:void(0)"
              id="delete-button"
              title="Approval Progress"
            >
              <img height="32" src="assets/img/05_inventory-to-customer_Y.png" width="32" />
            </a>
          </td>
        </tr>
      </tbody>
    </table>
    <div style="display: flex">
      <pagination-controls
        (pageChange)="pageChangedEventCustomerAssignInventory($event)"
        [directionLinks]="true"
        [maxSize]="10"
        id="assignedInventoryListData"
        nextLabel=""
        previousLabel=""
      >
      </pagination-controls>
      <div #itemPerPageDropDownInventory id="itemPerPageDropdown">
        <p-dropdown
          (onChange)="totalItemsEventCustomerAssignInventory($event)"
          [options]="pageLimitOptions"
          optionLabel="value"
          optionValue="value"
        ></p-dropdown>
      </div>
    </div> -->
        </div>
      </div>
    </div>
  </div>
</div>

<app-customer-inventory-details
  dialogId="customerInventoryDetailModal"
  [inventoryData]="inventoryData"
></app-customer-inventory-details>

<!-- replace Inventory -->
<p-dialog
  header="Replace Inventory"
  [(visible)]="replaceInventoryModal"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <form [formGroup]="replaceInventoryForm">
      <div class="row">
        <div class="col-lg-12 col-md-12">
          <div class="form-group">
            <label>Replacement*</label>
            <p-dropdown
              [options]="replacementType"
              filter="true"
              filterBy="label"
              formControlName="inventoryType"
              optionLabel="label"
              optionValue="value"
              placeholder="Select status"
              (onChange)="selReplacementType($event)"
            ></p-dropdown>
            <div
              *ngIf="replaceSumitted && replaceInventoryForm.controls.inventoryType.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  replaceSumitted && replaceInventoryForm.controls.inventoryType.errors.required
                "
                class="error text-danger"
              >
                Replacement is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12">
          <div class="form-group">
            <label>Replacement Reason*</label>
            <p-dropdown
              [options]="replacementReasonType"
              filter="true"
              filterBy="label"
              formControlName="replacementReason"
              optionLabel="label"
              optionValue="value"
              placeholder="Select status"
            ></p-dropdown>
            <div
              *ngIf="replaceSumitted && replaceInventoryForm.controls.inventoryType.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  replaceSumitted && replaceInventoryForm.controls.inventoryType.errors.required
                "
                class="error text-danger"
              >
                Replacement is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12">
          <div class="form-group">
            <label>Remark*</label>
            <input
              type="text"
              class="form-control"
              formControlName="remark"
              placeholder="Enter Remark"
            />
            <div
              *ngIf="replaceSumitted && replaceInventoryForm.controls.remark.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="replaceSumitted && replaceInventoryForm.controls.remark.errors.required"
                class="error text-danger"
              >
                Remark is required.
              </div>
            </div>
          </div>
        </div>
        <!-- <div *ngIf="itemConditionSingleReplaceFlag">
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="form-group">
                  <label>Item Condition*</label>
                  <p-dropdown
                    (onChange)="selectItemConditionReplace($event)"
                    [options]="itemConditionData"
                    filter="true"
                    filterBy="label"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Select Item Type"
                  >
                  </p-dropdown>
                </div>
              </div>
            </div> -->
        <div class="col-lg-12 col-md-12" *ngIf="otherInventoryReplaceFlag">
          <div class="form-group">
            <label>Product*</label>
            <p-dropdown
              (onChange)="getReplaceLevelMacAddressList($event)"
              [options]="replaceProducts"
              filter="true"
              filterBy="name"
              formControlName="productId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select Product"
            ></p-dropdown>
            <div
              *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors.required"
                class="error text-danger"
              >
                Product is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12" *ngIf="planInventoryReplaceFlag">
          <div class="form-group">
            <label>Product*</label>
            <p-dropdown
              (onChange)="getReplacePlanLevelMacAddressList($event)"
              [options]="productByPlanListReplace"
              filter="true"
              filterBy="name"
              formControlName="productId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select Product"
            ></p-dropdown>
            <div
              *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors.required"
                class="error text-danger"
              >
                Product is required.
              </div>
            </div>
          </div>
        </div>
        <!-- this is my code  -->
        <div class="col-lg-12 col-md-12" *ngIf="macReplaceListFlag && macAddressList.length > 0">
          <div class="form-group">
            <p-table
              #dt
              [value]="macAddressList"
              [(selection)]="selectedReplaceMACAddress"
              dataKey="id"
              styleClass="p-datatable-customers"
              [rowHover]="true"
              [filterDelay]="0"
              responsiveLayout="scroll"
              [scrollable]="true"
              scrollHeight="300px"
              [globalFilterFields]="['condition', 'id', 'serialNumber', 'macAddress']"
              responsiveLayout="scroll"
              [globalFilterFields]="['serialNumber', 'macAddress', 'id']"
              responsiveLayout="scroll"
              [scrollable]="true"
              scrollHeight="250px"
            >
              <ng-template pTemplate="caption">
                <div class="flex align-items-center justify-content-between">
                  <span class="p-input-icon-left">
                    <input
                      class="form-control"
                      pInputText
                      type="text"
                      [(ngModel)]="filterGlobalReplaceSingle"
                      [ngModelOptions]="{ standalone: true }"
                      (input)="dt.filterGlobal($event.target.value, 'contains')"
                      placeholder="Global Search Filter"
                    />
                  </span>
                  &nbsp;
                  <button
                    type="button"
                    class="btn btn-default"
                    (click)="clearFilterGlobalReplaceSingle(dt)"
                  >
                    Clear
                  </button>
                </div>
              </ng-template>
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 10%"></th>
                  <th>Item Id</th>
                  <th>Item Type</th>
                  <th *ngIf="this.hasMac">MAC Address</th>
                  <th *ngIf="this.hasSerial">Serial Number</th>
                  <th>Action</th>
                </tr>
              </ng-template>
              <ng-template let-product pTemplate="body">
                <tr>
                  <td style="width: 10%">
                    <p-tableRadioButton
                      *ngIf="product.customerId == null"
                      [value]="product"
                      (click)="editReplacementLevelMacMapping(product.id, product, $event)"
                    ></p-tableRadioButton>
                  </td>
                  <td>
                    <input name="itemId" class="form-control" [value]="product.itemId" disabled />
                  </td>
                  <td>
                    {{ product.condition }}
                  </td>
                  <td *ngIf="this.hasMac">
                    <input
                      type="text"
                      name="macAddress"
                      class="form-control"
                      [value]="product.macAddress"
                      [(ngModel)]="product.macAddress"
                      [ngModelOptions]="{ standalone: true }"
                      [disabled]="enterReplacementLevelMacSerial == product.id ? false : true"
                    />
                  </td>
                  <td *ngIf="this.hasSerial">
                    <input
                      type="text"
                      name="serialNumber"
                      class="form-control"
                      [value]="product.serialNumber"
                      [(ngModel)]="product.serialNumber"
                      [ngModelOptions]="{ standalone: true }"
                      [disabled]="enterReplacementLevelMacSerial == product.id ? false : true"
                    />
                  </td>
                  <td>
                    <button
                      (click)="editReplacementLevelMac(product.id)"
                      [disabled]="
                        editReplacementLevelMacSerialBtn == product.id && isEditEnable
                          ? false
                          : true
                      "
                      class="curson_pointer approve-btn"
                      title="Edit Mac"
                    >
                      <img src="assets/img/ioc01.jpg" />
                    </button>
                    <button
                      id="addAtt"
                      class="btn btn-primary"
                      (click)="saveMacidMapping(product.itemId, product)"
                      style="object-fit: cover; padding: 5px 8px"
                      icon="pi pi-check"
                      [disabled]="
                        enterReplacementLevelMacSerial == product.id &&
                        editReplacementLevelMacSerialBtn == product.id
                          ? false
                          : true
                      "
                    >
                      Add
                    </button>
                    <button
                      id="addAtt"
                      class="btn btn-primary"
                      (click)="viewSpecificationParameters(product.itemId, product)"
                      style="object-fit: cover; padding: 5px 8px; margin-left: 5px"
                      icon="pi pi-check"
                      [disabled]="
                        editReplacementLevelMacSerialBtn == product.id ||
                        editReplacementLevelMacSerialBtn == product.id
                          ? false
                          : true
                      "
                    >
                      View
                    </button>
                  </td>
                </tr>
              </ng-template>
              <ng-template pTemplate="summary">
                <p-paginator
                  (onPageChange)="paginate($event)"
                  [first]="newFirst"
                  [rows]="macAddressListdataitemsPerPage"
                  [totalRecords]="macAddressListtotalRecords"
                  [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                ></p-paginator>
              </ng-template>
            </p-table>
          </div>
        </div>
        <div class="col-lg-12 col-md-12">
          <div class="form-group">
            <label>Replace Date*</label>
            <div>
              <p-calendar
                [style]="{ width: '40%' }"
                [numberOfMonths]="1"
                [showSeconds]="true"
                [showTime]="true"
                formControlName="assignedDateTime"
                inputId="time"
              ></p-calendar>
              <div
                *ngIf="replaceSumitted && replaceInventoryForm.controls.assignedDateTime.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    replaceSumitted &&
                    replaceInventoryForm.controls.assignedDateTime.errors.required
                  "
                  class="error text-danger"
                >
                  Replace Date is required.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="replaceInventorySubmit()"
        class="btn btn-primary btn-sm"
        id="submit"
        type="submit"
        [disabled]="!replaceInventoryForm.valid"
      >
        <i class="fa fa-check-circle"></i>
        Submit
      </button>

      <button (click)="replaceInventoryModalClose()" class="btn btn-danger btn-sm">Close</button>
    </div>
  </div>
</p-dialog>

<!-- replace Assembly Inventory -->
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="replaceAssemblyInventoryModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog nearSearchModalLocation">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Replace Assembly Inventory</h3>
      </div>
      <div class="modal-body">
        <form [formGroup]="replaceInventoryForm">
          <div class="row">
            <div class="col-lg-12 col-md-12">
              <div class="form-group">
                <label>Replacement*</label>
                <p-dropdown
                  [options]="replacementType"
                  filter="true"
                  filterBy="label"
                  formControlName="inventoryType"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select status"
                ></p-dropdown>
                <div
                  *ngIf="replaceSumitted && replaceInventoryForm.controls.inventoryType.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      replaceSumitted && replaceInventoryForm.controls.inventoryType.errors.required
                    "
                    class="error text-danger"
                  >
                    Replacement is required.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-12 col-md-12">
              <div class="form-group">
                <label>Replacement Reason*</label>
                <p-dropdown
                  [options]="replacementReasonType"
                  filter="true"
                  filterBy="label"
                  formControlName="replacementReason"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select status"
                ></p-dropdown>
                <div
                  *ngIf="replaceSumitted && replaceInventoryForm.controls.inventoryType.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      replaceSumitted && replaceInventoryForm.controls.inventoryType.errors.required
                    "
                    class="error text-danger"
                  >
                    Replacement is required.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-12 col-md-12">
              <div class="form-group">
                <label>Remark*</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="remark"
                  placeholder="Enter Remark"
                />
                <div
                  *ngIf="replaceSumitted && replaceInventoryForm.controls.remark.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="replaceSumitted && replaceInventoryForm.controls.remark.errors.required"
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
              <div class="form-group">
                <label>STB Product*</label>
                <p-dropdown
                  (onChange)="getMacAddressList1Replace($event)"
                  [options]="stbProductsToReplace"
                  filter="true"
                  filterBy="name"
                  formControlName="productId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select Product"
                ></p-dropdown>
                <div
                  *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      replaceSumitted && replaceInventoryForm.controls.productId.errors.required
                    "
                    class="error text-danger"
                  >
                    Product is required.
                  </div>
                </div>
              </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
              <div class="form-group" style="padding: 0px 15px">
                <label>Card Product*</label>
                <p-dropdown
                  (onChange)="getMacAddressList2Replace($event)"
                  [options]="cardProductsToReplace"
                  filter="true"
                  filterBy="name"
                  formControlName="productId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select Product"
                ></p-dropdown>
                <div
                  *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      replaceSumitted && replaceInventoryForm.controls.productId.errors.required
                    "
                    class="error text-danger"
                  >
                    Product is required.
                  </div>
                </div>
              </div>
            </div>
            <div *ngIf="getAllPairItemMacReplaceFlag">
              <p-splitter [style]="{ height: '100%' }" gutterSize="">
                <ng-template pTemplate>
                  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="row">
                      <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <div class="form-group">
                        <label>STB Product*</label>
                        <p-dropdown (onChange)="getMacAddressList1Replace($event)" [options]="stbProductsToReplace"
                          filter="true" filterBy="name" formControlName="productId" optionLabel="name" optionValue="id"
                          placeholder="Select Product"></p-dropdown>
                        <div *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors"
                          class="errorWrap text-danger">
                          <div *ngIf="
                              replaceSumitted &&
                              replaceInventoryForm.controls.productId.errors.required
                            " class="error text-danger">
                            Product is required.
                          </div>
                        </div>
                      </div>
                    </div> -->
                      <div
                        class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                        *ngIf="macAddressList1.length > 0 && getAllPairItemMacReplaceFlag"
                      >
                        <div class="form-group">
                          <p-table
                            #dt
                            [(selection)]="selectedReplaceMACAddress"
                            [globalFilterFields]="['serialNumber', 'condition']"
                            [rowHover]="true"
                            [value]="macAddressList1"
                            dataKey="id"
                            responsiveLayout="scroll"
                            styleClass="p-datatable-customers"
                            [filterDelay]="0"
                            [scrollable]="true"
                            scrollHeight="250px"
                          >
                            <ng-template pTemplate="caption">
                              <div class="flex align-items-center justify-content-between">
                                <span class="p-input-icon-left">
                                  <input
                                    class="form-control"
                                    pInputText
                                    type="text"
                                    [(ngModel)]="stbFileterGlobalReplace"
                                    [ngModelOptions]="{ standalone: true }"
                                    (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Global Search Filter"
                                  />
                                </span>
                                &nbsp;
                                <button
                                  type="button"
                                  class="btn btn-default"
                                  (click)="clearstbFileterGlobalReplace(dt)"
                                >
                                  Clear
                                </button>
                              </div>
                            </ng-template>
                            <ng-template pTemplate="header">
                              <tr>
                                <th style="width: 10%"></th>
                                <!-- <th>Item Id</th>
                            <th *ngIf="this.hasMac">MAC Address</th> -->
                                <th>Item Type</th>
                                <th>Serial Number</th>
                                <!-- <th>Action</th> -->
                              </tr>
                            </ng-template>
                            <ng-template let-product pTemplate="body">
                              <tr>
                                <td style="width: 10%">
                                  <p-tableRadioButton
                                    [value]="product"
                                    *ngIf="product.customerId == null"
                                  ></p-tableRadioButton>
                                </td>
                                <!-- <td style="width: 25%">
                              <input
                                name="itemId"
                                [(ngModel)]="selectedStbOption"
                                class="form-control"
                                [value]="product.itemId"
                                disabled
                              />
                            </td> -->
                                <!-- <td style="width: 25%" *ngIf="this.hasMac">
                              <input
                                type="text"
                                name="macAddress"
                                class="form-control"
                                [value]="product.macAddress"
                                [(ngModel)]="product.macAddress"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="
                                  enterReplacementLevelMacSerial == product.id ? false : true
                                "
                              />
                            </td>
                            <td style="width: 25%">
                              <input
                                type="text"
                                name="serialNumber"
                                class="form-control"
                                [value]="product.serialNumber"
                                [(ngModel)]="product.serialNumber"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="
                                  enterReplacementLevelMacSerial == product.id ? false : true
                                "
                              />
                            </td> -->
                                <td>
                                  {{ product.condition }}
                                </td>
                                <td>
                                  {{ product.serialNumber }}
                                </td>
                                <!-- <td>
                              <button
                                (click)="editReplacementLevelMac(product.id)"
                                [disabled]="
                                  editReplacementLevelMacSerialBtn == product.id ? false : true
                                "
                                class="curson_pointer approve-btn"
                                title="Edit Mac"
                              >
                                <img src="assets/img/ioc01.jpg" />
                              </button>
                              <button
                                id="addAtt"
                                class="btn btn-primary"
                                (click)="saveMacidMapping(product.itemId, product)"
                                style="object-fit: cover; padding: 5px 8px"
                                icon="pi pi-check"
                                [disabled]="
                                  enterReplacementLevelMacSerial == product.id ? false : true
                                "
                                [disabled]="
                                  editReplacementLevelMacSerialBtn == product.id ? false : true
                                "
                              >
                                Add
                              </button>
                            </td> -->
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="summary">
                              <p-paginator
                                (onPageChange)="paginateMacAddrRep1data($event)"
                                [first]="newFirstMacAddrRep1"
                                [rows]="macAddrRep1ListdataitemsPerPage"
                                [totalRecords]="macAddrRep1ListtotalRecords"
                                [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                                [(ngModel)]="macAddrRep1ListdataitemsPerPage"
                              ></p-paginator>
                            </ng-template>
                          </p-table>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
                <ng-template pTemplate>
                  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="row">
                      <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      <div class="form-group" style="padding: 0px 15px">
                        <label>Card Product*</label>
                        <p-dropdown (onChange)="getMacAddressList2Replace($event)" [options]="cardProductsToReplace"
                          filter="true" filterBy="name" formControlName="productId" optionLabel="name" optionValue="id"
                          placeholder="Select Product"></p-dropdown>
                        <div *ngIf="replaceSumitted && replaceInventoryForm.controls.productId.errors"
                          class="errorWrap text-danger">
                          <div *ngIf="
                              replaceSumitted &&
                              replaceInventoryForm.controls.productId.errors.required
                            " class="error text-danger">
                            Product is required.
                          </div>
                        </div>
                      </div>
                    </div> -->
                      <div
                        class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                        *ngIf="macAddressList2.length > 0 && getAllPairItemMacReplaceFlag"
                      >
                        <div class="form-group">
                          <p-table
                            #dt
                            [value]="macAddressList2"
                            [(selection)]="selectedReplaceMACAddress2"
                            dataKey="id"
                            styleClass="p-datatable-customers"
                            [rowHover]="true"
                            [filterDelay]="0"
                            [globalFilterFields]="['serialNumber', 'condition']"
                            responsiveLayout="scroll"
                            [scrollable]="true"
                            scrollHeight="250px"
                          >
                            <ng-template pTemplate="caption">
                              <div class="flex align-items-center justify-content-between">
                                <span class="p-input-icon-left">
                                  <input
                                    class="form-control"
                                    pInputText
                                    type="text"
                                    [(ngModel)]="cardFileterGlobalReplace"
                                    [ngModelOptions]="{ standalone: true }"
                                    (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Global Search Filter"
                                  />
                                </span>
                                &nbsp;
                                <button
                                  type="button"
                                  class="btn btn-default"
                                  (click)="clearcardFileterGlobalReplace(dt)"
                                >
                                  Clear
                                </button>
                              </div>
                            </ng-template>
                            <ng-template pTemplate="header">
                              <tr>
                                <th style="width: 10%"></th>
                                <!-- <th>Item Id</th>
                            <th *ngIf="this.hasMac">MAC Address</th> -->
                                <th>Item Type</th>
                                <th>Serial Number</th>
                                <!-- <th>Action</th> -->
                              </tr>
                            </ng-template>
                            <ng-template let-product pTemplate="body">
                              <tr>
                                <td style="width: 10%">
                                  <p-tableRadioButton
                                    [value]="product"
                                    *ngIf="product.customerId == null"
                                  ></p-tableRadioButton>
                                </td>
                                <!-- <td style="width: 25%">
                              <input
                                name="itemId"
                                [(ngModel)]="selectedCardOption"
                                class="form-control"
                                [value]="product.itemId"
                              />
                            </td>
                            <td style="width: 25%" *ngIf="this.hasMac">
                              <input
                                type="text"
                                name="macAddress"
                                class="form-control"
                                [value]="product.macAddress"
                                [(ngModel)]="product.macAddress"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="
                                  enterReplacementLevelMacSerial == product.id ? false : true
                                "
                              />
                            </td>
                            <td style="width: 25%">
                              <input
                                type="text"
                                name="serialNumber"
                                class="form-control"
                                [value]="product.serialNumber"
                                [(ngModel)]="product.serialNumber"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="
                                  enterReplacementLevelMacSerial == product.id ? false : true
                                "
                              />
                            </td> -->
                                <td>
                                  {{ product.condition }}
                                </td>
                                <td>
                                  {{ product.serialNumber }}
                                </td>
                                <!-- <td>
                              <button
                                (click)="editReplacementLevelMac(product.id)"
                                [disabled]="
                                  editReplacementLevelMacSerialBtn == product.id ? false : true
                                "
                                class="curson_pointer approve-btn"
                                title="Edit Mac"
                              >
                                <img src="assets/img/ioc01.jpg" />
                              </button>
                              <button
                                id="addAtt"
                                class="btn btn-primary"
                                (click)="saveMacidMapping(product.itemId, product)"
                                style="object-fit: cover; padding: 5px 8px"
                                icon="pi pi-check"
                                [disabled]="
                                  enterReplacementLevelMacSerial == product.id ? false : true
                                "
                                [disabled]="
                                  editReplacementLevelMacSerialBtn == product.id ? false : true
                                "
                              >
                                Add
                              </button>
                            </td> -->
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="summary">
                              <p-paginator
                                (onPageChange)="paginateMacAddrRep2Data($event)"
                                [first]="newFirstMacAddrRep2"
                                [rows]="macAddrRep2ListdataitemsPerPage"
                                [totalRecords]="macAddrRep2ListtotalRecords"
                                [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                              ></p-paginator>
                            </ng-template>
                          </p-table>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
              </p-splitter>
            </div>
            <div class="col-lg-12 col-md-12">
              <div class="form-group">
                <label>Replace Date*</label>
                <p-calendar
                  [numberOfMonths]="1"
                  [showSeconds]="true"
                  [showTime]="true"
                  formControlName="assignedDateTime"
                  inputId="time"
                ></p-calendar>
                <div
                  *ngIf="replaceSumitted && replaceInventoryForm.controls.assignedDateTime.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      replaceSumitted &&
                      replaceInventoryForm.controls.assignedDateTime.errors.required
                    "
                    class="error text-danger"
                  >
                    Replace Date is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <div class="addUpdateBtn">
          <button
            (click)="replaceAssemblyInventorySubmit()"
            class="btn btn-primary btn-sm"
            id="submit"
            type="submit"
            [disabled]="!replaceInventoryForm.valid"
          >
            <i class="fa fa-check-circle"></i>
            Submit
          </button>

          <button (click)="replaceAssemblyInventoryModalClose()" class="btn btn-danger btn-sm">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Workflow Inventory -->

<div class="modal fade" id="workflowInventoryModal" role="dialog">
  <div class="modal-dialog nearSearchModalLocation">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h3 class="panel-title">Inventory Progress</h3>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <div class="progressbarWrap">
              <div
                *ngFor="let data of inventoryApproveProgressDetail; last as isLast"
                [ngClass]="{
                  complete: data.status == 'Approved',
                  progressdv: data.status == 'inprogress'
                }"
                class="circleWrap"
              >
                <div class="circledv completedWorkFlowClass">
                  <i *ngIf="data.status == 'Approved'" class="fa fa-check"></i>
                </div>
                <p>{{ data.teamName }}</p>
                <div *ngIf="!isLast" class="lines">
                  <div class="line"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12">
            <div style="margin: 20px">
              <h3>Workflow Audit</h3>
              <div class="table-responsive">
                <div class="row">
                  <div class="col-lg-12 col-md-12">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Action</th>
                          <th>Staff name</th>
                          <th>Remark</th>
                          <th>Action Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let data of inventoryWorkflowAuditData
                              | paginate
                                : {
                                    id: 'inventoryWorkflowAuditData',
                                    itemsPerPage: inventoryApproveProgressPerPage,
                                    currentPage: currentPageInventoryApproveProgress,
                                    totalItems: inventoryApproveProgresstotalRecords
                                  };
                            index as i
                          "
                        >
                          <td>
                            <div>
                              {{ data.action }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.actionByName }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.remark }}
                            </div>
                          </td>
                          <td>
                            <div>
                              {{ data.actionDateTime | date: "yyyy-MM-dd hh:mm a" }}
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <br />
                    <div class="pagination_Dropdown">
                      <pagination-controls
                        (pageChange)="pageChangedInventoryProgress($event)"
                        directionLinks="true"
                        id="inventoryWorkflowAuditData"
                        maxSize="10"
                        nextLabel=""
                        previousLabel=""
                      >
                      </pagination-controls>
                      <div id="itemPerPageDropdown">
                        <!-- <p-dropdown
                          [options]="pageLimitOptions"
                          optionLabel="value"
                          optionValue="value"
                          (onChange)="TotalItemPerPageWorkFlow($event)"
                        ></p-dropdown> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn">
          <button class="btn btn-danger btn-sm" (click)="closeInventoryWorkflowModel()">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Edit Inventory -->

<div *ngIf="editInventory" class="row" style="margin-top: 20px"></div>
<p-table [value]="invenoryDetails" *ngIf="editInventory" [tableStyle]="{ 'min-width': '50rem' }">
  <ng-template pTemplate="caption">
    <div class="flex align-items-center justify-content-between">
      <b>Inventory Details</b>
      <button
        (click)="approveReplaceInventoryInventory(true, false)"
        class="btn btn-success gridbtn"
        [disabled]="invenoryDetails[0].currentApproveId != staffUserId"
        style="border: none; background: transparent; padding: 0; margin-left: 80%"
        title="Approve"
        type="button"
      >
        <img height="25" src="assets/img/assign.jpg" width="25" />
      </button>
      <button
        (click)="approveReplaceInventoryInventory(false, false)"
        [disabled]="invenoryDetails[0].currentApproveId != staffUserId"
        class="btn btn-danger gridbtn"
        style="border: none; background: transparent; padding: 0; margin-left: 3px"
        title="Reject"
        type="button"
      >
        <img height="25" src="assets/img/reject.jpg" width="25" />
      </button>
      <button
        [disabled]="invenoryDetails[0].newSerialNumber === ''"
        (click)="inventoryWorkFlowList(invenoryDetails[0].newId)"
        href="javascript:void(0)"
        id="delete-button"
        style="border: none; background: transparent; padding: 0; margin-left: 4px"
        title="Approval Progress"
      >
        <img height="25" src="assets/img/05_inventory-to-customer_Y.png" width="25" />
      </button>
    </div>
  </ng-template>
  <ng-template pTemplate="header">
    <tr>
      <th>Old serialNumber</th>
      <th>Old macAddress</th>
      <th>New serialNumber</th>
      <th>New macAddress</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-invenoryDetail>
    <tr>
      <td>{{ invenoryDetail.oldSerialNumber }}</td>
      <td>{{ invenoryDetail.oldMacAddress }}</td>
      <td>{{ invenoryDetail.newSerialNumber }}</td>
      <td>{{ invenoryDetail.newMacAddress }}</td>
    </tr>
  </ng-template>
</p-table>

<!-- Edit Inventory -->

<div *ngIf="editSTBCradInventory" class="row" style="margin-top: 20px"></div>
<p-table
  [value]="invenoryDetails"
  *ngIf="editSTBCradInventory"
  [tableStyle]="{ 'min-width': '50rem' }"
>
  <ng-template pTemplate="caption">
    <div class="flex align-items-center justify-content-between">
      <b>Inventory Details</b>
      <button
        (click)="approveReplaceInventoryInventory(true, true)"
        class="btn btn-success gridbtn"
        [disabled]="invenoryDetails[0].currentApproveId != staffUserId"
        style="border: none; background: transparent; padding: 0; margin-left: 80%"
        title="Approve"
        type="button"
      >
        <img height="25" src="assets/img/assign.jpg" width="25" />
      </button>
      <button
        (click)="approveReplaceInventoryInventory(false, true)"
        [disabled]="invenoryDetails[0].currentApproveId != staffUserId"
        class="btn btn-danger gridbtn"
        style="border: none; background: transparent; padding: 0; margin-left: 3px"
        title="Reject"
        type="button"
      >
        <img height="25" src="assets/img/reject.jpg" width="25" />
      </button>
      <button
        [disabled]="invenoryDetails[0].newSerialNumber === ''"
        (click)="inventoryWorkFlowList(invenoryDetails[0].newId)"
        href="javascript:void(0)"
        id="delete-button"
        style="border: none; background: transparent; padding: 0; margin-left: 4px"
        title="Approval Progress"
      >
        <img height="25" src="assets/img/05_inventory-to-customer_Y.png" width="25" />
      </button>
    </div>
  </ng-template>
  <ng-template pTemplate="header">
    <tr>
      <th>Old serialNumber</th>
      <th>Old macAddress</th>
      <th>New serialNumber</th>
      <th>New macAddress</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-invenoryDetail>
    <tr>
      <td>{{ invenoryDetail.oldSerialNumber }}</td>
      <td>{{ invenoryDetail.oldMacAddress }}</td>
      <td>{{ invenoryDetail.newSerialNumber }}</td>
      <td>{{ invenoryDetail.newMacAddress }}</td>
    </tr>
  </ng-template>
</p-table>

<!-- Edit Inventory -->
<!-- <div *ngIf="editInventoryold" class="row" style="margin-top: 20px">
  <div class="col-md-12 col-sm-12">
    <h3 class="panel-title">
      {{ custData.title }}
      {{ custData.custname }} Inventory Details List
    </h3>

    <table class="table" >
      <thead>
        <tr>
          <th style="text-align: left">Current Mac Address</th>
          <th style="text-align: left">Current Serial</th>
          <th style="text-align: left">New MAC Address</th>
          <th style="text-align: left">New Serial</th>
          <th style="text-align: left">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{ inOutMacMapping.oldMac }}</td>
          <td>{{ inOutMacMapping.oldSerial }}</td>
          <td>{{ inOutMacMapping.newMac }}</td>
          <td>{{ inOutMacMapping.newSerial }}</td>

          <td class="btnAction" rowspan="2" style="vertical-align: middle;">
            <button
              (click)="
                approveReplaceInventoryInventory(
                  inOutMacMapping.newId,
                  inOutMacMapping.oldId,
                  inOutMacMapping.newStatus
                )
              "
              class="btn btn-success gridbtn"
              [disabled]="inOutMacMapping.oldMac != '' && inOutMacMapping.newMac == ''"
              style="border: none; background: transparent; padding: 0; margin-right: 5px"
              title="Approve"
              type="button"
            >
              <img src="assets/img/assign.jpg" />
            </button>
            <button
              (click)="
                rejectInventoryReplaceInventory(
                  inOutMacMapping.newId,
                  inOutMacMapping.oldId,
                  inOutMacMapping.newStatus
                )
              "
              [disabled]="inOutMacMapping.oldMac != '' && inOutMacMapping.newMac == ''"
              class="btn btn-danger gridbtn"
              style="border: none; background: transparent; padding: 0; margin-right: 3px"
              title="Reject"
              type="button"
            >
              <img src="assets/img/reject.jpg" />
            </button>
            <a
              (click)="checkStatusForRepalce()"
              href="javascript:void(0)"
              id="delete-button"
              style="border: none; background: transparent; padding: 0; margin-right: 5px"
              title="Approval Progress"
            >
              <img height="32" src="assets/img/05_inventory-to-customer_Y.png" width="32" />
            </a>
          </td>
        </tr>
        <tr>
          <td>{{ inOutMacMapping.oldMac }}</td>
          <td >{{ inOutMacMapping.oldSerial }}</td>
          <td>{{ inOutMacMapping.newMac }}</td>
          <td style="padding: 8px;">{{ inOutMacMapping.newSerial }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div> -->

<div class="modal fade" id="EditinventoryStatusView" role="dialog">
  <div class="modal-dialog nearSearchModalLocation">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="panel-title">Replace Inventory Progress</h3>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12">
            <div class="progressbarWrap">
              <div
                *ngFor="let data of inventoryApproveProgressDetail; last as isLast"
                [ngClass]="{
                  complete: data.status == 'Approved',
                  progressdv: data.status == 'inprogress'
                }"
                class="circleWrap"
              >
                <div class="circledv completedWorkFlowClass">
                  <i *ngIf="data.status == 'Approved'" class="fa fa-check"></i>
                </div>
                <p>{{ data.teamName }}</p>
                <div *ngIf="!isLast" class="lines">
                  <div class="line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer" style="display: flex; justify-content: flex-end">
        <div class="addUpdateBtn">
          <button #closebutton class="btn btn-danger btn-sm" data-dismiss="modal">Close</button>
          <br />
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Assign Other Inventory"
  [(visible)]="displayDialogAssignOtherInventory"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <form [formGroup]="inventoryAssignForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Service*</label>
            <p-dropdown
              #ddlService
              (onChange)="getServiceAtOtherInventory($event, ddlService)"
              [options]="uniqueServices"
              filter="true"
              filterBy="planName"
              formControlName="serviceId"
              optionLabel="service"
              optionValue="serviceId"
              placeholder="Select Service"
            ></p-dropdown>
            <div
              *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.serviceId.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted && inventoryAssignForm.controls.serviceId.errors.required
                "
                class="error text-danger"
              >
                Service is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getAllConnectionNumberFlag">
          <div class="form-group">
            <label>Connection No*</label>
            <p-dropdown
              (onChange)="getConnectionNoDetails($event)"
              [options]="connectionNoList"
              filter="true"
              filterBy="planName"
              formControlName="connectionNo"
              optionLabel="connection_no"
              optionValue="connection_no"
              placeholder="Select Connection No"
            ></p-dropdown>
            <div
              *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.connectionNo.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.connectionNo.errors.required
                "
                class="error text-danger"
              >
                Connection No is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getAllConnectionNumberFlag">
          <div class="form-group">
            <label>Item Type*</label>
            <p-dropdown
              (onChange)="getSelItemType($event)"
              *ngIf="!isassemblyStaffMac"
              [options]="ItemSelectionType"
              filter="true"
              filterBy="label"
              formControlName="itemTypeFlag"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Item Type"
            >
            </p-dropdown>
            <div
              *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.itemTypeFlag.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.itemTypeFlag.errors.required
                "
                class="error text-danger"
              >
                Item Type is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getAllAssemblyTypeFlag">
          <div class="form-group">
            <label>Assembly Type*</label>
            <p-dropdown
              (onChange)="getSelAssemblyType($event)"
              *ngIf="!isassemblyStaffMac"
              [options]="productSelectionType"
              filter="true"
              filterBy="label"
              formControlName="itemAssemblyflag"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Assembly Type"
            ></p-dropdown>
            <div
              *ngIf="
                inventoryAssignSumitted && inventoryAssignForm.controls.itemAssemblyflag.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.itemAssemblyflag.errors.required
                "
                class="error text-danger"
              >
                Assembly Type is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getAllAssemblyNameFlag">
          <div class="form-group">
            <label>Assembly Name*</label>
            <input
              type="text"
              class="form-control"
              formControlName="itemAssemblyName"
              placeholder="Enter Assembly Name"
            />
            <div
              *ngIf="
                inventoryAssignSumitted && inventoryAssignForm.controls.itemAssemblyName.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.itemAssemblyName.errors.required
                "
                class="error text-danger"
              >
                Assembly Name is required.
              </div>
            </div>
          </div>
        </div>
        <!-- Item Condition Serialized Single Item -->
        <div *ngIf="itemConditionSingleFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Item Condition*</label>
              <p-dropdown
                (onChange)="selectItemCondition($event)"
                [options]="itemConditionData"
                filter="true"
                filterBy="label"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Item Type"
                formControlName="itemType"
              >
              </p-dropdown>
              <div
                *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.itemType.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSumitted && inventoryAssignForm.controls.itemType.errors.required
                  "
                  class="error text-danger"
                >
                  Item Condition is required.
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Item Condition Serialized Pair Item -->
        <div *ngIf="itemConditionPairFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Item Condition*</label>
              <p-dropdown
                (onChange)="selectItemConditionPair($event)"
                [options]="itemConditionData"
                filter="true"
                filterBy="label"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Item Type"
                formControlName="itemType"
              >
              </p-dropdown>
              <div
                *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.itemType.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSumitted && inventoryAssignForm.controls.itemType.errors.required
                  "
                  class="error text-danger"
                >
                  Item Condition is required.
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="getAllSerializedProductFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Product*</label>
              <p-dropdown
                (onChange)="getMacAddressList($event)"
                [options]="allActiveProducts"
                filter="true"
                filterBy="name"
                formControlName="productId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Product"
              ></p-dropdown>
              <div
                *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.productId.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSumitted &&
                    inventoryAssignForm.controls.productId.errors.required
                  "
                  class="error text-danger"
                >
                  Product is required.
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
          *ngIf="macAddressList.length > 0 && getAllSingleItemMacFlag"
        >
          <div class="modal-body">
            <p-table
              #dt
              [value]="macAddressList"
              [(selection)]="selectedMACAddress"
              dataKey="id"
              styleClass="p-datatable-customers"
              [rowHover]="true"
              [filterDelay]="0"
              [globalFilterFields]="['condition', 'itemId', 'serialNumber', 'macAddress']"
              responsiveLayout="scroll"
              [scrollable]="true"
              scrollHeight="300px"
            >
              <ng-template pTemplate="caption">
                <form [formGroup]="searchForm">
                  <div class="row">
                    <div class="col-lg-3 col-md-3">
                      <p-dropdown
                        [options]="macOptionSelect"
                        optionValue="value"
                        optionLabel="label"
                        filter="true"
                        filterBy="label"
                        formControlName="searchOption"
                        (onChange)="selMacSearchOption($event.value)"
                        placeholder="Select a search option"
                      >
                      </p-dropdown>
                    </div>
                    <div
                      class="col-lg-3 col-md-3"
                      *ngIf="this.searchForm.value.searchOption === 'itemId'"
                    >
                      <input
                        id="namesearch"
                        type="text"
                        class="form-control"
                        formControlName="searchMacDeatil"
                        (keydown.enter)="searchMac()"
                        placeholder="Enter Item Id"
                      />
                    </div>
                    <div
                      class="col-lg-3 col-md-3"
                      *ngIf="this.searchForm.value.searchOption === 'mac'"
                    >
                      <input
                        id="mobilesearch"
                        (keydown.enter)="searchMac()"
                        type="text"
                        class="form-control"
                        formControlName="searchMacDeatil"
                        placeholder="Enter Mac"
                      />
                    </div>
                    <div
                      class="col-lg-3 col-md-3"
                      *ngIf="this.searchForm.value.searchOption === 'serialNumber'"
                    >
                      <input
                        id="leadSourcesearch"
                        (keydown.enter)="searchMac()"
                        type="text"
                        class="form-control"
                        formControlName="searchMacDeatil"
                        placeholder="Enter Serial Number"
                      />
                    </div>
                    <div
                      class="col-lg-3 col-md-3"
                      *ngIf="this.searchForm.value.searchOption === 'assetId '"
                    >
                      <input
                        id="serviceArea"
                        (keydown.enter)="searchMac()"
                        type="text"
                        class="form-control"
                        formControlName="searchMacDeatil"
                        placeholder="Enter Asset Id"
                      />
                    </div>
                    <div
                      class="col-lg-3 col-md-3"
                      *ngIf="this.searchForm.value.searchOption === 'itemType'"
                    >
                      <input
                        id="Lead Assigne Name"
                        (keydown.enter)="searchMac()"
                        type="text"
                        class="form-control"
                        formControlName="searchMacDeatil"
                        placeholder="Enter Item Type"
                      />
                    </div>
                    <div class="row">
                      <button
                        type="button"
                        class="btn btn-primary"
                        id="searchbtn"
                        (click)="searchMac()"
                        [disabled]="!this.searchForm.value.searchMacDeatil"
                      >
                        <i class="fa fa-search"></i>
                        Search
                      </button>
                      &nbsp;
                      <button
                        type="button"
                        class="btn btn-default"
                        id="searchbtn"
                        (click)="clearMac()"
                      >
                        <i class="fa fa-refresh"></i>
                        Clear
                      </button>
                    </div>
                  </div>
                </form>
              </ng-template>
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 10%"></th>
                  <th>Item Id</th>
                  <th>Item Type</th>
                  <th *ngIf="this.hasMac">MAC Address</th>
                  <th *ngIf="this.hasSerial">Serial Number</th>
                  <th>Action</th>
                </tr>
              </ng-template>
              <ng-template let-product pTemplate="body" let-rowIndex="rowIndex">
                <tr>
                  <td style="width: 10%">
                    <p-tableRadioButton
                      *ngIf="getAllSerializedProductFlag && product.customerId == null"
                      [value]="product"
                      (click)="editMacMapping(product.id, product, $event)"
                    ></p-tableRadioButton>
                  </td>
                  <td>
                    <input name="itemId" class="form-control" [value]="product.itemId" disabled />
                  </td>
                  <td>
                    {{ product.condition }}
                  </td>
                  <td *ngIf="this.hasMac">
                    <input
                      type="text"
                      name="macAddress"
                      class="form-control"
                      [value]="product.macAddress"
                      [(ngModel)]="product.macAddress"
                      [ngModelOptions]="{ standalone: true }"
                      [disabled]="enterMacSerial == product.id ? false : true"
                    />
                  </td>
                  <td *ngIf="this.hasSerial">
                    <input
                      type="text"
                      name="serialNumber"
                      class="form-control"
                      [value]="product.serialNumber"
                      [(ngModel)]="product.serialNumber"
                      [ngModelOptions]="{ standalone: true }"
                      [disabled]="enterMacSerial == product.id ? false : true"
                    />
                  </td>
                  <td>
                    <button
                      (click)="editMac(product.id)"
                      [disabled]="editMacSerialBtn == product.id && isEditEnable ? false : true"
                      class="curson_pointer approve-btn"
                      title="Edit Mac"
                    >
                      <img src="assets/img/ioc01.jpg" />
                    </button>
                    <button
                      id="addAtt"
                      class="btn btn-primary"
                      (click)="saveMacidMapping(product.itemId, product)"
                      style="object-fit: cover; padding: 5px 8px"
                      icon="pi pi-check"
                      [disabled]="
                        enterMacSerial == product.id && editMacSerialBtn == product.id
                          ? false
                          : true
                      "
                    >
                      Add
                    </button>
                    <button
                      id="addAtt"
                      class="btn btn-primary"
                      (click)="viewSpecificationParameters(product.itemId, product)"
                      style="object-fit: cover; padding: 5px 8px; margin-left: 5px"
                      icon="pi pi-check"
                      [disabled]="
                        enterMacSerial == product.id || editMacSerialBtn == product.id
                          ? false
                          : true
                      "
                    >
                      View
                    </button>
                  </td>
                </tr>
              </ng-template>
              <ng-template pTemplate="summary">
                <p-paginator
                  (onPageChange)="paginateMacAddressData($event)"
                  [first]="newFirstMacAddress"
                  [rows]="macAddressListdataitemsPerPage"
                  [totalRecords]="macAddressListtotalRecords"
                  [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                ></p-paginator>
              </ng-template>
            </p-table>
          </div>
        </div>
        <div *ngIf="getSplitterFlag">
          <p-splitter [style]="{ height: '100%' }" gutterSize="">
            <ng-template pTemplate>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="row" *ngIf="getAllPairProductFlag">
                  <div
                    class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                    *ngIf="getAllPairProductFlag"
                  >
                    <div class="form-group">
                      <label>STB Product*</label>
                      <p-dropdown
                        [options]="allSTBProducts"
                        (onChange)="getMacAddressList1($event)"
                        filter="true"
                        filterBy="name"
                        formControlName="productId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select Product"
                      ></p-dropdown>
                      <div
                        *ngIf="
                          inventoryAssignSumitted && inventoryAssignForm.controls.productId.errors
                        "
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            inventoryAssignSumitted &&
                            inventoryAssignForm.controls.productId.errors.required
                          "
                          class="error text-danger"
                        >
                          Product is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                    *ngIf="macAddressList1.length > 0 && getAllPairItemMacFlag"
                  >
                    <div class="form-group">
                      <p-table
                        #dt
                        [value]="macAddressList1"
                        [(selection)]="selectedMACAddress"
                        dataKey="id"
                        styleClass="p-datatable-customers"
                        [loading]="loading"
                        [rowHover]="true"
                        [filterDelay]="0"
                        [globalFilterFields]="['serialNumber', 'condition']"
                        responsiveLayout="scroll"
                        [scrollable]="true"
                        scrollHeight="250px"
                      >
                        <ng-template pTemplate="caption">
                          <div class="flex align-items-center justify-content-between">
                            <span class="p-input-icon-left">
                              <input
                                class="form-control"
                                pInputText
                                type="text"
                                [(ngModel)]="stbFileterGlobal"
                                [ngModelOptions]="{ standalone: true }"
                                (input)="dt.filterGlobal($event.target.value, 'contains')"
                                placeholder="Enter Seial Number"
                              />
                            </span>
                            &nbsp;
                            <button
                              type="button"
                              class="btn btn-default"
                              (click)="clearstbFileterGlobal(dt)"
                            >
                              Clear
                            </button>
                          </div>
                        </ng-template>
                        <br />
                        <ng-template pTemplate="header">
                          <tr>
                            <th style="width: 10%"></th>
                            <th>Item Type</th>
                            <th>Serial Number</th>
                            <th>Action</th>
                          </tr>
                        </ng-template>
                        <ng-template let-product pTemplate="body">
                          <tr>
                            <td style="width: 10%">
                              <p-tableRadioButton
                                [value]="product"
                                *ngIf="getAllPairProductFlag && product.customerId == null"
                                (click)="editSTBSerialMapping(product.id)"
                              ></p-tableRadioButton>
                            </td>
                            <td>
                              {{ product.condition }}
                            </td>
                            <td>
                              <input
                                type="text"
                                name="serialNumber"
                                class="form-control"
                                [value]="product.serialNumber"
                                [(ngModel)]="product.serialNumber"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="enterSTBSerial == product.id ? false : true"
                              />
                              <!-- {{ product.serialNumber }} -->
                            </td>
                            <td>
                              <button
                                (click)="editSTBSerial(product.id)"
                                [disabled]="editSTBSerialBtn == product.id ? false : true"
                                class="curson_pointer approve-btn"
                                title="Edit Mac"
                              >
                                <img src="assets/img/ioc01.jpg" />
                              </button>
                              <button
                                id="addAtt"
                                class="btn btn-primary"
                                (click)="saveMacidMapping(product.itemId, product)"
                                style="object-fit: cover; padding: 5px 8px"
                                icon="pi pi-check"
                                [disabled]="enterSTBSerial == product.id ? false : true"
                                [disabled]="editSTBSerialBtn == product.id ? false : true"
                              >
                                Add
                              </button>
                            </td>
                          </tr>
                        </ng-template>
                        <ng-template pTemplate="summary">
                          <p-paginator
                            (onPageChange)="paginateMacAddress1Data($event)"
                            [first]="newFirstMacAddress1"
                            [rows]="macAddress1ListdataitemsPerPage"
                            [totalRecords]="macAddress1ListtotalRecords"
                            [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                            [(ngModel)]="macAddress1ListdataitemsPerPage"
                          ></p-paginator>
                        </ng-template>
                      </p-table>
                    </div>
                  </div>
                  <!-- Discount To Other Inventory Pair Item-->
                  <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="oldOfferBasedDiscountSTBPairFlag">
                        <div class="form-group">
                          <label>Discount</label>
                          <input
                            [(ngModel)]="selectedCustDiscount"
                            class="form-control"
                            formControlName="discount"
                            type="text"
                            readonly
                          />
                        </div>
                      </div> -->
                </div>
              </div>
            </ng-template>
            <ng-template pTemplate>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="row" *ngIf="getAllPairProductFlag">
                  <div
                    class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                    *ngIf="getAllPairProductFlag"
                  >
                    <div class="form-group">
                      <label>Card Product*</label>
                      <p-dropdown
                        [options]="allCardProducts"
                        (onChange)="getMacAddressList2($event)"
                        filter="true"
                        filterBy="name"
                        formControlName="productId"
                        optionLabel="name"
                        optionValue="id"
                        placeholder="Select Product"
                      ></p-dropdown>
                      <div
                        *ngIf="
                          inventoryAssignSumitted && inventoryAssignForm.controls.productId.errors
                        "
                        class="errorWrap text-danger"
                      >
                        <div
                          *ngIf="
                            inventoryAssignSumitted &&
                            inventoryAssignForm.controls.productId.errors.required
                          "
                          class="error text-danger"
                        >
                          Product is required.
                        </div>
                      </div>
                    </div>
                  </div>
                  <br />
                  <br />
                  <div
                    class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                    *ngIf="macAddressList2.length > 0 && getAllPairItemMacFlag"
                  >
                    <div class="form-group">
                      <p-table
                        #dt
                        [value]="macAddressList2"
                        [(selection)]="selectedMACAddress2"
                        dataKey="id"
                        styleClass="p-datatable-customers"
                        [rowHover]="true"
                        [loading]="loading"
                        [filterDelay]="0"
                        [globalFilterFields]="['serialNumber', 'condition']"
                        responsiveLayout="scroll"
                        [scrollable]="true"
                        scrollHeight="250px"
                      >
                        <ng-template pTemplate="caption">
                          <div class="flex align-items-center justify-content-between">
                            <span class="p-input-icon-left">
                              <input
                                class="form-control"
                                pInputText
                                type="text"
                                [(ngModel)]="cardFileterGlobal"
                                [ngModelOptions]="{ standalone: true }"
                                (input)="dt.filterGlobal($event.target.value, 'contains')"
                                placeholder="Global Search Filter"
                              />
                            </span>
                            &nbsp;
                            <button
                              type="button"
                              class="btn btn-default"
                              (click)="clearcardFileterGlobal(dt)"
                            >
                              Clear
                            </button>
                          </div>
                        </ng-template>
                        <br />
                        <ng-template pTemplate="header">
                          <tr>
                            <th style="width: 10%"></th>
                            <th>Item Type</th>
                            <th>Serial Number</th>
                            <th>Action</th>
                          </tr>
                        </ng-template>
                        <ng-template let-product pTemplate="body">
                          <tr>
                            <td style="width: 10%">
                              <p-tableRadioButton
                                [value]="product"
                                *ngIf="getAllPairProductFlag && product.customerId == null"
                                (click)="editCardSerialMapping(product.id)"
                              ></p-tableRadioButton>
                            </td>
                            <td>
                              {{ product.condition }}
                            </td>
                            <td>
                              <input
                                type="text"
                                name="serialNumber"
                                class="form-control"
                                [value]="product.serialNumber"
                                [(ngModel)]="product.serialNumber"
                                [ngModelOptions]="{ standalone: true }"
                                [disabled]="enterCardSerial == product.id ? false : true"
                              />
                              <!-- {{ product.serialNumber }} -->
                            </td>
                            <td>
                              <button
                                (click)="editCardSerial(product.id)"
                                [disabled]="editCardSerialBtn == product.id ? false : true"
                                class="curson_pointer approve-btn"
                                title="Edit Mac"
                              >
                                <img src="assets/img/ioc01.jpg" />
                              </button>
                              <button
                                id="addAtt"
                                class="btn btn-primary"
                                (click)="saveMacidMapping(product.itemId, product)"
                                style="object-fit: cover; padding: 5px 8px"
                                icon="pi pi-check"
                                [disabled]="enterCardSerial == product.id ? false : true"
                                [disabled]="editCardSerialBtn == product.id ? false : true"
                              >
                                Add
                              </button>
                            </td>
                          </tr>
                        </ng-template>
                        <ng-template pTemplate="summary">
                          <p-paginator
                            (onPageChange)="paginateMacAddress2Data($event)"
                            [first]="newFirstMacAddress2"
                            [rows]="macAddress2ListdataitemsPerPage"
                            [totalRecords]="macAddress2ListtotalRecords"
                            [rowsPerPageOptions]="[5, 10, 20, 50, 100, 1000]"
                            [(ngModel)]="macAddress2ListdataitemsPerPage"
                          ></p-paginator>
                        </ng-template>
                      </p-table>
                    </div>
                  </div>
                  <!-- Discount To Other Inventory Pair Item-->
                  <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="oldOfferBasedDiscountCardPairFlag">
                        <div class="form-group">
                          <label>Discount</label>
                          <input
                            [(ngModel)]="selectedCustDiscount"
                            class="form-control"
                            formControlName="discount"
                            type="text"
                            readonly
                          />
                        </div>
                      </div> -->
                  <!-- Old Offer Price ORGANIZATION To Other Inventory Card Pair Item-->
                  <!-- <div
                        class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                        *ngIf="oldOfferPriceCardFlag"
                      >
                        <div class="form-group">
                          <label>Old Offer Price</label>
                          <input
                            class="form-control"
                            [(ngModel)]="oldOfferCard"
                            type="text"
                            formControlName="offerPrice"
                            readonly
                          />
                        </div>
                      </div> -->
                  <!-- New Offer Price ORGANIZATION To Other Inventory Pair Item-->
                  <!-- <div
                        class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                        *ngIf="oldOfferPriceCardFlag"
                      >
                        <div class="form-group">
                          <label>New Offer Price</label>
                          <input
                            class="form-control"
                            [(ngModel)]="newOfferCard"
                            type="text"
                            formControlName="newAmount"
                            (keypress)="newOfferPriceValidation($event)"
                            placeholder="Enter new offer price"
                            [readonly]="!newOfferCardFlag"
                          />
                        </div>
                      </div> -->
                  <!-- Old Offer Price CUSTOMER To Other Inventory Pair Item-->
                  <!-- <div
                        class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
                        *ngIf="oldOfferBasedDiscountCardPairFlag"
                      >
                        <div class="form-group">
                          <label>Old Offer Price</label>
                          <input
                            class="form-control"
                            [(ngModel)]="oldOfferCard"
                            type="text"
                            formControlName="offerPrice"
                            readonly
                          />
                        </div>
                      </div> -->
                  <!-- New Offer Price CUSTOMER To Other Inventory Pair Item-->
                  <!-- <div
                        class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
                        *ngIf="oldOfferBasedDiscountCardPairFlag"
                      >
                        <div class="form-group">
                          <label>New Offer Price</label>
                          <input
                            class="form-control"
                            [(ngModel)]="newOfferCard"
                            (keypress)="newOfferPriceValidation($event)"
                            type="text"
                            formControlName="newAmount"
                            placeholder="Enter new offer price"
                            [readonly]="!newOfferCardFlag"
                          />
                        </div>
                      </div> -->
                </div>
              </div>
            </ng-template>
          </p-splitter>
        </div>
        <div *ngIf="getAllNonSerializedProductFlag">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Product*</label>
              <p-dropdown
                *ngIf="getAllNonSerializedProductFlag"
                (onChange)="getNonTrackableProductQty($event)"
                [options]="allActiveNonTrackableProducts"
                filter="true"
                filterBy="name"
                formControlName="productId"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Product"
              ></p-dropdown>
              <div
                *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.productId.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSumitted &&
                    inventoryAssignForm.controls.productId.errors.required
                  "
                  class="error text-danger"
                >
                  Product is required.
                </div>
              </div>
            </div>
          </div>
          <tr>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label style="margin-bottom: 1.5rem"> Available Quantity </label>
                  <!-- &nbsp;&nbsp;&nbsp; -->
                  <br />
                  <label>
                    {{ this.availableQty }}
                  </label>
                </div>
              </div>
            </th>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label>Assign Quantity*</label>
                  <input
                    type="number"
                    (keypress)="assignQuantityValidation($event)"
                    class="form-control"
                    placeholder="Enter Assign Quantity"
                    formControlName="nonSerializedQty"
                    [ngClass]="{
                      'is-invalid':
                        inventoryAssignSumitted &&
                        inventoryAssignForm.controls.nonSerializedQty.errors
                    }"
                  />
                  <div class="error text-danger" *ngIf="this.showQtyError">
                    Assign quantity must be less than available quantity.
                  </div>
                  <div class="error text-danger" *ngIf="this.negativeAssignQtyError">
                    Assign quantity must be greater than 0.
                  </div>
                  <div
                    class="errorWrap text-danger"
                    *ngIf="
                      inventoryAssignSumitted &&
                      inventoryAssignForm.controls.nonSerializedQty.errors
                    "
                  >
                    <div
                      class="error text-danger"
                      *ngIf="
                        inventoryAssignSumitted &&
                        inventoryAssignForm.controls.nonSerializedQty.required
                      "
                    >
                      Assign Quantity is required.
                    </div>
                    <div
                      *ngIf="
                        inventoryAssignSumitted &&
                        inventoryAssignForm.controls.nonSerializedQty.errors.pattern
                      "
                      class="error text-danger"
                    >
                      Only Numeric value are allowed.
                    </div>
                  </div>
                </div>
              </div>
            </th>
            <th>
              <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="availableQtyFlag">
                <div class="form-group">
                  <label>{{ this.UOM }}</label>
                </div>
              </div>
            </th>
          </tr>
        </div>
        <!-- Invoice To Other Inventory Single Item-->
        <div class="row" style="margin-left: auto; margin-right: auto" *ngIf="billToSigleFlag">
          <!-- Bill To Other Inventory Single Item-->
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4" *ngIf="billToSigleFlag">
            <div class="form-group">
              <label>Bill To*</label>
              <p-dropdown
                (onChange)="selectBillToSingle($event)"
                [options]="billToData"
                formControlName="billTo"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Bill To"
              ></p-dropdown>
            </div>
          </div>
          <!-- Discount To Other Inventory Single Item-->
          <!-- <div
                class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
                *ngIf="oldOfferBasedDiscountSingleFlag"
              >
                <div class="form-group">
                  <label>Discount</label>
                  <input
                    [(ngModel)]="selectedCustDiscount"
                    class="form-control"
                    formControlName="discount"
                    type="text"
                    readonly
                  />
                </div>
              </div> -->
          <!-- Invoice To ORG Other Inventory Single Item-->
          <div
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
            *ngIf="billToSigleFlag && isInvoiceDataSingleFlag"
          >
            <div class="form-group">
              <label>Invoice To Org</label>
              <p-dropdown
                (onChange)="selInvoiceToOrgSingle($event)"
                [options]="isInvoiceData"
                formControlName="isInvoiceToOrg"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Invoice to org or not"
              ></p-dropdown>
            </div>
          </div>
          <!-- Required To Approval Other Inventory Single Item-->
          <!-- <div
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
            *ngIf="billToSigleFlag && isInvoiceDataSingleFlag && requiredApprovalSingleFlag"
          >
            <div class="form-group">
              <label>Required To Approval</label>
              <p-dropdown
                [options]="isRequiredApprovalData"
                formControlName="isRequiredApproval"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Required to approval"
              ></p-dropdown>
            </div>
          </div> -->
        </div>
        <div
          class="row"
          style="margin-left: auto; margin-right: auto"
          *ngIf="oldOfferPriceSingleFlag"
        >
          <!-- Old Offer Price ORGANIZATION To Other Inventory Single Item-->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferPriceSingleFlag">
            <div class="form-group">
              <label>Old Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="oldOfferOtherSigle"
                type="text"
                formControlName="offerPrice"
                readonly
              />
            </div>
          </div>
          <!-- New Offer Price ORGANIZATION To Other Inventory Single Item-->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferPriceSingleFlag">
            <div class="form-group">
              <label>New Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="newOfferOtherSigle"
                type="text"
                formControlName="newAmount"
                placeholder="Enter new offer price"
                (keypress)="newOfferPriceValidation($event)"
                [readonly]="!newOfferSingleFlag"
              />
              <div class="error text-danger" *ngIf="this.showError">
                {{ this.priceErrorMsg }}
              </div>
            </div>
          </div>
          <br />
        </div>
        <div
          class="row"
          style="margin-left: auto; margin-right: auto"
          *ngIf="oldOfferBasedDiscountSingleFlag"
        >
          <!-- Old Offer Price CUSTOMER To Other Inventory Single Item-->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferBasedDiscountSingleFlag">
            <div class="form-group">
              <label>Old Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="oldOfferOtherSigle"
                type="text"
                formControlName="offerPrice"
                readonly
              />
            </div>
          </div>
          <!-- New Offer Price CUSTOMER To Other Inventory Single Item-->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferBasedDiscountSingleFlag">
            <div class="form-group">
              <label>New Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="newOfferOtherSigle"
                type="text"
                (keypress)="newOfferPriceValidation($event)"
                formControlName="newAmount"
                placeholder="Enter new offer price"
                [readonly]="!newOfferSingleFlag"
              />
              <div class="error text-danger" *ngIf="this.showError">
                {{ this.priceErrorMsg }}
              </div>
            </div>
          </div>
          <br />
        </div>
        <!-- Bill to Support for Non Serialized Item -->
        <div class="row" style="margin-left: auto; margin-right: auto" *ngIf="availableQtyFlag">
          <!-- Bill To Other Inventory Non Serialized Item-->
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4" *ngIf="availableQtyFlag">
            <div class="form-group">
              <label>Bill To*</label>
              <p-dropdown
                (onChange)="selectBillToNonSerialize($event)"
                [options]="billToData"
                formControlName="billTo"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Bill To"
              ></p-dropdown>
            </div>
          </div>
          <!-- Discount To Other Inventory Non Serialized Item-->
          <!-- <div
                class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
                *ngIf="availableQtyFlag && oldOfferBasedDiscountNonSerialFlag"
              >
                <div class="form-group">
                  <label>Discount</label>
                  <input
                    [(ngModel)]="selectedCustDiscount"
                    class="form-control"
                    formControlName="discount"
                    type="text"
                    readonly
                  />
                </div>
              </div> -->
          <!-- Invoice To ORG Other Inventory Non Serialized Item-->
          <div
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
            *ngIf="availableQtyFlag && isInvoiceDataNonSerialFlag"
          >
            <div class="form-group">
              <label>Invoice To Org</label>
              <p-dropdown
                (onChange)="selInvoiceToOrgNonSerial($event)"
                [options]="isInvoiceData"
                formControlName="isInvoiceToOrg"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Invoice to org or not"
              ></p-dropdown>
            </div>
          </div>
          <!-- Required To Approval Other Inventory Non Serialized Item-->
          <!-- <div
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
            *ngIf="availableQtyFlag && isInvoiceDataNonSerialFlag && requiredApprovalNonSerialFlag"
          >
            <div class="form-group">
              <label>Required To Approval</label>
              <p-dropdown
                [options]="isRequiredApprovalData"
                formControlName="isRequiredApproval"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Required to approval"
              ></p-dropdown>
            </div>
          </div> -->
        </div>
        <div
          class="row"
          style="margin-left: auto; margin-right: auto"
          *ngIf="availableQtyFlag && oldOfferPriceNonSerialFlag"
        >
          <!-- Old Offer Price ORGANIZATION To Other Inventory Non Serialized Item-->
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            *ngIf="availableQtyFlag && oldOfferPriceNonSerialFlag"
          >
            <div class="form-group">
              <label>Per Unit Price</label>
              <input
                class="form-control"
                [(ngModel)]="perUOMCharge"
                type="text"
                formControlName="offerPrice"
                readonly
              />
            </div>
          </div>
          <!-- New Offer Price ORGANIZATION To Other Inventory Non Serialized Item-->
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            *ngIf="availableQtyFlag && oldOfferPriceNonSerialFlag"
          >
            <div class="form-group">
              <label>New UOM Price</label>
              <input
                class="form-control"
                [(ngModel)]="newUOMAmount"
                type="text"
                formControlName="newAmount"
                placeholder="Enter new offer price"
                [readonly]="!newOfferNonSerialFlag"
              />
              <div class="error text-danger" *ngIf="this.showError">
                {{ this.priceErrorMsg }}
              </div>
            </div>
          </div>
          <br />
        </div>
        <div
          class="row"
          style="margin-left: auto; margin-right: auto"
          *ngIf="oldOfferBasedDiscountNonSerialFlag"
        >
          <!-- Old Offer Price CUSTOMER To Other Inventory Non Serialized Item-->
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            *ngIf="oldOfferBasedDiscountNonSerialFlag"
          >
            <div class="form-group">
              <label>Per Unit Price</label>
              <input
                class="form-control"
                [(ngModel)]="perUOMCharge"
                type="text"
                formControlName="offerPrice"
                readonly
              />
            </div>
          </div>
          <!-- New Offer Price CUSTOMER To Other Inventory Non Serialized Item-->
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            *ngIf="oldOfferBasedDiscountNonSerialFlag"
          >
            <div class="form-group">
              <label>New UOM Price</label>
              <input
                class="form-control"
                [(ngModel)]="newUOMAmount"
                type="text"
                (keypress)="newOfferPriceValidation($event)"
                formControlName="newAmount"
                placeholder="Enter new offer price"
                [readonly]="!newOfferNonSerialFlag"
              />
              <div class="error text-danger" *ngIf="this.showError">
                {{ this.priceErrorMsg }}
              </div>
            </div>
          </div>
          <br />
        </div>
        <!-- Remark for Non Serialized Item Assign -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="nonSerializedItemAssignFlag">
          <div class="form-group">
            <label>Non Serialized Invoice Remark*</label>
            <textarea
              [ngClass]="{
                'is-invalid':
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.nonSerializedItemRemark.errors
              }"
              class="form-control"
              formControlName="nonSerializedItemRemark"
              placeholder="Enter Non Serialized Invoice Remark"
            ></textarea>
            <div
              *ngIf="
                inventoryAssignSumitted &&
                inventoryAssignForm.controls.nonSerializedItemRemark.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.nonSerializedItemRemark.errors.required
                "
                class="error text-danger"
              >
                Non Serialized Invoice Remark is required.
              </div>
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.nonSerializedItemRemark.errors.pattern
                "
                class="error text-danger"
              >
                Maximum 255 charecter required.
              </div>
            </div>
          </div>
        </div>
        <!-- Bill To Other Inventory Pair Item-->
        <div class="row" style="margin-left: auto; margin-right: auto" *ngIf="billToPairFlag">
          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4" *ngIf="billToPairFlag">
            <div class="form-group">
              <label>Bill To*</label>
              <p-dropdown
                (onChange)="selectBillToPair($event)"
                [options]="billToData"
                formControlName="billTo"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Bill To"
              ></p-dropdown>
            </div>
          </div>
          <div
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
            *ngIf="billToPairFlag && isInvoiceDataPairFlag"
          >
            <div class="form-group">
              <label>Invoice To Org</label>
              <p-dropdown
                (onChange)="selInvoiceToPair($event)"
                [options]="isInvoiceData"
                formControlName="isInvoiceToOrg"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Invoice to org or not"
              ></p-dropdown>
            </div>
          </div>
          <!-- <div
            class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
            *ngIf="billToPairFlag && isInvoiceDataPairFlag && requiredApprovalPairFlag"
          >
            <div class="form-group">
              <label>Required To Approval</label>
              <p-dropdown
                [options]="isRequiredApprovalData"
                formControlName="isRequiredApproval"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Required to approval"
              ></p-dropdown>
            </div>
          </div> -->
          <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="discountPairFlag">
                            <div class="form-group">
                              <label>Discount</label>
                              <input
                                [(ngModel)]="selectedPairDiscount"
                                class="form-control"
                                formControlName="discount"
                                type="text"
                                readonly
                              />
                            </div>
                          </div> -->
        </div>
        <div class="row" style="margin-left: auto; margin-right: auto" *ngIf="oldOfferPriceSTBFlag">
          <!-- Old Offer Price ORGANIZATION To Other Inventory STB Pair Item-->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferPriceSTBFlag">
            <div class="form-group">
              <label>Old Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="oldOfferSTB"
                type="text"
                formControlName="offerPrice"
                readonly
              />
            </div>
          </div>
          <!-- New Offer Price ORGANIZATION To Other Inventory STB Pair Item-->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferPriceSTBFlag">
            <div class="form-group">
              <label>New Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="newOfferSTB"
                type="text"
                (keypress)="newOfferPriceValidation($event)"
                formControlName="newAmount"
                placeholder="Enter new offer price"
                [readonly]="!newOfferSTBFlag"
              />
              <div class="error text-danger" *ngIf="this.showError">
                {{ this.priceErrorMsg }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="row"
          style="margin-left: auto; margin-right: auto"
          *ngIf="oldOfferBasedDiscountSTBPairFlag"
        >
          <!-- Old Offer Price CUSTOMER To Other Inventory Pair Item-->
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            *ngIf="oldOfferBasedDiscountSTBPairFlag"
          >
            <div class="form-group">
              <label>Old Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="oldOfferSTB"
                type="text"
                formControlName="offerPrice"
                readonly
              />
            </div>
          </div>
          <!-- New Offer Price CUSTOMER To Other Inventory Pair Item-->
          <div
            class="col-lg-6 col-md-6 col-sm-6 col-xs-12"
            *ngIf="oldOfferBasedDiscountSTBPairFlag"
          >
            <div class="form-group">
              <label>New Offer Price</label>
              <input
                class="form-control"
                [(ngModel)]="newOfferSTB"
                type="text"
                (keypress)="newOfferPriceValidation($event)"
                formControlName="newAmount"
                placeholder="Enter new offer price"
                [readonly]="!newOfferSTBFlag"
              />
              <div class="error text-danger" *ngIf="this.showError">
                {{ this.priceErrorMsg }}
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          <label>Billable To</label>
          <br />
          <p-dropdown
            [disabled]="true"
            [options]="billableCusList"
            [showClear]="true"
            filter="true"
            filterBy="name"
            formControlName="billabecustId"
            optionLabel="name"
            optionValue="id"
            placeholder="Select a Billable"
            styleClass="disableDropdown"
          ></p-dropdown>
          <button
            type="button"
            [disabled]="iscustomerEdit"
            (click)="modalOpenParentCustomer('billable')"
            class="btn btn-primary"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
          >
            <i class="fa fa-plus-square"></i>
          </button>
          <button
            [disabled]="iscustomerEdit"
            class="btn btn-danger"
            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            (click)="removeSelParentCust('billable')"
          >
            <i class="fa fa-trash"></i>
          </button>
        </div>
        <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="form-group">
                <label>Status*</label>
                <p-dropdown
                  [options]="inventoryStatus"
                  filter="true"
                  filterBy="label"
                  formControlName="status"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select status"
                ></p-dropdown>
                <div
                  *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.status.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      inventoryAssignSumitted && inventoryAssignForm.controls.status.errors.required
                    "
                    class="error text-danger"
                  >
                    Status is required.
                  </div>
                </div>
              </div>
            </div> -->
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Payment Owner*</label>
            <p-dropdown
              [disabled]="true"
              [options]="staffSelectList"
              optionLabel="name"
              optionValue="id"
              filterBy="firstname"
              placeholder="Select a staff"
              [filter]="true"
              formControlName="paymentOwnerId"
              [showClear]="true"
              styleClass="disableDropdown"
            >
              <ng-template let-data pTemplate="item">
                <div class="item-drop1">
                  <span class="item-value1"> {{ data.name }} </span>
                </div>
              </ng-template>
            </p-dropdown>

            <button
              type="button"
              (click)="modalOpenSelectStaff('inventoryAssign')"
              class="btn btn-primary"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-plus-square"></i>
            </button>
            <button
              type="button"
              (click)="removeOtherSelectStaff()"
              class="btn btn-danger"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-trash"></i>
            </button>
            <div
              *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.paymentOwnerId.errors"
              class="errorWrap text-danger"
            >
              <div
                class="error text-danger"
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.paymentOwnerId.errors.required
                "
              >
                Payment owner is required
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Assign Date*</label>
            <div>
              <p-calendar
                [style]="{ width: '50%' }"
                [numberOfMonths]="1"
                [showSeconds]="true"
                [showTime]="true"
                formControlName="assignedDateTime"
                inputId="time"
              ></p-calendar>
              <div
                *ngIf="
                  inventoryAssignSumitted && inventoryAssignForm.controls.assignedDateTime.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSumitted &&
                    inventoryAssignForm.controls.assignedDateTime.errors.required
                  "
                  class="error text-danger"
                >
                  Assign Date is required.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="assigneOtherInventory()"
        class="btn btn-primary btn-sm"
        id="submit"
        type="submit"
        *ngIf="serializedItemAssignFlag"
      >
        <i class="fa fa-check-circle"></i>
        Assign Other Inventory
      </button>
      <button
        (click)="assigneOtherInventoryForNonSerializedItem()"
        class="btn btn-primary btn-sm"
        id="submit"
        type="submit"
        *ngIf="nonSerializedItemAssignFlag"
      >
        <i class="fa fa-check-circle"></i>
        Assign Other Inventory
      </button>
      <button #closebutton (click)="assignOtherInventoryModalClose()" class="btn btn-danger btn-sm">
        Close
      </button>
      <br />
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Assign Inventory With External Item Group"
  [(visible)]="displayAssignInventoryWithExternalItemGroup"
  [style]="{ width: '50%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <form [formGroup]="externalInventoryAssignForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Service*</label>
            <p-dropdown
              (onChange)="getServiceAtExternalInventory($event)"
              [options]="uniqueServices"
              filter="true"
              filterBy="planName"
              formControlName="serviceId"
              optionLabel="service"
              optionValue="serviceId"
              placeholder="Select Service"
            ></p-dropdown>
            <div
              *ngIf="
                externalInventoryAssignSumitted &&
                externalInventoryAssignForm.controls.serviceId.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  externalInventoryAssignSumitted &&
                  externalInventoryAssignForm.controls.serviceId.errors.required
                "
                class="error text-danger"
              >
                Service is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getAllConnectionNumberFlag">
          <div class="form-group">
            <label>Connection No*</label>
            <p-dropdown
              [options]="this.connectionNoList"
              (onChange)="onChangeConnection($event)"
              filter="true"
              filterBy="planName"
              formControlName="connectionNo"
              optionLabel="connection_no"
              optionValue="connection_no"
              placeholder="Select Connection No"
            ></p-dropdown>
            <div
              *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.connectionNo.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  inventoryAssignSumitted &&
                  inventoryAssignForm.controls.connectionNo.errors.required
                "
                class="error text-danger"
              >
                Connection No is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getExternalProductFlag">
          <div class="form-group">
            <label>Product*</label>
            <p-dropdown
              (onChange)="getExternalItemList($event)"
              [options]="allActiveProducts"
              filter="true"
              filterBy="name"
              formControlName="productId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select Product"
            ></p-dropdown>
            <div
              *ngIf="
                externalInventoryAssignSumitted &&
                externalInventoryAssignForm.controls.productId.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  externalInventoryAssignSumitted &&
                  externalInventoryAssignForm.controls.productId.errors.required
                "
                class="error text-danger"
              >
                Product is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getExternalItemListFlag">
          <div class="form-group">
            <label>Select Extrenal Item Group*</label>
            <p-dropdown
              (onChange)="getAllMappingByExternal($event)"
              [options]="externalItemList"
              filter="true"
              filterBy="inwardNumber"
              formControlName="externalItemId"
              optionLabel="externalItemGroupNumber"
              optionValue="id"
              placeholder="Select External Item Number"
            ></p-dropdown>
            <div
              *ngIf="
                externalInventoryAssignSumitted &&
                externalInventoryAssignForm.controls.externalItemId.errors
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="
                  externalInventoryAssignSumitted &&
                  externalInventoryAssignForm.controls.externalItemId.errors.required
                "
                class="error text-danger"
              >
                Extrenal Item Group is required.
              </div>
            </div>
          </div>
        </div>
        <div
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
          *ngIf="macExternalListFlag && macAddressList.length > 0"
        >
          <div class="form-group">
            <p-table
              #dt
              [(selection)]="selectedExternalMACAddress"
              [value]="macAddressList"
              dataKey="id"
              styleClass="p-datatable-customers"
              [rowHover]="true"
              [rows]="5"
              [showCurrentPageReport]="true"
              [rowsPerPageOptions]="[5, 10, 25, 50]"
              [loading]="loading"
              [paginator]="true"
              currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
              [filterDelay]="0"
              [globalFilterFields]="['serialNumber', 'macAddress', 'id']"
              responsiveLayout="scroll"
              [scrollable]="true"
              scrollHeight="250px"
            >
              <ng-template pTemplate="caption">
                <div class="flex align-items-center justify-content-between">
                  <span class="p-input-icon-left">
                    <input
                      class="form-control"
                      pInputText
                      type="text"
                      [(ngModel)]="externalItemsFilterGlobal"
                      [ngModelOptions]="{ standalone: true }"
                      (input)="dt.filterGlobal($event.target.value, 'contains')"
                      placeholder="Global Search Filter"
                    />
                  </span>
                  &nbsp;
                  <button
                    type="button"
                    class="btn btn-default"
                    (click)="clearexternalItemsFilterGlobal(dt)"
                  >
                    Clear
                  </button>
                </div>
              </ng-template>
              <ng-template pTemplate="header">
                <tr>
                  <th style="width: 10%">
                    <!-- <p-tableRadioButton></p-tableRadioButton> -->
                  </th>
                  <th>Item Id</th>
                  <th *ngIf="this.hasMac">MAC Address</th>
                  <th *ngIf="this.hasSerial">Serial Number</th>
                </tr>
              </ng-template>
              <ng-template let-product pTemplate="body">
                <tr>
                  <td style="width: 10%">
                    <p-tableRadioButton
                      *ngIf="product.customerId == null"
                      [value]="product"
                    ></p-tableRadioButton>
                  </td>
                  <td>{{ product.id }}</td>
                  <td *ngIf="this.hasMac">{{ product.macAddress }}</td>
                  <td *ngIf="this.hasSerial">
                    {{ product.serialNumber }}
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>

        <!-- <label *ngIf="!this.productHasMac && !this.productHasSerial" style="font-weight: bold">
              Available Quantity :- {{ this.availableQty }}
            </label> -->

        <!-- <label>Quantity in {{ this.unit }} *</label>
            <input
              [ngClass]="{
                'is-invalid': submitted && this.inventoryAssignForm.controls.qty.errors
              }"
              class="form-control"
              formControlName="qty"
              placeholder="Enter Quantity"
              type="text"
            />
            <div *ngIf="this.showQtyError" class="error text-danger">
              Quantity must be greater than used quantity.
            </div>
            <div *ngIf="this.showQtySelectionError" class="error text-danger">
              Quantity must be greater than selected MAC addresses.
            </div> -->

        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
          <div class="form-group">
            <label>Assign Date*</label>
            <div>
              <p-calendar
                [numberOfMonths]="1"
                [showSeconds]="true"
                [showTime]="true"
                formControlName="assignedDateTime"
                inputId="time"
              ></p-calendar>
              <div
                *ngIf="
                  externalInventoryAssignSumitted &&
                  externalInventoryAssignForm.controls.assignedDateTime.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    externalInventoryAssignSumitted &&
                    externalInventoryAssignForm.controls.assignedDateTime.errors.required
                  "
                  class="error text-danger"
                >
                  Assign Date is required.
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="form-group">
                <label>Status*</label>
                <p-dropdown
                  [options]="inventoryStatus"
                  filter="true"
                  filterBy="label"
                  formControlName="status"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select status"
                ></p-dropdown>
                <div
                  *ngIf="
                    externalInventoryAssignSumitted &&
                    externalInventoryAssignForm.controls.status.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      externalInventoryAssignSumitted &&
                      externalInventoryAssignForm.controls.status.errors.required
                    "
                    class="error text-danger"
                  >
                    Status is required.
                  </div>
                </div>
              </div>
            </div> -->
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="assigneExternalInventory()"
        class="btn btn-primary btn-sm"
        id="submit"
        type="submit"
      >
        <i class="fa fa-check-circle"></i>
        External Inventory
      </button>
      <button
        #closebutton
        (click)="assignExternalInventoryModalClose()"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="DTV History"
  [(visible)]="displayDTVHistory"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <p-table
      #dt
      [value]="inventoryLogDetailsList"
      dataKey="id"
      styleClass="p-datatable-customers"
      [rowHover]="true"
      [rows]="5"
      [showCurrentPageReport]="true"
      [rowsPerPageOptions]="[5, 10, 25, 50]"
      responsiveLayout="scroll"
      [scrollable]="true"
      scrollHeight="300px"
      [loading]="loading"
      [paginator]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [filterDelay]="0"
      [globalFilterFields]="[
        'updatedate',
        'stbSerialNumber',
        'cardSerialNumber',
        'evenType',
        'lastModifiedByName'
      ]"
    >
      <!-- <ng-template pTemplate="caption">
            <div class="flex align-items-center justify-content-between">
              <span class="p-input-icon-left">
                <input
                  class="form-control"
                  pInputText
                  type="text"
                  [(ngModel)]="getAllInventoryofCustFilterGlobal"
                  (input)="dt.filterGlobal($event.target.value, 'contains')"
                  placeholder="Global Search Filter"
                />
              </span>
              &nbsp;
              <button
                type="button"
                class="btn btn-default"
                #btnClose
                (click)="cleargetAllInventoryofCustFilterGlobal(dt)"
              >
                Clear
              </button>
            </div>
          </ng-template> -->
      <ng-template pTemplate="header">
        <tr>
          <!-- <th>Item Id</th> -->
          <th>Event Date</th>
          <th>STB Serial Number</th>
          <th>Card Serial Number</th>
          <th>Event Type</th>
          <th>Staff Name</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-product let-rowIndex="rowIndex">
        <tr>
          <!-- <td>
              <span *ngIf="product.id">
                {{ product.id }}
              </span>
              <span *ngIf="!product.id">-</span>
            </td> -->
          <td>
            <span *ngIf="product.updatedate">
              {{ product.updatedate }}
            </span>
            <span *ngIf="!product.updatedate">-</span>
          </td>
          <td>
            <span *ngIf="product.stbSerialNumber">
              {{ product.stbSerialNumber }}
            </span>
            <span *ngIf="!product.stbSerialNumber">-</span>
          </td>
          <td>
            <span *ngIf="product.cardSerialNumber">
              {{ product.cardSerialNumber }}
            </span>
            <span *ngIf="!product.cardSerialNumber">-</span>
          </td>
          <td>
            <span *ngIf="product.evenType">
              {{ product.evenType }}
            </span>
            <span *ngIf="!product.evenType">-</span>
          </td>
          <td>
            <span *ngIf="product.lastModifiedByName">
              {{ product.lastModifiedByName }}
            </span>
            <span *ngIf="!product.lastModifiedByName">-</span>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="button"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeInventoryLogModal()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Inventory Integration"
  [(visible)]="integration"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <p-table
      #dt
      [value]="inventoryLogDetailsList"
      dataKey="id"
      styleClass="p-datatable-customers"
      [rowHover]="true"
      [rows]="5"
      [showCurrentPageReport]="true"
      [rowsPerPageOptions]="[5, 10, 25, 50]"
      responsiveLayout="scroll"
      [scrollable]="true"
      scrollHeight="300px"
      [loading]="loading"
      [paginator]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [filterDelay]="0"
      [globalFilterFields]="[
        'updatedate',
        'stbSerialNumber',
        'cardSerialNumber',
        'evenType',
        'lastModifiedByName'
      ]"
    >
      <ng-template pTemplate="header">
        <tr>
          <th>Serial Number</th>
          <th>Operation</th>
          <th>Status</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-product let-rowIndex="rowIndex">
        <tr>
          <td>
            <span>
              {{ product.status }}
            </span>
          </td>
          <td>
            <span>
              {{ product.operation }}
            </span>
          </td>
          <td>
            <span>
              {{ product.status }}
            </span>
          </td>

          <td>
            <span>
              {{ product.status }}
            </span>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="button"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
        (click)="closeIntegrationInventoryLogModal()"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- Swap Inventory Plan-->
<p-dialog
  header="Swap Inventory Plan"
  [(visible)]="displaySwapInventoryPlan"
  [style]="{ width: '90%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <form [formGroup]="childAndParentInventoryDetailsForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Service*</label>
            <p-dropdown
              (onChange)="setServiceId($event)"
              [options]="this.uniqueparentService"
              formControlName="serviceName"
              optionLabel="serviceName"
              optionValue="serviceName"
              placeholder="Select Service"
            ></p-dropdown>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Swap Options*</label>
            <p-dropdown
              [options]="swapOptions"
              formControlName="swapOption"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Swap Option"
            ></p-dropdown>
          </div>
        </div>

        <div
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
          *ngIf="childAndParentInventoryDetailsForm.controls.swapOption.value == 'parent_child'"
        >
          <div class="form-group">
            <label>Parent Connection*</label>
            <p-dropdown
              [options]="filtteredParentConnection"
              formControlName="parentConnectionNo"
              optionLabel="connectionNo"
              optionValue="connectionNo"
              filter="true"
              filterBy="serviceName"
              placeholder="Select Service"
            ></p-dropdown>
          </div>
        </div>
        <div
          class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
          *ngIf="childAndParentInventoryDetailsForm.controls.swapOption.value == 'child_child'"
        >
          <div class="form-group">
            <label>Child Connection*</label>
            <p-dropdown
              [options]="filtteredChildConnection"
              (onChange)="onChildSerChange($event.value)"
              formControlName="parentConnectionNo"
              optionLabel="connectionNo"
              optionValue="connectionNo"
              filter="true"
              filterBy="serviceName"
              placeholder="Select Service"
            ></p-dropdown>
          </div>
        </div>

        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Child Connection*</label>
            <p-dropdown
              [options]="filtteredChildConnection1"
              formControlName="childConnectionNo"
              (onChange)="onChildSerChange1($event.value)"
              optionLabel="connectionNo"
              optionValue="connectionNo"
              filter="true"
              filterBy="serviceName"
              placeholder="Select Service"
            ></p-dropdown>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="swapInventoryPlan()"
        class="btn btn-primary btn-sm"
        id="submit"
        type="submit"
      >
        <i class="fa fa-check-circle"></i>
        Swap
      </button>
      <button
        #closebutton
        (click)="swapInventoryPlanModalClose()"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
      >
        Close
      </button>
      <br />
    </div>
  </div>
</p-dialog>

<!-- Assign Plan Inventory -->

<p-dialog
  header="Assign Inventory With Plan"
  [(visible)]="displayAssignPlanInventoryModal"
  [styleClass]="'nearSearchModalLocation'"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <ng-template pTemplate="content">
    <div class="modal-body">
      <form [formGroup]="planInventoryAssignForm">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Service*</label>
              <p-dropdown
                #ddlService
                (onChange)="getServiceAtPlanInventory($event, ddlService)"
                [options]="uniqueServices"
                filter="true"
                filterBy="planName"
                formControlName="serviceId"
                optionLabel="service"
                optionValue="serviceId"
                placeholder="Select Service"
              ></p-dropdown>
              <div
                *ngIf="
                  planInventoryAssignSumitted && planInventoryAssignForm.controls.serviceId.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.serviceId.errors.required
                  "
                  class="error text-danger"
                >
                  Service is required.
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="getAllConnectionNumberFlag">
            <div class="form-group">
              <label>Connection No*</label>
              <p-dropdown
                (onChange)="getConnectionNoDetails($event)"
                [options]="this.connectionNoList"
                filter="true"
                filterBy="planName"
                formControlName="connectionNo"
                optionLabel="connection_no"
                optionValue="connection_no"
                placeholder="Select Connection No"
              ></p-dropdown>
              <div
                *ngIf="inventoryAssignSumitted && inventoryAssignForm.controls.connectionNo.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    inventoryAssignSumitted &&
                    inventoryAssignForm.controls.connectionNo.errors.required
                  "
                  class="error text-danger"
                >
                  Connection No is required.
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group">
              <label>Plan Category</label>
              <input
                [value]="custPlanCategory"
                class="form-control"
                type="text"
                value=""
                readonly
              />
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="planGroupPlanMappingFlag">
            <div class="form-group">
              <label>Plan Group Name</label>
              <!-- <input [value]="planGroupName" class="form-control" type="text" value="" readonly /> -->
              <input class="form-control" type="text" formControlName="planGroupName" readonly />
            </div>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getAllPlanFlag && individualPlanMappingFlag"
          >
            <div class="form-group">
              <label>Plan*</label>
              <p-dropdown
                (onChange)="getAllPlanIvnetoryId($event)"
                [options]="this.planList"
                filter="true"
                filterBy="planName"
                formControlName="planId"
                optionLabel="planName"
                optionValue="planId"
                placeholder="Select Plan Name"
              ></p-dropdown>
              <div
                *ngIf="
                  planInventoryAssignSumitted && planInventoryAssignForm.controls.planId.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.planId.errors.required
                  "
                  class="error text-danger"
                >
                  Plan is required.
                </div>
              </div>
            </div>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getAllPlanFlag && planGroupPlanMappingFlag"
          >
            <div class="form-group">
              <label>Plan*</label>
              <p-dropdown
                (onChange)="getAllPlanGroupPlanIvnetoryId($event)"
                [options]="this.planList"
                filter="true"
                filterBy="planName"
                formControlName="planId"
                optionLabel="planName"
                optionValue="planId"
                placeholder="Select Plan Name"
              ></p-dropdown>
              <div
                *ngIf="
                  planInventoryAssignSumitted && planInventoryAssignForm.controls.planId.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.planId.errors.required
                  "
                  class="error text-danger"
                >
                  Plan is required.
                </div>
              </div>
            </div>
          </div>
          <!-- Plan Inventory Id Selection for Plan Iventory Assign -->
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getPlanInventoryIdFlag && individualPlanMappingFlag"
          >
            <div class="form-group">
              <label>Plan Inventory Id*</label>
              <p-dropdown
                (onChange)="getProductCatAndProduct($event)"
                [options]="this.getAllPlanIvnetoryIdOnPlanIdList"
                filter="true"
                filterBy="name"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Plan Inventory Id"
              ></p-dropdown>
            </div>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getPlanInventoryIdFlag && planGroupPlanMappingFlag"
          >
            <div class="form-group">
              <label>Plan Inventory Id*</label>
              <p-dropdown
                (onChange)="getProductCatAndProductByPlanGroup($event)"
                [options]="this.getAllPlanIvnetoryIdOnPlanIdList"
                filter="true"
                filterBy="name"
                optionLabel="name"
                optionValue="id"
                placeholder="Select Plan Inventory Id"
              ></p-dropdown>
            </div>
          </div>
          <!-- Product Category Selection for Plan Inventory Assign -->
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getProductCategoryFlag && individualPlanMappingFlag"
          >
            <div class="form-group">
              <label>Product Category</label>
              <input
                [value]="productCategoryName"
                class="form-control"
                type="text"
                required
                value=""
                readonly
              />
            </div>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getProductCategoryFlag && planGroupPlanMappingFlag"
          >
            <div class="form-group">
              <label>Product Category</label>
              <input
                [value]="productCategoryName"
                class="form-control"
                type="text"
                required
                value=""
                readonly
              />
            </div>
          </div>
          <!-- Assembly Type Selection for Plan Inventory Assign -->
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="selAssemblyTypePlanFlag">
            <div class="form-group">
              <label>Assembly Type*</label>
              <p-dropdown
                (onChange)="getSelPlanAssemblyType($event)"
                [options]="productSelectionType"
                filter="true"
                filterBy="label"
                formControlName="itemAssemblyflag"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Assembly Type"
              ></p-dropdown>
              <div
                *ngIf="
                  planInventoryAssignSumitted &&
                  planInventoryAssignForm.controls.itemAssemblyflag.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.itemAssemblyflag.errors.required
                  "
                  class="error text-danger"
                >
                  Assembly Type is required.
                </div>
              </div>
            </div>
          </div>
          <!-- Assembly Type Selection for Plan Group Inventory Assign -->
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="selAssemblyTypePlanGroupFlag">
            <div class="form-group">
              <label>Assembly Type*</label>
              <p-dropdown
                (onChange)="getSelPlanAssemblyType($event)"
                [options]="productSelectionType"
                filter="true"
                filterBy="label"
                formControlName="itemAssemblyflag"
                optionLabel="label"
                optionValue="value"
                placeholder="Select Assembly Type"
              ></p-dropdown>
              <div
                *ngIf="
                  planInventoryAssignSumitted &&
                  planInventoryAssignForm.controls.itemAssemblyflag.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.itemAssemblyflag.errors.required
                  "
                  class="error text-danger"
                >
                  Assembly Type is required.
                </div>
              </div>
            </div>
          </div>
          <!-- Assembly Name Enter for Plan Inventory Assign -->
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="getAssemblyNameflag && (selAssemblyTypePlanFlag || selAssemblyTypePlanGroupFlag)"
          >
            <div class="form-group">
              <label>Assembly Name*</label>
              <input
                type="text"
                class="form-control"
                formControlName="itemAssemblyName"
                placeholder="Enter Assembly Name"
              />
              <div
                *ngIf="
                  planInventoryAssignSumitted &&
                  planInventoryAssignForm.controls.itemAssemblyName.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.itemAssemblyName.errors.required
                  "
                  class="error text-danger"
                >
                  Assembly Name is required.
                </div>
              </div>
            </div>
          </div>
          <!-- Item Condition Serialized Plan Item -->
          <div *ngIf="itemConditionPlanSeriFlag">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="form-group">
                <label>Item Condition*</label>
                <p-dropdown
                  (onChange)="selectItemConditionPlan($event)"
                  [options]="itemConditionData"
                  filter="true"
                  filterBy="label"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                  formControlName="itemType"
                >
                </p-dropdown>
              </div>
            </div>
          </div>
          <!-- Item Condition Serialized Plan Item -->
          <div *ngIf="itemConditionPlanPairFlag">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="form-group">
                <label>Item Condition*</label>
                <p-dropdown
                  (onChange)="selectItemConditionPlan($event)"
                  [options]="itemConditionData"
                  filter="true"
                  filterBy="label"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Item Type"
                >
                </p-dropdown>
              </div>
            </div>
          </div>
          <!-- Get All Product from Plan Product Mapping -->
          <div *ngIf="itemConditionPlanSeriFlag && getPlanSingleSplitterFlag">
            <div
              class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
              *ngIf="
                getProductForPlanInventoryAssignFlag &&
                (individualPlanMappingFlag || planGroupPlanMappingFlag)
              "
            >
              <div class="form-group">
                <label>Product*</label>
                <!-- <p-multiSelect
                  *ngIf="planInventoryAssignForm.controls.itemAssemblyName.enabled"
                  [options]="productByPlanList"
                  (onChange)="getMultipleMacAddressList($event)"
                  filter="true"
                  filterBy="name"
                  formControlName="productId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select Product"
                ></p-multiSelect> -->
                <p-dropdown
                  (onChange)="getPlanLevelMacAddressList($event)"
                  [options]="productByPlanList"
                  filter="true"
                  filterBy="name"
                  formControlName="productId"
                  optionLabel="name"
                  optionValue="id"
                  placeholder="Select Product"
                ></p-dropdown>
                <div
                  *ngIf="
                    planInventoryAssignSumitted && planInventoryAssignForm.controls.productId.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planInventoryAssignSumitted &&
                      planInventoryAssignForm.controls.productId.errors.required
                    "
                    class="error text-danger"
                  >
                    Product is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Get All Product from Plan-Group Product Mapping
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
              *ngIf="getProductForPlanInventoryAssignFlag && planGroupPlanMappingFlag">
              <div class="form-group">
                <label>Product*</label>
                <p-dropdown (onChange)="getPlanLevelMacAddressList($event)" [options]="productByPlanList" filter="true"
                  filterBy="name" formControlName="productId" optionLabel="name" optionValue="id"
                  placeholder="Select Product"></p-dropdown>
                <div *ngIf="
                    planInventoryAssignSumitted && planInventoryAssignForm.controls.productId.errors
                  " class="errorWrap text-danger">
                  <div *ngIf="
                      planInventoryAssignSumitted &&
                      planInventoryAssignForm.controls.productId.errors.required
                    " class="error text-danger">
                    Product is required.
                  </div>
                </div>
              </div>
            </div> -->
          <div *ngIf="itemConditionPlanPairFlag && getPlanPairSplitterFlag">
            <p-splitter [style]="{ height: '100%' }" gutterSize="">
              <ng-template pTemplate>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                  <div class="row" *ngIf="getAllPairPlanProductSTBFlag">
                    <div
                      class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                      *ngIf="getAllPairPlanProductSTBFlag"
                    >
                      <div class="form-group">
                        <label>STB Product*</label>
                        <p-dropdown
                          [options]="productByPlanList"
                          (onChange)="getPlanLevelSTBMacAddressList($event)"
                          filter="true"
                          filterBy="name"
                          formControlName="productId"
                          optionLabel="name"
                          optionValue="id"
                          placeholder="Select Product"
                        ></p-dropdown>
                        <div
                          *ngIf="
                            planInventoryAssignSumitted &&
                            planInventoryAssignForm.controls.productId.errors
                          "
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              planInventoryAssignSumitted &&
                              planInventoryAssignForm.controls.productId.errors.required
                            "
                            class="error text-danger"
                          >
                            Product is required.
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                      *ngIf="macAddressListSTB.length > 0 && getAllPairPlanProductSTBFlag"
                    >
                      <div class="form-group">
                        <p-table
                          #dt
                          [value]="macAddressListSTB"
                          [(selection)]="selectedSTBPlanMACAddress"
                          dataKey="id"
                          styleClass="p-datatable-customers"
                          [rowHover]="true"
                          [rows]="5"
                          [showCurrentPageReport]="true"
                          [rowsPerPageOptions]="[5, 10, 25, 50]"
                          [loading]="loading"
                          [paginator]="true"
                          currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                          [filterDelay]="0"
                          [globalFilterFields]="['serialNumber', 'condition']"
                          responsiveLayout="scroll"
                          [scrollable]="true"
                          scrollHeight="250px"
                        >
                          <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                              <span class="p-input-icon-left">
                                <input
                                  class="form-control"
                                  pInputText
                                  type="text"
                                  [(ngModel)]="stbFileterGlobal"
                                  [ngModelOptions]="{ standalone: true }"
                                  (input)="dt.filterGlobal($event.target.value, 'contains')"
                                  placeholder="Enter Seial Number"
                                />
                              </span>
                              &nbsp;
                              <button
                                type="button"
                                class="btn btn-default"
                                (click)="clearstbFileterGlobal(dt)"
                              >
                                Clear
                              </button>
                            </div>
                          </ng-template>
                          <br />
                          <ng-template pTemplate="header">
                            <tr>
                              <th style="width: 10%"></th>
                              <th>Item Type</th>
                              <th>Serial Number</th>
                              <th>Action</th>
                            </tr>
                          </ng-template>
                          <ng-template let-product pTemplate="body">
                            <tr>
                              <td style="width: 10%">
                                <p-tableRadioButton
                                  [value]="product"
                                  *ngIf="getAllPairPlanProductSTBFlag && product.customerId == null"
                                  (click)="editPlanSTBSerialMapping(product.id)"
                                ></p-tableRadioButton>
                              </td>
                              <td>
                                {{ product.condition }}
                              </td>
                              <td>
                                <input
                                  type="text"
                                  name="serialNumber"
                                  class="form-control"
                                  [value]="product.serialNumber"
                                  [(ngModel)]="product.serialNumber"
                                  [ngModelOptions]="{ standalone: true }"
                                  [disabled]="enterSTBSerial == product.id ? false : true"
                                />
                                <!-- {{ product.serialNumber }} -->
                              </td>
                              <td>
                                <button
                                  (click)="editPlanSTBSerial(product.id)"
                                  [disabled]="
                                    editSTBSerialBtn == product.id && isEditEnable ? false : true
                                  "
                                  class="curson_pointer approve-btn"
                                  title="Edit Mac"
                                >
                                  <img src="assets/img/ioc01.jpg" />
                                </button>
                                <button
                                  id="addAtt"
                                  class="btn btn-primary"
                                  (click)="saveMacidMapping(product.itemId, product)"
                                  style="object-fit: cover; padding: 5px 8px"
                                  icon="pi pi-check"
                                  [disabled]="enterSTBSerial == product.id ? false : true"
                                  [disabled]="editSTBSerialBtn == product.id ? false : true"
                                >
                                  Add
                                </button>
                              </td>
                            </tr>
                          </ng-template>
                        </p-table>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>
              <ng-template pTemplate>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                  <div class="row" *ngIf="getAllPairProductCardFlag">
                    <div
                      class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                      *ngIf="getAllPairProductCardFlag"
                    >
                      <div class="form-group">
                        <label>Card Product*</label>
                        <p-dropdown
                          [options]="allCardProducts"
                          (onChange)="getPlanLevelCardMacAddressList($event)"
                          filter="true"
                          filterBy="name"
                          formControlName="productId"
                          optionLabel="name"
                          optionValue="id"
                          placeholder="Select Product"
                        ></p-dropdown>
                        <div
                          *ngIf="
                            planInventoryAssignSumitted &&
                            planInventoryAssignForm.controls.productId.errors
                          "
                          class="errorWrap text-danger"
                        >
                          <div
                            *ngIf="
                              planInventoryAssignSumitted &&
                              planInventoryAssignForm.controls.productId.errors.required
                            "
                            class="error text-danger"
                          >
                            Product is required.
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
                      *ngIf="macAddressListCard.length > 0 && getAllPairProductCardFlag"
                    >
                      <div class="form-group">
                        <p-table
                          #dt
                          [value]="macAddressListCard"
                          [(selection)]="selectedCardPlanMACAddress"
                          dataKey="id"
                          styleClass="p-datatable-customers"
                          [rowHover]="true"
                          [loading]="loading"
                          [filterDelay]="0"
                          [globalFilterFields]="['serialNumber', 'condition']"
                          responsiveLayout="scroll"
                          [scrollable]="true"
                          scrollHeight="250px"
                        >
                          <ng-template pTemplate="caption">
                            <div class="flex align-items-center justify-content-between">
                              <span class="p-input-icon-left">
                                <input
                                  class="form-control"
                                  pInputText
                                  type="text"
                                  [(ngModel)]="cardFileterGlobal"
                                  [ngModelOptions]="{ standalone: true }"
                                  (input)="dt.filterGlobal($event.target.value, 'contains')"
                                  placeholder="Global Search Filter"
                                />
                              </span>
                              &nbsp;
                              <button
                                type="button"
                                class="btn btn-default"
                                (click)="clearcardFileterGlobal(dt)"
                              >
                                Clear
                              </button>
                            </div>
                          </ng-template>
                          <br />
                          <ng-template pTemplate="header">
                            <tr>
                              <th style="width: 10%"></th>
                              <th>Item Type</th>
                              <th>Serial Number</th>
                              <th>Action</th>
                            </tr>
                          </ng-template>
                          <ng-template let-product pTemplate="body">
                            <tr>
                              <td style="width: 10%">
                                <p-tableRadioButton
                                  [value]="product"
                                  *ngIf="getAllPairProductCardFlag && product.customerId == null"
                                  (click)="editCardSerialMapping(product.id)"
                                ></p-tableRadioButton>
                              </td>
                              <td>
                                {{ product.condition }}
                              </td>
                              <td>
                                <input
                                  type="text"
                                  name="serialNumber"
                                  class="form-control"
                                  [value]="product.serialNumber"
                                  [(ngModel)]="product.serialNumber"
                                  [ngModelOptions]="{ standalone: true }"
                                  [disabled]="enterCardSerial == product.id ? false : true"
                                />
                                <!-- {{ product.serialNumber }} -->
                              </td>
                              <td>
                                <button
                                  (click)="editCardSerial(product.id)"
                                  [disabled]="editCardSerialBtn == product.id ? false : true"
                                  class="curson_pointer approve-btn"
                                  title="Edit Mac"
                                >
                                  <img src="assets/img/ioc01.jpg" />
                                </button>
                                <button
                                  id="addAtt"
                                  class="btn btn-primary"
                                  (click)="saveMacidMapping(product.itemId, product)"
                                  style="object-fit: cover; padding: 5px 8px"
                                  icon="pi pi-check"
                                  [disabled]="enterCardSerial == product.id ? false : true"
                                  [disabled]="editCardSerialBtn == product.id ? false : true"
                                >
                                  Add
                                </button>
                              </td>
                            </tr>
                          </ng-template>
                          <ng-template pTemplate="summary">
                            <p-paginator
                              (onPageChange)="paginateMacAddressCardData($event)"
                              [first]="newFirstMacAddressCard"
                              [rows]="macAddressListCarditemsPerPage"
                              [totalRecords]="macAddressListCardtotalRecords"
                            ></p-paginator>
                          </ng-template>
                        </p-table>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>
            </p-splitter>
          </div>
          <div
            class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
            *ngIf="macPlanListFlag && macAddressList.length > 0"
          >
            <div class="form-group">
              <p-table
                #dt
                [value]="macAddressList"
                [(selection)]="selectedPlanMACAddress"
                dataKey="id"
                styleClass="p-datatable-customers"
                [rowHover]="true"
                [loading]="loading"
                [filterDelay]="0"
                responsiveLayout="scroll"
                [scrollable]="true"
                scrollHeight="300px"
                [globalFilterFields]="['condition', 'itemId', 'serialNumber', 'macAddress']"
              >
                <ng-template pTemplate="caption">
                  <div class="flex align-items-center justify-content-between">
                    <span class="p-input-icon-left">
                      <input
                        class="form-control"
                        pInputText
                        type="text"
                        [(ngModel)]="fileterGlobalPlanlevel"
                        [ngModelOptions]="{ standalone: true }"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="Global Search Filter"
                      />
                    </span>
                    &nbsp;
                    <button
                      type="button"
                      class="btn btn-default"
                      (click)="clearfileterGlobalPlanlevel(dt)"
                    >
                      Clear
                    </button>
                  </div>
                </ng-template>
                <ng-template pTemplate="header">
                  <tr>
                    <th style="width: 10%">
                      <!-- <p-tableRadioButton></p-tableRadioButton> -->
                    </th>
                    <!-- <th *ngIf="planInventoryAssignForm.controls.itemAssemblyName.enabled">
                        Product
                      </th> -->
                    <th>Item Id</th>
                    <th>Item Type</th>
                    <th *ngIf="this.hasMac">MAC Address</th>
                    <th *ngIf="this.hasSerial">Serial Number</th>
                    <th>Action</th>
                  </tr>
                </ng-template>
                <ng-template let-product pTemplate="body" let-rowIndex="rowIndex">
                  <tr>
                    <td style="width: 10%">
                      <p-tableRadioButton
                        *ngIf="product.customerId == null"
                        [value]="product"
                        (click)="editPlanLevelMacMapping(product.id, product)"
                      ></p-tableRadioButton>
                      <!-- <p-tableCheckbox
                          [value]="product"
                          *ngIf="
                            planInventoryAssignForm.controls.itemAssemblyName.enabled &&
                            product.customerId == null
                          "
                        ></p-tableCheckbox> -->
                    </td>
                    <!-- <td
                        *ngIf="
                          planInventoryAssignForm.controls.itemAssemblyName.enabled &&
                          product.customerId == null
                        "
                      >
                        {{ product.productName }}
                      </td> -->
                    <td>
                      <input name="itemId" class="form-control" [value]="product.itemId" disabled />
                    </td>
                    <td>
                      {{ product.condition }}
                    </td>
                    <td *ngIf="this.hasMac">
                      <input
                        type="text"
                        name="macAddress"
                        class="form-control"
                        [value]="product.macAddress"
                        [(ngModel)]="product.macAddress"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="enterPlanLevelMacSerial == product.id ? false : true"
                      />
                    </td>
                    <td *ngIf="this.hasSerial">
                      <input
                        type="text"
                        name="serialNumber"
                        class="form-control"
                        [value]="product.serialNumber"
                        [(ngModel)]="product.serialNumber"
                        [ngModelOptions]="{ standalone: true }"
                        [disabled]="enterPlanLevelMacSerial == product.id ? false : true"
                      />
                    </td>
                    <td>
                      <button
                        (click)="editPlanLevelMac(product.id)"
                        [disabled]="
                          editPlanLevelMacSerialBtn == product.id && isEditEnable ? false : true
                        "
                        class="curson_pointer approve-btn"
                        title="Edit Mac"
                      >
                        <img src="assets/img/ioc01.jpg" />
                      </button>
                      <button
                        id="addAtt"
                        class="btn btn-primary"
                        (click)="saveMacidMapping(product.itemId, product)"
                        style="object-fit: cover; padding: 5px 8px"
                        icon="pi pi-check"
                        [disabled]="
                          enterPlanLevelMacSerial == product.id &&
                          editPlanLevelMacSerialBtn == product.id
                            ? false
                            : true
                        "
                      >
                        Add
                      </button>
                      <button
                        id="addAtt"
                        class="btn btn-primary"
                        (click)="viewSpecificationParameters(product.itemId, product)"
                        style="object-fit: cover; padding: 5px 8px; margin-left: 5px"
                        icon="pi pi-check"
                        [disabled]="
                          enterPlanLevelMacSerial == product.id ||
                          editPlanLevelMacSerialBtn == product.id
                            ? false
                            : true
                        "
                      >
                        View1
                      </button>
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>

          <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" *ngIf="macAddressList.length > 0">
              <div class="form-group">
                <p-table
                  #dt
                  [(selection)]="selectedMACAddress"
                  [globalFilterFields]="['macAddress']"
                  [paginator]="true"
                  [rowHover]="true"
                  [rows]="5"
                  [showCurrentPageReport]="true"
                  [value]="macAddressList"
                  currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                  dataKey="id"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="caption">
                    <div class="flex align-items-center justify-content-between">
                      <h5 class="m-0">MAC Address</h5>
                      <span class="p-input-icon-left">
                        <input
                          (input)="dt.filterGlobal($event.target.value, 'contains')"
                          class="form-control"
                          pInputText
                          placeholder="Search..."
                          type="text"
                        />
                      </span>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 10%">
                       <p-tableRadioButton></p-tableRadioButton> 
                      </th>
                      <th>Item Id</th>
                      <th>MAC Address</th>
                      <th>Serial Number</th>
                    </tr>
                  </ng-template>
                  <ng-template let-product pTemplate="body">
                    <tr>
                      <td style="width: 10%">
                        <p-tableRadioButton
                          *ngIf="product.customerId == null"
                          [value]="product"
                        ></p-tableRadioButton>
                      </td>
                      <td>{{ product.id }}</td>
                      <td>{{ product.macAddress }}</td>
                      <td>
                        {{ product.serialNumber }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div> -->

          <!-- <label *ngIf="!this.productHasMac && !this.productHasSerial" style="font-weight: bold">
              Available Quantity :- {{ this.availableQty }}
            </label>
            <br />
            <br />
            <label>Quantity in {{ this.unit }} *</label>
            <input
              [ngClass]="{
                'is-invalid': submitted && planInventoryAssignForm.controls.qty.errors
              }"
              class="form-control"
              formControlName="qty"
              placeholder="Enter Quantity"
              type="text"
            />
            <div *ngIf="this.showQtyError" class="error text-danger">
              Quanitty must be greater than used quantity.
            </div>
            <div *ngIf="this.showQtySelectionError" class="error text-danger">
              Quanitty must be greater than selected MAC addresses.
            </div> -->
          <div class="row" style="margin-left: auto; margin-right: auto">
            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4" *ngIf="billToPlanFlag">
              <div class="form-group">
                <label>Bill To*</label>
                <input
                  [(ngModel)]="billToPlan"
                  class="form-control"
                  formControlName="billTo"
                  type="text"
                  readonly
                />
              </div>
            </div>

            <!-- <div
                class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
                *ngIf="oldOfferBasedDiscountPlanFlag"
              >
                <div class="form-group">
                  <label>Discount</label>
                  <input
                    [(ngModel)]="selectedCustDiscount"
                    class="form-control"
                    formControlName="discount"
                    type="text"
                    readonly
                  />
                </div>
              </div> -->
            <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
              *ngIf="billToPlanFlag && isInvoiceDataFlag"
            >
              <div class="form-group">
                <label>Invoice To Org</label>
                <p-dropdown
                  (onChange)="selInvoiceToOrgPlan($event)"
                  [options]="isInvoiceData"
                  formControlName="isInvoiceToOrg"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Invoice to org or not"
                  [readonly]="invoiceDataReadOnly"
                ></p-dropdown>
              </div>
            </div>
            <!-- <div
              class="col-lg-4 col-md-4 col-sm-4 col-xs-4"
              *ngIf="billToPlanFlag && isInvoiceDataFlag && requiredApprovalPlanFlag"
            >
              <div class="form-group">
                <label>Required To Approval</label>
                <p-dropdown
                  [options]="isRequiredApprovalData"
                  formControlName="isRequiredApproval"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select Required to approval"
                ></p-dropdown>
              </div>
            </div> -->
          </div>
          <div class="row" style="margin-left: auto; margin-right: auto">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferPricePlanFlag">
              <div class="form-group">
                <label>Old Offer Price</label>
                <input
                  class="form-control"
                  [(ngModel)]="oldOfferPricePlan"
                  type="text"
                  formControlName="offerPrice"
                  readonly
                />
              </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferPricePlanFlag">
              <div class="form-group">
                <label>New Offer Price</label>
                <input
                  class="form-control"
                  [(ngModel)]="newOfferPricePlan"
                  type="text"
                  formControlName="newAmount"
                  placeholder="Enter new offer price"
                  (keypress)="newOfferPriceValidation($event)"
                  [readonly]="newOfferPriceFlag"
                />
                <div class="error text-danger" *ngIf="this.showError">
                  {{ this.priceErrorMsg }}
                </div>
              </div>
            </div>
            <br />
          </div>
          <div class="row" style="margin-left: auto; margin-right: auto">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferBasedDiscountPlanFlag">
              <div class="form-group">
                <label>Old Offer Price</label>
                <input
                  class="form-control"
                  [(ngModel)]="oldOfferPricePlan"
                  type="text"
                  formControlName="offerPrice"
                  readonly
                />
              </div>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12" *ngIf="oldOfferBasedDiscountPlanFlag">
              <div class="form-group">
                <label>New Offer Price</label>
                <input
                  class="form-control"
                  [(ngModel)]="newOfferPricePlan"
                  type="text"
                  formControlName="newAmount"
                  placeholder="Enter new offer price"
                  (keypress)="newOfferPriceValidation($event)"
                  [readonly]="newOfferPriceFlag"
                />
                <div class="error text-danger" *ngIf="this.showError">
                  {{ this.priceErrorMsg }}
                </div>
              </div>
            </div>
            <br />
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
            <label>Billable To</label>
            <br />
            <p-dropdown
              [disabled]="true"
              [options]="billableCusList"
              [showClear]="true"
              filter="true"
              filterBy="name"
              formControlName="billabecustId"
              optionLabel="name"
              optionValue="id"
              placeholder="Select a Billable"
              styleClass="disableDropdown"
            ></p-dropdown>
            <button
              type="button"
              [disabled]="iscustomerEdit"
              (click)="modalOpenParentCustomer('billable')"
              class="btn btn-primary"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
            >
              <i class="fa fa-plus-square"></i>
            </button>
            <button
              [disabled]="iscustomerEdit"
              class="btn btn-danger"
              style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
              (click)="removeSelParentCust('billable')"
            >
              <i class="fa fa-trash"></i>
            </button>
          </div>
          <!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="form-group">
                <label>Status*</label>
                <p-dropdown
                  [options]="inventoryStatus"
                  filter="true"
                  filterBy="label"
                  formControlName="status"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select status"
                ></p-dropdown>
                <div
                  *ngIf="
                    planInventoryAssignSumitted && planInventoryAssignForm.controls.status.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planInventoryAssignSumitted &&
                      planInventoryAssignForm.controls.status.errors.required
                    "
                    class="error text-danger"
                  >
                    Status is required.
                  </div>
                </div>
              </div>
            </div> -->
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
            <div class="form-group">
              <label>Payment Owner*</label>
              <p-dropdown
                [disabled]="true"
                [options]="staffSelectList"
                optionLabel="name"
                optionValue="id"
                filterBy="firstname"
                placeholder="Select a staff"
                [filter]="true"
                formControlName="paymentOwnerId"
                [showClear]="true"
                styleClass="disableDropdown"
              >
                <ng-template let-data pTemplate="item">
                  <div class="item-drop1">
                    <span class="item-value1"> {{ data.name }} </span>
                  </div>
                </ng-template>
              </p-dropdown>

              <button
                type="button"
                (click)="modalOpenSelectStaff('planInventoryAssign')"
                class="btn btn-primary"
                style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
              >
                <i class="fa fa-plus-square"></i>
              </button>
              <button
                type="button"
                (click)="removePlanSelectStaff()"
                class="btn btn-danger"
                style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 10px"
              >
                <i class="fa fa-trash"></i>
              </button>
              <!-- <div *ngIf="planInventoryAssignSumitted &&
                      planInventoryAssignForm.controls.paymentOwnerId.errors.required" class="errorWrap text-danger">
                <div class="error text-danger">Payment owner is required</div>
              </div> -->
              <div
                *ngIf="
                  planInventoryAssignSumitted &&
                  planInventoryAssignForm.controls.paymentOwnerId.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  class="error text-danger"
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.paymentOwnerId.errors.required
                  "
                >
                  Payment owner is required
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
            <div class="form-group">
              <label>Assign Date*</label>
              <div>
                <p-calendar
                  [style]="{ width: '50%' }"
                  [numberOfMonths]="1"
                  [showSeconds]="true"
                  [showTime]="true"
                  formControlName="assignedDateTime"
                  inputId="time"
                ></p-calendar>
                <div
                  *ngIf="
                    planInventoryAssignSumitted &&
                    planInventoryAssignForm.controls.assignedDateTime.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      planInventoryAssignSumitted &&
                      planInventoryAssignForm.controls.assignedDateTime.errors.required
                    "
                    class="error text-danger"
                  >
                    Assign Date is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <div class="addUpdateBtn">
        <button
          (click)="assignPlanInventory()"
          class="btn btn-primary btn-sm"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Assign Inventory By Plan
        </button>
        <button
          (click)="assignPlanInventoryModalClose()"
          class="btn btn-danger btn-sm"
          data-dismiss="modal"
        >
          Close
        </button>
      </div>
    </div>
  </ng-template>
</p-dialog>
<!-- <div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="approveChangeStatusModal"
  role="dialog"
  tabindex="-1"
  data-backdrop="static"
  data-keyboard="false"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button
          aria-label="Close"
          class="close"
          (click)="closeApproveInventoryModal()"
          data-dismiss="modal"
          type="button"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Change Approval Status For Remove Inventory
        </h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="removeRemarkForm">
          <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-4 col-xs-12">
              <label>Remark*:</label>
            </div>
            <div class="col-lg-10 col-md-9 col-sm-8 col-xs-12">
              <textarea class="form-control" formControlName="remark" name="remark"></textarea>
              <div
                class="errorWrap text-danger"
                *ngIf="removeRemarkSubmitted && removeRemarkForm.controls.remark.errors"
              >
                <div
                  class="error text-danger"
                  *ngIf="removeRemarkSubmitted && removeRemarkForm.controls.remark.errors.required"
                >
                  Remark is required.
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button (click)="acceptRemoveItem()" class="btn btn-primary" id="submit" type="submit">
          <i class="fa fa-check-circle"></i>
          Approve
        </button>
        <button
          class="btn btn-default"
          (click)="closeApproveInventoryModal()"
          data-dismiss="modal"
          type="button"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div> -->
<!-- Refund Ammount when Remove Inventory -->
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="refundAmountModal"
  role="dialog"
  tabindex="-1"
  data-backdrop="static"
  data-keyboard="false"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button
          aria-label="Close"
          class="close"
          (click)="closeRefundAmountModal()"
          data-dismiss="modal"
          type="button"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Change Refund Amount
        </h4>
      </div>
      <div class="modal-body">
        <form [formGroup]="refundAmountForm">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <tr>
                <th>
                  <div class="form-group">
                    <label> Actual Refund Amount </label>
                    <!-- <label>
                      {{ this.actualRefundPrice }}
                    </label> -->
                    <input
                      class="form-control"
                      [(ngModel)]="actualProductPrice"
                      type="text"
                      (keypress)="newOfferPriceValidation($event)"
                      formControlName="actualRefundPrice"
                      readonly
                    />
                  </div>
                </th>
                <th>
                  <div class="form-group">
                    <label>Override Refund Amount*</label>
                    <input
                      class="form-control"
                      [(ngModel)]="newProductPrice"
                      type="text"
                      (keydown)="handleKeyDown($event)"
                      formControlName="newRefundAmount"
                      placeholder="Enter new offer price"
                    />
                    <div
                      class="errorWrap text-danger"
                      *ngIf="
                        refundAmountSubmitted && refundAmountForm.controls.newRefundAmount.errors
                      "
                    >
                      <div
                        class="error text-danger"
                        *ngIf="
                          refundAmountSubmitted &&
                          refundAmountForm.controls.newRefundAmount.errors.required
                        "
                      >
                        Remark is required.
                      </div>
                    </div>
                  </div>
                </th>
              </tr>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button (click)="finalRemoveWithRefund()" class="btn btn-primary" id="submit" type="submit">
          <i class="fa fa-check-circle"></i>
          Remove
        </button>
        <button
          class="btn btn-default"
          (click)="closeRefundAmountModal()"
          data-dismiss="modal"
          type="button"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Customer Inventory History"
  [(visible)]="displayCustomerInventoryHistory"
  [style]="{ width: '90%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <p-table
      #dt1
      [value]="this.getAllInventoryofCust"
      dataKey="id"
      styleClass="p-datatable-customers"
      [rowHover]="true"
      [rows]="5"
      [showCurrentPageReport]="true"
      [rowsPerPageOptions]="[5, 10, 25, 50]"
      responsiveLayout="scroll"
      [scrollable]="true"
      scrollHeight="300px"
      [loading]="loading"
      [paginator]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      [filterDelay]="0"
      [globalFilterFields]="[
        'startDate',
        'id',
        'condition',
        'serviceName',
        'connectionNo',
        'macAddress',
        'serialNumber',
        'postPaidPlanName',
        'externalItemGroupNumber',
        'event',
        'approvalRemark'
      ]"
    >
      <ng-template pTemplate="caption">
        <div class="flex align-items-center justify-content-between">
          <span class="p-input-icon-left">
            <input
              class="form-control"
              pInputText
              type="text"
              [(ngModel)]="getAllInventoryofCustFilterGlobal"
              [ngModelOptions]="{ standalone: true }"
              (input)="dt1.filterGlobal($event.target.value, 'contains')"
              placeholder="Global Search Filter"
            />
          </span>
          &nbsp;
          <button
            type="button"
            class="btn btn-default"
            #btnClose
            (click)="cleargetAllInventoryofCustFilterGlobal(dt1)"
          >
            Clear
          </button>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <!-- <th>Item Id</th> -->
          <th>Event Date</th>
          <th>Inventory Type</th>
          <th>Service Name</th>
          <th>Connection No</th>
          <th>MAC Address</th>
          <th>Serial Number</th>
          <th>Plan Name</th>
          <th>External Item Group</th>
          <th>Event Type</th>
          <th>Remark</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-product let-rowIndex="rowIndex">
        <tr>
          <!-- <td>
              <span *ngIf="product.id">
                {{ product.id }}
              </span>
              <span *ngIf="!product.id">-</span>
            </td> -->
          <td>
            <span *ngIf="product.startDate">
              {{ product.startDate | date: "dd-MM-yyyy hh:mm:ss" }}
            </span>
            <span *ngIf="!product.startDate">-</span>
          </td>
          <td>
            <span *ngIf="product.condition">
              {{ product.condition }}
            </span>
            <span *ngIf="!product.condition">-</span>
          </td>
          <td>
            <span *ngIf="product.serviceName">
              {{ product.serviceName }}
            </span>
            <span *ngIf="!product.serviceName">-</span>
          </td>
          <td>
            <span *ngIf="product.connectionNo">
              {{ product.connectionNo }}
            </span>
            <span *ngIf="!product.connectionNo">-</span>
          </td>
          <td>
            <span *ngIf="product.macAddress">
              {{ product.macAddress }}
            </span>
            <span *ngIf="!product.macAddress">-</span>
          </td>
          <td>
            <span *ngIf="product.serialNumber">
              {{ product.serialNumber }}
            </span>
            <span *ngIf="!product.serialNumber">-</span>
          </td>
          <td>
            <span *ngIf="product.postPaidPlanName">
              {{ product.postPaidPlanName }}
            </span>
            <span *ngIf="!product.postPaidPlanName">-</span>
          </td>
          <td>
            <span *ngIf="product.externalItemGroupNumber">
              {{ product.externalItemGroupNumber }}
            </span>
            <span *ngIf="!product.externalItemGroupNumber">-</span>
          </td>
          <td>
            <span *ngIf="product.event">
              {{ product.event }}
            </span>
            <span *ngIf="!product.event">-</span>
          </td>
          <td>
            <span *ngIf="product.approvalRemark">
              {{ product.approvalRemark }}
            </span>
            <span *ngIf="!product.approvalRemark">-</span>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button type="button" class="btn btn-danger btn-sm" data-dismiss="modal" (click)="onclosed()">
        Close
      </button>
    </div>
  </div>
</p-dialog>

<p-dialog
  header="Select Customer"
  [(visible)]="displaySelectParentCustomer"
  [style]="{ width: '60%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <h5>Search Parent Customer</h5>
    <div class="row">
      <div class="col-lg-3 col-md-3 m-b-10">
        <p-dropdown
          (onChange)="selParentSearchOption($event)"
          [(ngModel)]="searchParentCustOption"
          [options]="searchOptionSelect"
          [filter]="true"
          filterBy="label"
          optionLabel="label"
          optionValue="value"
          placeholder="Select a Search Option"
        ></p-dropdown>
      </div>
      <div
        *ngIf="
          parentFieldEnable &&
          searchParentCustOption != 'status' &&
          searchParentCustOption !== 'serviceareaName' &&
          searchParentCustOption !== 'plan'
        "
        class="col-lg-3 col-md-3 m-b-10"
      >
        <input
          [(ngModel)]="searchParentCustValue"
          class="form-control"
          id="username"
          placeholder="Enter Search Detail"
          type="text"
          (keydown.enter)="searchParentCustomer()"
        />
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption === 'status'">
        <p-dropdown
          [options]="commondropdownService.CustomerStatusValue"
          optionValue="value"
          optionLabel="text"
          filter="true"
          filterBy="text"
          placeholder="Select a Status"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>

      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'serviceareaName'">
        <p-dropdown
          [options]="commondropdownService.serviceAreaList"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Servicearea"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div class="col-lg-3 col-md-3 m-b-10" *ngIf="searchParentCustOption == 'plan'">
        <p-dropdown
          [options]="commondropdownService.postpaidplanData"
          optionValue="id"
          optionLabel="name"
          filter="true"
          filterBy="name"
          placeholder="Select a Plan"
          [(ngModel)]="searchParentCustValue"
        ></p-dropdown>
      </div>
      <div *ngIf="parentFieldEnable" class="col-lg-6 col-md-6 col-sm-12">
        <button
          (click)="searchParentCustomer()"
          class="btn btn-primary"
          id="searchbtn"
          type="button"
        >
          <i class="fa fa-search"></i>
          Search
        </button>
        <button
          (click)="clearSearchParentCustomer()"
          class="btn btn-default"
          id="searchbtn"
          type="button"
        >
          <i class="fa fa-refresh"></i>
          Clear
        </button>
      </div>
    </div>
    <h5 style="margin-top: 15px">Select Parent Customer</h5>
    <p-table
      #dt
      [(selection)]="selectedParentCust"
      [value]="prepaidParentCustomerList"
      responsiveLayout="scroll"
    >
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 5rem"></th>
          <th>Name</th>
          <th>User Name</th>
        </tr>
      </ng-template>
      <ng-template let-prepaidParentCustomerList let-rowIndex="rowIndex" pTemplate="body">
        <tr>
          <td>
            <p-tableRadioButton [value]="prepaidParentCustomerList"></p-tableRadioButton>
          </td>
          <td>{{ prepaidParentCustomerList.name }}</td>
          <td>{{ prepaidParentCustomerList.username }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <div
          class="pagination_Dropdown"
          style="display: flex; align-items: center; justify-content: center"
        >
          <p-paginator
            [rowsPerPageOptions]="pageOptions"
            (onPageChange)="paginate($event)"
            [first]="newFirst"
            [rows]="parentCustomerListdataitemsPerPage"
            [totalRecords]="parentCustomerListdatatotalRecords"
          ></p-paginator>
        </div>
      </ng-template>
    </p-table>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="saveSelCustomer()"
        [disabled]="this.selectedParentCust.length == 0"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="modalCloseParentCustomer()" class="btn btn-danger btn-sm" type="button">
        Close
      </button>
    </div>
  </div>
</p-dialog>
<!-- Approve Assign Iventory Modal Open -->
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="assignApproveOtherInventoryOpen"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Approve Assign Inventory
        </h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="approveAssignInventoryForm">
          <div class="row">
            <div *ngIf="approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="row">
                <div class="col-md-6">
                  <input
                    id="searchStaffName"
                    type="text"
                    name="username"
                    class="form-control"
                    placeholder="Global Search Filter"
                    [(ngModel)]="searchStaffDeatil"
                    (keydown.enter)="searchStaffByName()"
                    [ngModelOptions]="{ standalone: true }"
                  />
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12">
                  <button
                    (click)="searchStaffByName()"
                    class="btn btn-primary"
                    id="searchbtn"
                    type="submit"
                    [disabled]="!searchStaffDeatil"
                  >
                    <i class="fa fa-search"></i>
                    Search
                  </button>
                  <button
                    (click)="clearSearchForm()"
                    class="btn btn-default"
                    id="searchbtn"
                    type="reset"
                  >
                    <i class="fa fa-refresh"></i>
                    Clear
                  </button>
                </div>
              </div>

              <div class="card">
                <h5>Select Staff</h5>
                <p-table
                  [(selection)]="selectAssignInventoryApproveStaff"
                  [value]="approveAssignInventoryData"
                  responsiveLayout="scroll"
                  [paginator]="true"
                  [rows]="5"
                  [rowsPerPageOptions]="[5, 10, 15, 20]"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th>Name</th>
                      <th>Username</th>
                    </tr>
                  </ng-template>
                  <ng-template let-product pTemplate="body">
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div *ngIf="!approved" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label>Remark*</label>
              <textarea
                [ngClass]="{
                  'is-invalid':
                    assignAssignInventorysubmitted &&
                    approveAssignInventoryForm.controls.remark.errors
                }"
                class="form-control"
                formControlName="remark"
                name="remark"
              ></textarea>
              <div
                *ngIf="
                  assignAssignInventorysubmitted &&
                  approveAssignInventoryForm.controls.remark.errors
                "
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    assignAssignInventorysubmitted &&
                    approveAssignInventoryForm.controls.remark.errors.required
                  "
                  class="error text-danger"
                >
                  Remark is required.
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="approveInventory()"
          *ngIf="!approved"
          class="btn btn-primary"
          id="submit"
          type="submit"
          [disabled]="!approveAssignInventoryForm.valid"
        >
          <i class="fa fa-check-circle"></i>
          Approve
        </button>
        <button
          (click)="assignToStaff(true)"
          *ngIf="approved"
          class="btn btn-primary"
          id="submitButtonForApprove"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button
          class="btn btn-default"
          data-dismiss="modal"
          type="button"
          (click)="clearapproveInventory()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>
<!-- Reject Assign Iventory Modal Open -->
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="assignRejectOtherInventoryOpen"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Reject Assign Inventory
        </h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="rejectAssignInventoryForm">
          <div class="row">
            <div class="row">
              <div *ngIf="reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card">
                  <h5>Select Staff</h5>
                  <p-table
                    [(selection)]="selectAssignInventoryRejectStaff"
                    [value]="rejectAssignInventoryData"
                    responsiveLayout="scroll"
                  >
                    <ng-template pTemplate="header">
                      <tr>
                        <th style="width: 3rem"></th>
                        <th>Name</th>
                        <th>Username</th>
                      </tr>
                    </ng-template>
                    <ng-template let-product pTemplate="body">
                      <tr>
                        <td>
                          <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                        </td>
                        <td>{{ product.fullName }}</td>
                        <td>
                          {{ product.username }}
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
              </div>
              <div *ngIf="!reject" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <label>Remark*</label>
                <textarea
                  [ngClass]="{
                    'is-invalid':
                      rejectAssignInventorySubmitted &&
                      rejectAssignInventoryForm.controls.remark.errors
                  }"
                  class="form-control"
                  formControlName="remark"
                  name="remark"
                ></textarea>
                <div
                  *ngIf="
                    rejectAssignInventorySubmitted &&
                    rejectAssignInventoryForm.controls.remark.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      rejectAssignInventorySubmitted &&
                      rejectAssignInventoryForm.controls.remark.errors.required
                    "
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="rejectInventory()"
          *ngIf="!reject"
          class="btn btn-primary"
          id="submit"
          type="submit"
          [disabled]="!rejectAssignInventoryForm.valid"
        >
          <i class="fa fa-times-circle"></i>
          Reject
        </button>
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject && !selectAssignInventoryRejectStaff"
          class="btn btn-primary"
          disabled
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          (click)="assignToStaff(false)"
          *ngIf="reject && selectAssignInventoryRejectStaff"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          class="btn btn-default"
          data-dismiss="modal"
          type="button"
          (click)="clearassignToStaff()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="approvalReplaceInventory"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4
          class="modal-title"
          id="myModalLabelapprovalReplaceInventory"
          style="color: #fff !important"
        >
          Assign Staff
        </h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="replaceAssignForm">
          <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <div class="card">
                <h5>Select Staff</h5>
                <p-table
                  [(selection)]="seletedStaffReplace"
                  [value]="rejectPlanData"
                  responsiveLayout="scroll"
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th style="width: 3rem"></th>
                      <th>Name</th>
                      <th>Username</th>
                    </tr>
                  </ng-template>
                  <ng-template let-product pTemplate="body">
                    <tr>
                      <td>
                        <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                      </td>
                      <td>{{ product.fullName }}</td>
                      <td>
                        {{ product.username }}
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
              <label>Remark*</label>
              <textarea
                [ngClass]="{
                  'is-invalid':
                    assignReplaceInventorySubmitted && replaceAssignForm.controls.remark.errors
                }"
                class="form-control"
                formControlName="remark"
                name="remark"
              ></textarea>
              <div
                *ngIf="assignReplaceInventorySubmitted && replaceAssignForm.controls.remark.errors"
                class="errorWrap text-danger"
              >
                <div
                  *ngIf="
                    assignReplaceInventorySubmitted &&
                    replaceAssignForm.controls.remark.errors.required
                  "
                  class="error text-danger"
                >
                  Remark is required.
                </div>
              </div>
            </div>
            <br />
          </div>
          <!-- <input type="file" formControlName="fileName" name="fileName"> -->
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="assignToStaffReplace()"
          class="btn btn-primary"
          [disabled]="this.replaceAssignForm.invalid"
          id="Assign"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>

        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Approve Remove Iventory Modal Open -->
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="approveRemoveInventoryOpenModel"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Approve Remove Inventory
        </h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="approveRemoveInventoryForm">
          <div class="row">
            <div class="row">
              <div *ngIf="approveRemove" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card">
                  <h5>Select Staff</h5>
                  <p-table
                    [(selection)]="selectRemoveInventoryApproveStaff"
                    [value]="approveRemoveInventoryData"
                    responsiveLayout="scroll"
                  >
                    <ng-template pTemplate="header">
                      <tr>
                        <th style="width: 3rem"></th>
                        <th>Name</th>
                        <th>Username</th>
                      </tr>
                    </ng-template>
                    <ng-template let-product pTemplate="body">
                      <tr>
                        <td>
                          <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                        </td>
                        <td>{{ product.fullName }}</td>
                        <td>
                          {{ product.username }}
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
              </div>
              <div *ngIf="!approveRemove" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <label>Remark*</label>
                <textarea
                  [ngClass]="{
                    'is-invalid':
                      assignRemoveInventorysubmitted &&
                      approveRemoveInventoryForm.controls.remark.errors
                  }"
                  class="form-control"
                  formControlName="remark"
                  name="remark"
                ></textarea>
                <div
                  *ngIf="
                    assignRemoveInventorysubmitted &&
                    approveRemoveInventoryForm.controls.remark.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      assignRemoveInventorysubmitted &&
                      approveRemoveInventoryForm.controls.remark.errors.required
                    "
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="approveRemoveInventory()"
          *ngIf="!approveRemove"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Approve
        </button>
        <button
          (click)="assignRemoveInventoryToStaff(true)"
          *ngIf="approveRemove"
          class="btn btn-primary"
          id="submitButtonForApprove"
          type="submit"
        >
          <i class="fa fa-check-circle"></i>
          Assign
        </button>
        <button
          class="btn btn-default"
          data-dismiss="modal"
          type="button"
          (click)="clearapproveremoveInventory()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Reject Remove Iventory Modal Open -->
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="rejectRemoveInventoryOpenModel"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">
          Reject Remove Inventory
        </h4>
      </div>
      <div class="modal-body" style="margin: 10px">
        <form [formGroup]="rejectRemoveInventoryForm">
          <div class="row">
            <div class="row">
              <div *ngIf="rejectRemove" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="card">
                  <h5>Select Staff</h5>
                  <p-table
                    [(selection)]="selectRemoveInventoryRejectStaff"
                    [value]="rejectRemoveInventoryData"
                    responsiveLayout="scroll"
                  >
                    <ng-template pTemplate="header">
                      <tr>
                        <th style="width: 3rem"></th>
                        <th>Name</th>
                        <th>Username</th>
                      </tr>
                    </ng-template>
                    <ng-template let-product pTemplate="body">
                      <tr>
                        <td>
                          <p-tableRadioButton [value]="product.id"></p-tableRadioButton>
                        </td>
                        <td>{{ product.fullName }}</td>
                        <td>
                          {{ product.username }}
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>
                </div>
              </div>
              <div *ngIf="!rejectRemove" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <label>Remark*</label>
                <textarea
                  [ngClass]="{
                    'is-invalid':
                      rejectRemoveInventorySubmitted &&
                      rejectRemoveInventoryForm.controls.remark.errors
                  }"
                  class="form-control"
                  formControlName="remark"
                  name="remark"
                ></textarea>
                <div
                  *ngIf="
                    rejectRemoveInventorySubmitted &&
                    rejectRemoveInventoryForm.controls.remark.errors
                  "
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      rejectRemoveInventorySubmitted &&
                      rejectRemoveInventoryForm.controls.remark.errors.required
                    "
                    class="error text-danger"
                  >
                    Remark is required.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button
          (click)="rejectRemoveInventory()"
          *ngIf="!rejectRemove"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Reject
        </button>
        <button
          (click)="assignRemoveInventoryToStaff(false)"
          *ngIf="rejectRemove && !selectRemoveInventoryRejectStaff"
          class="btn btn-primary"
          disabled
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          (click)="assignRemoveInventoryToStaff(false)"
          *ngIf="rejectRemove && selectRemoveInventoryRejectStaff"
          class="btn btn-primary"
          id="submit"
          type="submit"
        >
          <i class="fa fa-times-circle"></i>
          Assign
        </button>
        <button
          class="btn btn-default"
          data-dismiss="modal"
          type="button"
          (click)="clearassignRemoveToStaff()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<app-staff-select-model
  *ngIf="showSelectStaffModel"
  (selectedStaffChange)="selectedStaffChange($event)"
  (closeSelectStaff)="closeSelectStaff()"
></app-staff-select-model>

<app-customer-inventory-specification-params
  *ngIf="inventorySpecificationParamModal"
  [productData]="productData"
  (closeInventorySpecModel)="closeInventorySpecModel()"
></app-customer-inventory-specification-params>

<p-dialog
  header="Inventory Detail"
  [(visible)]="inventorySpecificationDetailModal"
  [style]="{ width: '75%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div id="inwardDetail" class="panel-collapse collapse in">
    <div class="panel-body table-responsive">
      <fieldset style="margin-top: 0rem; margin-bottom: 2rem" *ngIf="specDetailsShow">
        <legend>Inventory Specification Parameter Details</legend>
        <div class="boxWhite">
          <div class="row table-responsive">
            <div class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th id="parameter">Parameter</th>
                    <th id="mandatory">Mandatory</th>
                    <th id="value">Value</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let param of inventorySpecificationDetails
                        | paginate
                          : {
                              id: 'productDetailPageData',
                              itemsPerPage: productDeatilItemPerPage,
                              currentPage: productPageChargeDeatilList,
                              totalItems: productDeatiltotalRecords
                            };
                      let i = index
                    "
                  >
                    <td>{{ param.paramName }}</td>
                    <td>{{ param.isMandatory }}</td>
                    <td *ngIf="!param.isMultiValueParam">
                      <input
                        type="text"
                        name="value"
                        class="form-control"
                        [(ngModel)]="param.paramValue"
                        [disabled]="!isEditing(i)"
                      />
                    </td>
                    <td *ngIf="param.isMultiValueParam">
                      <p-dropdown
                        [options]="param.multiValue"
                        [(ngModel)]="param.paramValue"
                        optionLabel="label"
                        optionValue="value"
                        filter="true"
                        filterBy="label"
                        placeholder="Select Parameter"
                        [disabled]="!isEditing(i)"
                      >
                      </p-dropdown>
                    </td>
                    <td *ngIf="!isEditing(i)">
                      <div class="btnAction">
                        <button
                          type="button"
                          class="approve-btn"
                          title="Edit"
                          (click)="editValue(i)"
                        >
                          <img src="assets/img/ioc01.jpg" />
                        </button>
                      </div>
                    </td>
                    <td *ngIf="isEditing(i)">
                      <button
                        type="submit"
                        id="delete-button"
                        class="btn btn-primary btn-sm"
                        (click)="addOrEditValue(i, param.id, param.paramValue, param)"
                      >
                        Save
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </fieldset>
    </div>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        type="button"
        (click)="closeInventorySpecificationDetailModal()"
        class="btn btn-danger btn-sm"
        data-dismiss="modal"
      >
        Close
      </button>
    </div>
  </div>
</p-dialog>

<!-- <p-dialog
  header="Upload Document"
  [(visible)]="uploadDocumentId"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body">
    <div [formGroup]="uploadDocForm" class="row">
      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <label>Select files to upload *</label>
        <input
          (change)="onFileChangeUpload($event)"
          class="form-control"
          formControlName="file"
          id="txtSelectDocument"
          multiple="multiple"
          placeholder="Select Attachment"
          style="padding: 2px; width: 100%"
          type="file"
        />
        <div *ngFor="let file of selectedFileUploadPreview; let i = index">
          <div
            style="
              padding-left: 10px;
              padding-right: 10px;
              padding-top: 4px;
              padding-bottom: 4px;
              font-size: 10px;
            "
          >
            {{ file?.name }}
            <button type="button" class="close" (click)="deletUploadedFile(file?.name)">
              &times;
            </button>
          </div>
        </div>
        <div *ngIf="submitted && uploadDocForm.controls.file.errors" class="errorWrap text-danger">
          <div
            *ngIf="submitted && uploadDocForm.controls.file.errors.required"
            class="error text-danger"
          >
            File is required.
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button (click)="uploadDocuments()" class="btn btn-primary" id="submit" type="submit">
        <i class="fa fa-check-circle"></i>
        Upload
      </button>
      <button type="button" class="btn btn-danger btn-sm" (click)="closeUploadDocumentId()">
        Close
      </button>
    </div>
  </div>
</p-dialog> -->
<p-dialog
  header="Upload Document"
  class="second-tabView"
  [(visible)]="uploadDocumentId"
  [style]="{ width: '70%' }"
  [modal]="true"
  [draggable]="false"
  [closable]="true"
  (onHide)="closeUploadDocumentId()"
>
  <p-tabView [(activeIndex)]="activeTabIndex" (onChange)="onTabChange($event)">
    <p-tabPanel *ngFor="let tab of tabs; let tabIndex = index" [header]="tab">
      <ng-container *ngTemplateOutlet="uploadForm; context: { tabIndex: tabIndex }"></ng-container>
    </p-tabPanel>
  </p-tabView>

  <ng-template #uploadForm let-tabIndex="tabIndex">
    <div class="modal-body" [formGroup]="uploadDocForm[tabIndex]">
      <div class="row g-3 gy-3">
        <!-- First Row -->
        <div class="col-12 col-md-3">
          <label>Section Name</label>
          <input
            formControlName="sectionName"
            class="form-control"
            placeholder="Enter Section Name"
            [value]="tabs[tabIndex]"
            disabled
          />
        </div>
        <div *ngIf="tabs[tabIndex] !== 'Optical Power Range'" class="col-12 col-md-3">
          <label>Latitude</label>
          <input formControlName="latitude" class="form-control" placeholder="Enter Latitude" />
        </div>
        <div *ngIf="tabs[tabIndex] !== 'Optical Power Range'" class="col-12 col-md-3">
          <label>Longitude</label>
          <input formControlName="longitude" class="form-control" placeholder="Enter Longitude" />
        </div>
        <div
          *ngIf="tabs[tabIndex] !== 'Optical Power Range'"
          class="col-12 col-md-3 d-flex align-items-center"
        >
          <span
            class="HoverEffect"
            (click)="mylocation()"
            title="Get Current Location"
            style="border-bottom: 1px solid #f7b206; cursor: pointer"
          >
            <img
              class="LocationIcon LocationIconMargin"
              src="assets/img/B_Find-My-current-location_Y.png"
            />
          </span>
        </div>
        <div
          *ngIf="tabs[tabIndex] === 'Optical Power Range'"
          class="col-12 col-md-3 d-flex align-items-center"
        >
          <label>Optical Power range(In db) *</label>
          <p-dropdown
            id="title"
            [options]="opticalRangeData"
            filter="true"
            filterBy="label"
            formControlName="opticalRange"
            optionLabel="label"
            optionValue="value"
            placeholder="Select an Optical Power Range"
            appendTo="body"
          ></p-dropdown>

          <!-- <input formControlName="opticalRange" class="form-control" placeholder="Enter Optical Range" /> -->
        </div>
      </div>

      <!-- Add spacing between rows -->
      <br />
      <!-- Second Row -->
      <div class="row g-3 gy-3">
        <div class="col-12 col-md-6" *ngIf="tabs[tabIndex] !== 'Optical Power Range'">
          <div *ngIf="tabsMandatory && tabsMandatory.length > 0">
            <label>
              Select files to upload
              <span *ngIf="tabsMandatory[tabIndex]" class="text-danger">*</span>
            </label>
          </div>
          <input
            (change)="onFileChangeUpload($event, tabIndex)"
            class="form-control"
            [id]="'txtSelectDocument_' + tabIndex"
            multiple
            placeholder="Select Attachment"
            style="padding: 2px"
            type="file"
          />

          <div
            *ngFor="let file of selectedFileUploadPreview[tabIndex]; let i = index"
            class="mt-2 d-flex justify-content-between align-items-center bg-light p-2"
          >
            <span style="font-size: 12px">{{ file?.name }}</span>
            <button
              type="button"
              class="btn-close"
              (click)="deletUploadedFile(file?.name, tabIndex)"
            ></button>
          </div>

          <div *ngIf="tabsMandatory && tabsMandatory.length > 0">
            <div
              *ngIf="
                tabsMandatory[tabIndex] &&
                submitted &&
                uploadDocForm[tabIndex].controls.file?.errors?.required
              "
              class="errorWrap text-danger"
            >
              <div
                *ngIf="uploadDocForm[tabIndex].controls.file.errors.required"
                class="error text-danger"
              >
                File is required.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>

  <div class="modal-footer d-flex justify-content-between">
    <button (click)="uploadAllDocuments()" class="btn btn-primary" id="submit" type="button">
      <i class="fa fa-check-circle"></i> Upload All Documents
    </button>
    <button type="button" class="btn btn-danger" (click)="closeUploadDocumentId()">Close</button>
  </div>
</p-dialog>

<!-- <p-dialog header="View Document" [(visible)]="downloadDocumentId" [style]="{ width: '70%' }" [modal]="true"
    [responsive]="true" [draggable]="false" [closable]="false">
    <div class="modal-body">
        <p-table [value]="inventoryFileData?.fileDetails" [paginator]="true" [rows]="5"
            [rowsPerPageOptions]="[5, 10, 20]" responsiveLayout="scroll">
            <ng-template pTemplate="header">
                <tr>
                    <th>File Name</th>
                    <th>Action</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-filedata>
                <tr>
                    <td>{{ filedata?.filename }}</td>
                    <td>
                        <button *ngIf="downloadDocumentAccess"
                            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 5px"
                            id="download-button"
                            (click)="downloadDoc(filedata.filename, inventoryFileData.id, filedata.uniquename)"
                            [disabled]="!filedata.filename" title="Download Document" class="btn btn-primary">
                            <img style="width: 25px; height: 25px; border-radius: 3px" src="assets/img/pdf.png" />
                        </button>
                        <button *ngIf="viewDocumentAccess"
                            style="border-radius: 5px; padding: 5px 10px; line-height: 1.5; margin-left: 5px"
                            id="view-button" [disabled]="!filedata.filename" title="View Document"
                            class="btn btn-primary" (click)="
                                showticketDocData(filedata.filename, inventoryFileData.id, filedata.uniquename)
                            ">
                            <img style="width: 25px; height: 25px; border-radius: 3px" src="assets/img/eye-icon.png" />
                        </button>
                        <button *ngIf="deleteDocumentAccess"
                            style="border-radius: 5px; padding: 3px 8px; line-height: 1.5; margin-left: 5px"
                            class="btn btn-primary" [disabled]="!filedata.filename" title="Remove File"
                            id="delete-button"
                            (click)="deleteDoc(filedata.filename, inventoryFileData.id, filedata.uniquename)">
                            <img src="assets/img/ioc02.jpg" />
                        </button>
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="modal-footer">
            <button type="button" class="btn btn-danger btn-sm" (click)="closeDownloadDocumentId()">
                Close
            </button>
        </div>
    </div>
</p-dialog> -->

<p-dialog
  header="View Document"
  [(visible)]="downloadDocumentId"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <button class="close-button" (click)="closeDownloadDocumentId()">&times;</button>
  <p-tabView [(activeIndex)]="activeTabViewIndex">
    <p-tabPanel *ngFor="let tab of tabs" [header]="tab">
      <ng-container *ngIf="hasFilesForTab(tab)">
        <ng-container *ngFor="let section of inventoryFileDocData">
          <ng-container *ngIf="section?.sectionName === tab">
            <ng-container *ngIf="section?.sectionName === 'Optical Power Range'; else showTable">
              <p>Optical Power Range: {{ section?.fileDetails[0].opticalRange }}</p>
            </ng-container>

            <ng-template #showTable>
              <p-table
                *ngIf="section?.fileDetails?.length > 0"
                [value]="section?.fileDetails"
                [paginator]="true"
                [rows]="5"
                [rowsPerPageOptions]="[5, 10, 20]"
                responsiveLayout="scroll"
              >
                <ng-template pTemplate="header">
                  <tr>
                    <th>File Name</th>
                    <th>Latitude</th>
                    <th>Longitude</th>
                    <th>Action</th>
                  </tr>
                </ng-template>

                <ng-template pTemplate="body" let-file>
                  <tr>
                    <td>{{ file.fileName }}</td>
                    <td>{{ file.latitude }}</td>
                    <td>{{ file.longitude }}</td>
                    <td>
                      <button
                        *ngIf="downloadDocumentAccess"
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        id="download-button"
                        (click)="downloadDoc(file.fileName, file, section?.sectionName)"
                        title="Download Document"
                        class="btn btn-primary"
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/pdf.png"
                        />
                      </button>

                      <button
                        *ngIf="viewDocumentAccess"
                        style="
                          border-radius: 5px;
                          padding: 5px 10px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        id="view-button"
                        title="View Document"
                        class="btn btn-primary"
                        (click)="showticketDocData(file.fileName, file, section?.sectionName)"
                      >
                        <img
                          style="width: 25px; height: 25px; border-radius: 3px"
                          src="assets/img/eye-icon.png"
                        />
                      </button>

                      <button
                        *ngIf="deleteDocumentAccess"
                        style="
                          border-radius: 5px;
                          padding: 3px 8px;
                          line-height: 1.5;
                          margin-left: 5px;
                        "
                        class="btn btn-primary"
                        title="Remove File"
                        id="delete-button"
                        (click)="deleteConfirm(file.fileName, file, section?.sectionName)"
                      >
                        <img src="assets/img/ioc02.jpg" />
                      </button>
                    </td>
                  </tr>
                </ng-template>
              </p-table>

              <div *ngIf="section?.fileDetails?.length === 0">
                <p>No files available for this section.</p>
              </div>
            </ng-template>
          </ng-container>
        </ng-container>
      </ng-container>
      <div *ngIf="!hasFilesForTab(tab)">
        <p>No files available for this tab.</p>
      </div>
    </p-tabPanel>
    <div class="modal-footer">
      <div class="closeBtn">
        <button
          class="btn btn-default"
          (click)="closeDownloadDocumentId()"
          data-dismiss="modal"
          type="button"
        >
          Close
        </button>
      </div>
    </div>
  </p-tabView>
</p-dialog>
<p-dialog
  header="Document Preview"
  [(visible)]="documentPreview"
  [style]="{ width: '40%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="false"
>
  <div class="modal-body" style="height: 600px">
    <iframe [src]="previewUrl" width="100%" height="100%" allowfullscreen></iframe>
  </div>
  <div class="modal-footer">
    <button
      class="btn btn-default"
      (click)="closeDocumentPreview()"
      data-dismiss="modal"
      type="button"
    >
      Close
    </button>
  </div>
</p-dialog>
<p-dialog
  header="Wifi Config"
  [(visible)]="wifiModel"
  [style]="{ width: '70%' }"
  [modal]="true"
  [responsive]="true"
  [draggable]="false"
  [closable]="true"
>
  <div class="modal-body">
    <form [formGroup]="wifiForm">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>SSID Username</label>
            <input
              type="text"
              class="form-control"
              formControlName="username"
              placeholder="Enter User Name"
              [readonly]="editWifi"
            />
            <div
              *ngIf="wifiSubmitted && wifiForm.controls.username.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="wifiSubmitted && wifiForm.controls.username.errors.required"
                class="error text-danger"
              >
                User Name is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>SSID Password</label>
            <input
              type="text"
              class="form-control"
              formControlName="password"
              placeholder="Enter Password"
              [readonly]="editWifi"
            />
            <div
              *ngIf="wifiSubmitted && wifiForm.controls.password.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="wifiSubmitted && wifiForm.controls.password.errors.required"
                class="error text-danger"
              >
                Password is required.
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
          <div class="form-group">
            <label>Working Frequency</label>
            <p-dropdown
              id="title"
              [options]="workingFrequencyType"
              filter="true"
              filterBy="label"
              formControlName="workingFrequency"
              optionLabel="label"
              optionValue="value"
              placeholder="Select a Working Frequency"
              [readonly]="editWifi"
            ></p-dropdown>
            <div
              *ngIf="wifiSubmitted && wifiForm.controls.workingFrequency.errors"
              class="errorWrap text-danger"
            >
              <div
                *ngIf="wifiSubmitted && wifiForm.controls.workingFrequency.errors.required"
                class="error text-danger"
              >
                Working Frequency is required.
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <div class="addUpdateBtn">
      <button
        (click)="editWifiModel()"
        class="btn btn-success btn-sm"
        type="button"
        [disabled]="!editWifi"
      >
        Edit
      </button>
      <button
        (click)="saveWifi()"
        class="btn btn-primary"
        style="object-fit: cover; padding: 5px 8px"
      >
        <i class="fa fa-check-circle"></i>
        Save
      </button>
      <button (click)="closeWifiModal()" class="btn btn-danger btn-sm" type="button">Close</button>
    </div>
  </div>
</p-dialog>
