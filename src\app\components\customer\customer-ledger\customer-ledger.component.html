<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ customerLedgerDetailData?.title }}
            {{ customerLedgerDetailData?.firstname }}
            {{ customerLedgerDetailData?.lastname }}
            Search Ledger
          </h3>
        </div>
        <div class="right">
          <button
            aria-controls="searchPreCustLedger"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#searchPreCustLedger"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>
      <div class="panel-collapse collapse in" id="searchPreCustLedger">
        <div class="panel-body table-responsive">
          <form [formGroup]="custLedgerForm" name="custLedgerForm">
            <div class="row">
              <div class="col-lg-3 col-md-3">
                <input
                  [ngClass]="{
                    'is-invalid':
                      custLedgerSubmitted && custLedgerForm.controls.startDateCustLedger.errors
                  }"
                  class="form-control"
                  formControlName="startDateCustLedger"
                  placeholder="From Ledger Date"
                  type="date"
                />
                <div
                  *ngIf="custLedgerSubmitted && custLedgerForm.controls.startDateCustLedger.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      custLedgerSubmitted &&
                      custLedgerForm.controls.startDateCustLedger.errors.required
                    "
                    class="error text-danger"
                  >
                    From Date is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3">
                <input
                  [ngClass]="{
                    'is-invalid':
                      custLedgerSubmitted && custLedgerForm.controls.endDateCustLedger.errors
                  }"
                  class="form-control"
                  formControlName="endDateCustLedger"
                  placeholder="To Ledger Date"
                  type="date"
                />
                <div
                  *ngIf="custLedgerSubmitted && custLedgerForm.controls.endDateCustLedger.errors"
                  class="errorWrap text-danger"
                >
                  <div
                    *ngIf="
                      custLedgerSubmitted &&
                      custLedgerForm.controls.endDateCustLedger.errors.required
                    "
                    class="error text-danger"
                  >
                    To Date is required.
                  </div>
                </div>
              </div>
              <div class="col-lg-3 col-md-3">
                <button
                  (click)="searchCustomerLedger()"
                  class="btn btn-primary"
                  id="searchbtn"
                  type="button"
                >
                  <i class="fa fa-search"></i>
                  Search
                </button>
                <button
                  (click)="clearSearchCustomerLedger()"
                  class="btn btn-default"
                  id="searchbtn"
                  type="reset"
                >
                  <i class="fa fa-refresh"></i>
                  Clear
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <h3 class="panel-title">Customer Ledger Details</h3>
        <div class="right">
          <button
            aria-controls="ledgerdetailsCust"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#ledgerdetailsCust"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="ledgerdetailsCust">
        <div class="panel-body table-responsive">
          <div class="row">
            <div class="col-lg-3 col-md-3 dataGroup">
              <label class="datalbl">Name :</label>
              <span>
                {{ customerLedgerDetailData?.title }}
                {{ customerLedgerDetailData?.firstname }}
                {{ customerLedgerDetailData?.lastname }}
              </span>
            </div>
            <div class="col-lg-3 col-md-3 dataGroup">
              <label class="datalbl">Status :</label>
              <span *ngIf="customerLedgerData.status == 'Active'" class="badge badge-success">
                {{ customerLedgerData.status }}
              </span>
              <span *ngIf="customerLedgerData.status == 'NewActivation'" class="badge badge-info">
                {{ customerLedgerData.status }}
              </span>
              <span *ngIf="customerLedgerData.status == 'Terminate'" class="badge badge-info">
                {{ customerLedgerData.status }}
              </span>
            </div>
            <div class="col-lg-3 col-md-3 dataGroup">
              <label class="datalbl">AAA Username :</label>
              <span>{{ customerLedgerData.username }}</span>
            </div>
          </div>

          <div class="row">
            <div class="col-lg-12 col-md-12 dataGroup">
              <label class="datalbl">Adress :</label>
              <span>{{ customerLedgerData.address }}</span>
            </div>
          </div>

          <div class="row">
            <div class="col-lg-12 col-md-12">
              <table class="table myclass">
                <thead>
                  <tr>
                    <th class="myclass">Create Date</th>
                    <th class="myclass">Receipt No./Credit No.</th>
                    <th class="myclass">Invoice No.</th>
                    <th class="myclass">Category</th>
                    <th class="myclass">Debit</th>
                    <th class="myclass">Credit</th>
                    <th class="myclass">Bal Amount</th>
                    <th class="myclass" style="width: 24%">Remarks</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let data of customerLedgerListData
                        | paginate
                          : {
                              id: 'customerLedgerpageData',
                              itemsPerPage: custLedgerItemPerPage,
                              currentPage: currentPagecustLedgerList,
                              totalItems: custLedgertotalRecords
                            };
                      index as i
                    "
                  >
                    <td class="myclass">{{ data.create_DATE }}</td>
                    <td class="myclass">{{ data.paymentRefNo }}</td>
                    <td class="myclass">
                      <span *ngFor="let invoice of data.invoiceNo">{{ invoice }} </span><br />
                    </td>

                    <td class="myclass">{{ data.transcategory }}</td>
                    <td class="myclass" *ngIf="data.transtype == 'DR'">
                      <!-- {{ currency }} {{ data.amount | number: '1.2-2' }} -->
                      {{ data.amount | currency: currency : "symbol" : "1.2-2" }}
                    </td>

                    <td class="myclass">-</td>

                    <td class="myclass" *ngIf="data.transtype == 'CR'">
                      <!-- {{ currency }} {{ data.amount | number: '1.2-2' }} -->
                      {{ data.amount | currency: currency : "symbol" : "1.2-2" }}
                    </td>

                    <td class="myclass">
                      <!-- {{ currency }} {{ data.balAmount | number: '1.2-2' }} -->
                      {{ data.balAmount | currency: currency : "symbol" : "1.2-2" }}
                    </td>
                    <td class="myclass" style="width: 24%">{{ data.remarks }}</td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  (pageChange)="pageChangedcustledgerList($event)"
                  directionLinks="true"
                  id="customerLedgerpageData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="TotalLedgerItemPerPage($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-12 col-sm-12 dataGroup text-right">
              <label class="datalbl">Opening Amount :</label>
              <span>
                <!-- {{ currency }}
                {{ customerLedgerData.customerLedgerInfoPojo.openingAmount | number: "1.2-2" }} -->
                {{
                  customerLedgerData.customerLedgerInfoPojo.openingAmount
                    | currency: currency : "symbol" : "1.2-2"
                }}
              </span>
            </div>
            <div class="col-lg-12 col-sm-12 dataGroup text-right">
              <label class="datalbl">Closing Balance :</label>
              <span>
                <!-- {{ currency }}
                {{ customerLedgerData.customerLedgerInfoPojo.closingBalance | number: "1.2-2" }} -->
                {{
                  customerLedgerData.customerLedgerInfoPojo.closingBalance
                    | currency: currency : "symbol" : "1.2-2"
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
