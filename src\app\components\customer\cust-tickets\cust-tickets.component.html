<div class="row">
  <div class="col-md-12 col-sm-12">
    <div class="panel">
      <div class="panel-heading">
        <div class="displayflex">
          <button
            (click)="customerDetailOpen()"
            class="btn btn-secondary backbtn"
            data-placement="bottom"
            data-toggle="tooltip"
            title="Go to Customer Details"
            type="button"
          >
            <i
              class="fa fa-arrow-circle-left"
              style="color: #f7b206 !important; font-size: 28px"
            ></i>
          </button>
          <h3 class="panel-title">
            {{ custData.title }}
            {{ custData.firstname }}
            {{ custData.lastname }} Ticket List
          </h3>
        </div>
        <div class="right">
          <button class="btn refreshbtn" type="reset" (click)="getcustTicket(this.custData.id, '')">
            <i class="fa fa-refresh"></i>
          </button>
          <button
            aria-controls="TicketListPRECUST"
            aria-expanded="false"
            class="btn-toggle-collapse"
            data-target="#TicketListPRECUST"
            data-toggle="collapse"
            type="button"
          >
            <i class="fa fa-minus-circle"></i>
          </button>
        </div>
      </div>

      <div class="panel-collapse collapse in" id="TicketListPRECUST">
        <div class="panel-body table-responsive">
          <div *ngIf="createTicketAccess">
            <button
              [disabled]="custData.status === 'Terminate'"
              [routerLink]="['/home/<USER>']"
              [state]="{ data: this.custData }"
              class="btn btn-primary"
              id="submit"
              type="submit"
            >
              <i class="fa fa-check-circle"></i>
              Add Ticket
            </button>
            <br />
          </div>
          <!-- <label>Please nagivate to Ticket Management in order to create new Ticket.</label> -->
          <div class="row">
            <div *ngIf="custTicketList.length !== 0" class="col-lg-12 col-md-12">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Number</th>
                    <th>Type</th>
                    <th>Assignee</th>
                    <th>Status</th>
                    <th>Created Date & Time</th>
                    <th>Last modified Date & Time</th>
                    <th>Followup Date & Time</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let ticket of custTicketList
                        | paginate
                          : {
                              id: 'ticketpageData',
                              itemsPerPage: itemsPerPage,
                              currentPage: currentPage,
                              totalItems: totalRecords
                            };
                      index as i
                    "
                  >
                    <td>{{ ticket.caseTitle }}</td>
                    <td>{{ ticket.caseNumber }}</td>
                    <td>{{ ticket.caseType }}</td>
                    <td *ngIf="ticket.currentAssigneeId">
                      <a
                        (click)="openStaffDetailModal(ticket.currentAssigneeId)"
                        href="javascript:void(0)"
                        style="color: #f7b206"
                      >
                        {{ ticket.currentAssigneeName }}
                      </a>
                    </td>
                    <td *ngIf="!ticket.currentAssigneeId">
                      {{ ticket.currentAssigneeName }}
                    </td>
                    <td>
                      <div *ngIf="ticket.caseStatus == 'Open'">
                        <span class="badge badge-success">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Closed'">
                        <span class="badge badge-danger">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'rejected'">
                        <span class="badge badge-danger">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Pending'">
                        <span class="badge badge-info">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Re-open'">
                        <span class="badge badge-info">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'In Progress'">
                        <span class="badge badge-success">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Resolved'">
                        <span class="badge badge-success">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'On Hold'">
                        <span class="badge badge-info">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Completed'">
                        <span class="badge badge-success">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Out of domain'">
                        <span class="badge badge-info">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Raise and Close'">
                        <span class="badge badge-info">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                      <div *ngIf="ticket.caseStatus == 'Follow Up'">
                        <span class="badge badge-info">
                          {{ ticket.caseStatus }}
                        </span>
                      </div>
                    </td>
                    <td>
                      {{ ticket.createdate }}
                    </td>
                    <td>
                      {{ ticket.updatedate }}
                    </td>
                    <td>
                      {{
                        ticket.nextFollowupDate + " " + ticket.nextFollowupTime
                          | date: "dd-MM-yyyy hh:mm a"
                      }}
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="pagination_Dropdown">
                <pagination-controls
                  (pageChange)="pageChangedTicketConfig($event)"
                  directionLinks="true"
                  id="ticketpageData"
                  maxSize="10"
                  nextLabel=""
                  previousLabel=""
                ></pagination-controls>
                <div id="itemPerPageDropdown">
                  <p-dropdown
                    (onChange)="TotalTicketItemPerPage($event)"
                    [options]="pageLimitOptions"
                    optionLabel="value"
                    optionValue="value"
                  ></p-dropdown>
                </div>
              </div>
            </div>
            <!-- <div *ngIf="custTicketList.length === 0" class="col-lg-12 col-md-12">
              Please nagivate to Ticket Management in order to create new Ticket.
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="staffDetailModal"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Staff Details</h4>
      </div>
      <div class="modal-body">
        <fieldset style="margin-top: 0rem; margin-bottom: 2rem">
          <legend>Basic Details</legend>
          <div class="boxWhite">
            <div class="row">
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Name :</label>
                <span>{{ staffData.fullName }}</span>
              </div>
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Email :</label>
                <span>{{ staffData.email }}</span>
              </div>
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Phone :</label>
                <span>{{ staffData.phone }}</span>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Username :</label>
                <span>{{ staffData.username }}</span>
              </div>
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Role Name :</label>
                <span>{{ staffData.roleName[0] }}</span>
              </div>
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Service Area :</label>
                <a
                  data-target="#serviceAreaDetail"
                  (click)="onClickServiceArea()"
                  href="javascript:void(0)"
                  style="color: blue"
                  >click here</a
                >
              </div>
              <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup">
                <label class="datalbl">Parent Staff Name :</label>
                <span>{{ staffData.parentstaffname }}</span>
              </div>
            </div>
          </div>
          <fieldset class="boxWhite">
            <legend>Team List :</legend>
            <div class="row">
              <div
                class="col-lg-4 col-md-4 col-sm-6 col-xs-12 dataGroup"
                *ngFor="let data of staffData.teamNameList"
              >
                <span>{{ data }}</span>
              </div>
            </div>
          </fieldset>
        </fieldset>
      </div>
      <div class="modal-footer">
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>
<div
  aria-labelledby="myModalLabel"
  class="modal fade"
  id="serviceAreaDetail"
  role="dialog"
  tabindex="-1"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="myModalLabel" style="color: #fff !important">Service Area</h4>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <table>
              <tr *ngFor="let data of serviceAreaList">
                {{
                  data
                }}
              </tr>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-default" data-dismiss="modal" type="button">Close</button>
      </div>
    </div>
  </div>
</div>
